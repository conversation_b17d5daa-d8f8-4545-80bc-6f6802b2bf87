#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本 - 运行侨批网络仿真演示
Quick Demo Script - Run Qiaopi Network Simulation Demo
"""

import os
import sys
import json
from datetime import datetime

def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        "demo_results",
        "test_results",
        "simulation_results",
        "logs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ 创建目录: {directory}")

def run_demo():
    """运行演示"""
    print("=" * 60)
    print("侨批网络智能体仿真系统 - 快速演示")
    print("Qiaopi Network Agent-Based Simulation - Quick Demo")
    print("=" * 60)
    print()
    
    # 确保目录存在
    ensure_directories()
    
    try:
        # 导入必要的模块
        print("加载模块...")
        from simulation_engine import SimulationConfig, QiaopiSimulationEngine
        from data_integration import create_sample_dataset
        print("✓ 模块加载成功")
        print()
        
        # 创建配置
        print("创建仿真配置...")
        config = SimulationConfig(
            start_year=1920,
            end_year=1925,  # 5年的短期仿真
            steps_per_year=12,
            num_migrants=50,     # 少量智能体以加快速度
            num_families=50,
            num_institutions=5,
            output_directory="demo_results",
            save_statistics_interval=6  # 每半年保存一次
        )
        print(f"✓ 配置创建成功")
        print(f"  - 时间范围: {config.start_year}-{config.end_year}")
        print(f"  - 智能体数量: 移民={config.num_migrants}, 家庭={config.num_families}, 机构={config.num_institutions}")
        print()
        
        # 创建仿真引擎
        print("初始化仿真引擎...")
        simulation = QiaopiSimulationEngine(config)
        print("✓ 仿真引擎初始化成功")
        print()
        
        # 运行仿真
        print("开始运行仿真...")
        print("（这可能需要几分钟时间...）")
        print()
        
        results = simulation.run_simulation()
        
        print()
        print("=" * 60)
        print("仿真结果")
        print("=" * 60)
        
        # 显示结果摘要
        if 'simulation_summary' in results:
            summary = results['simulation_summary']
            print(f"✓ 仿真完成")
            print(f"  - 总步数: {summary.get('total_steps', 'N/A')}")
            print(f"  - 智能体总数: {summary.get('total_agents', 'N/A')}")
        
        if 'qiaopi_summary' in results:
            qiaopi = results['qiaopi_summary']
            total = qiaopi.get('total_qiaopi_processed', 0)
            success = qiaopi.get('successful_remittances', 0)
            if total > 0:
                success_rate = success / total * 100
                print(f"  - 侨批处理: {total} 封")
                print(f"  - 成功递送: {success} 封 ({success_rate:.1f}%)")
        
        if 'final_statistics' in results:
            stats = results['final_statistics']
            print(f"  - 移民总储蓄: {stats.get('total_migrant_savings', 0):.2f}")
            print(f"  - 家庭总现金: {stats.get('total_family_cash', 0):.2f}")
        
        # 保存结果
        output_file = f"demo_results/demo_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print()
        print(f"✓ 结果已保存到: {output_file}")
        
        # 导出数据
        print()
        print("导出数据...")
        simulation.export_data("csv")
        simulation.export_data("json")
        print("✓ 数据导出完成")
        
        print()
        print("=" * 60)
        print("演示完成！")
        print("=" * 60)
        print()
        print("查看结果:")
        print(f"  - 统计数据: {config.output_directory}/")
        print(f"  - CSV数据: {config.output_directory}/migrants.csv, families.csv, qiaopi.csv")
        print(f"  - JSON数据: {config.output_directory}/simulation_data.json")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        print("请确保所有依赖模块都已正确安装")
        return False
        
    except Exception as e:
        print(f"✗ 仿真运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = run_demo()
    
    if success:
        print("\n✅ 演示成功完成！")
        return 0
    else:
        print("\n❌ 演示失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())