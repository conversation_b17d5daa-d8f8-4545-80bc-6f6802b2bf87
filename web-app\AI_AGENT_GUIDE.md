# AI智能体设计器使用指南

## 🤖 概述

本系统提供了强大的AI智能体设计和配置功能，让你可以轻松创建复杂的侨批网络仿真智能体。

## 🚀 快速开始

### 1. 访问智能体设计器

```
主仪表盘: http://localhost:3508
AI智能体页面: http://localhost:3508/#agents  
简单设计器: http://localhost:3508/agent-designer.html
高级设计器: http://localhost:3508/advanced-agent-designer.html
```

### 2. 智能体类型

系统支持以下5种智能体类型：

#### 🏃‍♂️ 海外移民智能体
- **功能**: 模拟海外华侨的汇款行为和决策过程
- **特点**: 强化学习、风险评估、宗族网络连接
- **适用场景**: 研究移民汇款模式、风险管理策略

#### 🏠 家乡家庭智能体  
- **功能**: 模拟接收汇款的家庭成员行为模式
- **特点**: 需求评估、资源分配、社会网络维护
- **适用场景**: 研究家庭接收行为、资源利用模式

#### 🏢 金融机构智能体
- **功能**: 模拟银庄、批局等金融中介机构
- **特点**: 风险管控、信用评估、网络优化
- **适用场景**: 研究金融中介作用、网络效率优化

#### 🏛️ 政策制定者智能体
- **功能**: 模拟政府政策对网络的影响
- **特点**: 政策分析、宏观调控、监管决策
- **适用场景**: 研究政策影响、监管效果分析

#### 🤝 商人智能体
- **功能**: 模拟商业网络中的交易行为
- **特点**: 市场分析、交易优化、信息维护
- **适用场景**: 研究商业网络、贸易模式分析

## 🧠 AI模型配置

### 强化学习 (Reinforcement Learning)
- **算法**: Q-Learning, DQN, PPO, A3C, SARSA
- **探索策略**: ε-贪婪, UCB, Thompson采样, Softmax
- **参数**: 学习率(0.0001-0.1), 折扣因子(0.1-1.0), 探索率(0-1.0)
- **适用**: 动态环境决策、策略优化

### 深度神经网络 (Deep Neural Network)
- **架构**: 可配置隐藏层数(1-10层)和神经元数(16-512个)
- **激活函数**: ReLU, Tanh, Sigmoid, Leaky ReLU
- **正则化**: Dropout(0-0.8)
- **适用**: 复杂模式识别、非线性关系建模

### 基于规则系统 (Rule-based System)
- **特点**: 确定性决策、高可解释性、易于调试
- **配置**: 规则优先级、决策树深度
- **适用**: 明确规则场景、可解释性要求高的应用

### 混合模型 (Hybrid Model)
- **特点**: 结合规则和学习的优势
- **配置**: 规则权重、学习权重、切换条件
- **适用**: 平衡性能与可解释性

## ⚙️ 高级配置选项

### 行为参数
- **风险容忍度** (0-1): 控制智能体对风险的接受程度
- **合作倾向** (0-1): 与其他智能体合作的意愿
- **适应性** (0-1): 适应环境变化的能力
- **决策速度** (0.1-1): 做出决策的速度
- **记忆强度** (0.1-1): 记住历史经验的能力
- **社会影响力** (0-1): 对其他智能体的影响程度

### 学习配置
- **学习率** (0.001-0.1): 学习新信息的速度
- **经验权重** (0-1): 过往经验在决策中的权重
- **遗忘因子** (0-0.5): 遗忘旧信息的速度
- **探索率** (0-0.5): 探索新策略的倾向

### 记忆系统
- **容量** (10-500): 可存储的记忆项目数量
- **情节记忆**: 具体事件的记忆
- **语义记忆**: 概念和知识的记忆  
- **程序性记忆**: 技能和程序的记忆

### 社交特征
- **信任水平** (0-1): 对其他智能体的基础信任程度
- **网络规模** (5-100): 可维护的社交连接数量
- **影响半径** (1-50): 影响其他智能体的范围
- **声誉系统**: 启用声誉评价机制
- **宗族连接**: 启用基于血缘关系的特殊连接

## 📊 性能优化建议

### 计算性能
- 网络规模建议不超过50个连接
- 神经网络隐藏层建议不超过5层
- 记忆容量根据可用内存调整

### 学习效率
- 学习率: 0.001-0.01为最佳范围
- 探索率: 初始0.1-0.3，逐渐衰减
- 批次大小: 16-64为推荐值

### 内存使用
- 基础智能体: ~10MB
- 强化学习智能体: ~20-30MB  
- 深度神经网络智能体: ~50-100MB
- 大规模社交网络: 额外+20-50MB

## 🛠️ 使用流程

### 简单设计器流程
1. 选择智能体类型
2. 配置基础参数
3. 设置学习和记忆选项
4. 配置社交特征
5. 预览并创建

### 高级设计器流程  
1. **选择类型**: 从5种预设类型中选择
2. **AI模型**: 配置具体的AI算法和参数
3. **行为配置**: 调整行为模式和决策参数
4. **高级设置**: 配置数据处理、通信、监控等
5. **预览&创建**: 查看配置摘要并生成智能体

## 🧪 测试功能

### 测试场景
- **基础行为测试**: 验证基本功能
- **压力测试**: 高负载环境下的表现
- **学习能力测试**: AI学习算法效果
- **社交互动测试**: 与其他智能体的交互能力

### 测试指标
- **成功率**: 任务完成的成功比例
- **决策时间**: 平均决策耗时
- **学习进度**: AI模型的学习效果
- **社交互动**: 与其他智能体的互动频率
- **内存使用**: 系统资源消耗
- **错误率**: 运行过程中的错误频率

## 🔧 故障排除

### 常见问题

**Q: 智能体创建失败**
A: 检查必填字段(类型、名称)是否完整，参数范围是否正确

**Q: Python集成失败**
A: 确认Python3已安装，且项目依赖包已正确安装

**Q: 仿真运行缓慢**
A: 减少智能体数量，简化AI模型，或减少网络连接数

**Q: 内存不足**
A: 减少记忆容量，使用更简单的AI模型，或减少并发智能体数

### 性能调优
1. **选择合适的AI模型**: 根据需求选择复杂度适中的模型
2. **优化网络规模**: 大网络会显著影响性能
3. **调整学习参数**: 过高的学习率可能导致不稳定
4. **合理设置记忆容量**: 避免内存过度消耗

## 📝 最佳实践

### 智能体设计
1. **明确目标**: 确定智能体要解决的具体问题
2. **渐进配置**: 从简单配置开始，逐步增加复杂度
3. **测试验证**: 每次配置变更后进行测试
4. **文档记录**: 记录配置选择的原因和效果

### 仿真运行
1. **小规模测试**: 先用少量智能体测试配置正确性
2. **参数扫描**: 系统性地测试不同参数组合
3. **结果分析**: 详细分析仿真结果，理解智能体行为
4. **迭代优化**: 根据结果反馈优化配置

## 🔄 更新日志

### 版本 2.0 (2025-08-28)
- ✅ 新增AI智能体设计器界面
- ✅ 支持5种智能体类型配置
- ✅ 集成强化学习和深度学习配置
- ✅ 添加智能体测试功能
- ✅ 实现Python后端集成
- ✅ 提供高级AI模型配置选项

## 📞 技术支持

如需技术支持或有功能建议，请：
1. 查看控制台错误信息
2. 检查浏览器开发者工具的网络请求
3. 确认Python后端服务状态
4. 联系开发团队获取帮助

---

**注意**: 本系统还在持续开发中，部分高级功能可能需要额外配置。建议在生产环境使用前进行充分测试。