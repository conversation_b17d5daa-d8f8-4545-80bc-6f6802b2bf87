// 简单测试服务器语法
console.log('Testing server.js syntax...');

try {
    const server = require('./web-app/server.js');
    console.log('✅ Server.js syntax is valid');
    console.log('✅ Server module loaded successfully');
    
    // 测试基本功能
    console.log('🔧 Testing basic functionality...');
    
    // 检查必需模块
    const requiredModules = ['express', 'cors', 'fs-extra', 'body-parser'];
    requiredModules.forEach(module => {
        try {
            require(module);
            console.log(`✅ ${module} is available`);
        } catch (e) {
            console.log(`❌ ${module} is missing: ${e.message}`);
        }
    });
    
    console.log('\n🎉 Server test completed successfully!');
    console.log('\n🚀 You can now start the server with:');
    console.log('   cd web-app && node server.js');
    console.log('\n🌐 Then visit:');
    console.log('   📊 Main Dashboard: http://localhost:3508');
    console.log('   🤖 AI Agent Designer: http://localhost:3508/agent-designer.html');
    console.log('   🧠 Advanced Designer: http://localhost:3508/advanced-agent-designer.html');
    
} catch (error) {
    console.log('❌ Server.js has errors:');
    console.log(error.message);
    console.log('\n🔧 Please check the server.js file for syntax errors.');
}