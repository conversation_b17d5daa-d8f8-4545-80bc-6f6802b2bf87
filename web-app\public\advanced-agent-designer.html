<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级AI智能体设计器 - 侨批网络仿真</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #16a085;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --border-radius: 12px;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--dark-color);
        }

        .designer-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            margin: 20px auto;
            padding: 30px;
            box-shadow: var(--box-shadow);
            max-width: 1400px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 15px;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
            transition: var(--transition);
        }

        .step.active .step-circle {
            background: var(--primary-color);
            color: white;
        }

        .step.completed .step-circle {
            background: var(--success-color);
            color: white;
        }

        .step-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .step-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .config-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border-left: 4px solid var(--secondary-color);
        }

        .ai-model-card {
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 20px;
            cursor: pointer;
            transition: var(--transition);
            height: 100%;
        }

        .ai-model-card:hover {
            border-color: var(--secondary-color);
            transform: translateY(-5px);
            box-shadow: var(--box-shadow);
        }

        .ai-model-card.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .parameter-slider {
            margin: 15px 0;
        }

        .slider-container {
            position: relative;
            margin: 10px 0;
        }

        .custom-range {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e9ecef;
            outline: none;
            -webkit-appearance: none;
        }

        .custom-range::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .neural-network-config {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 25px;
            margin: 20px 0;
        }

        .rl-config {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 25px;
            margin: 20px 0;
        }

        .behavior-pattern {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: var(--transition);
        }

        .behavior-pattern:hover {
            border-color: var(--secondary-color);
        }

        .behavior-pattern.selected {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.1);
        }

        .agent-visualization {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            box-shadow: var(--box-shadow);
            position: sticky;
            top: 20px;
        }

        .agent-avatar-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 3rem;
            box-shadow: var(--box-shadow);
        }

        .progress-ring {
            margin: 10px;
        }

        .neural-layer {
            display: flex;
            justify-content: center;
            margin: 10px 0;
        }

        .neuron {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--secondary-color);
            margin: 0 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .btn-wizard-nav {
            min-width: 120px;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
        }

        .complexity-indicator {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }

        .complexity-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
            background: #e9ecef;
        }

        .complexity-dot.filled {
            background: var(--warning-color);
        }

        .advanced-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius);
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain"></i> 高级AI智能体设计器
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/agent-designer.html">
                    <i class="fas fa-robot"></i> 简单设计器
                </a>
                <a class="nav-link" href="/">
                    <i class="fas fa-home"></i> 返回主页
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container">
        <div class="designer-container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step1">
                    <div class="step-circle">1</div>
                    <div>选择类型</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div>AI模型</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div>行为配置</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div>高级设置</div>
                </div>
                <div class="step" id="step5">
                    <div class="step-circle">5</div>
                    <div>预览&创建</div>
                </div>
            </div>

            <!-- Step 1: Agent Type Selection -->
            <div class="step-content active" id="step1Content">
                <div class="row">
                    <div class="col-lg-8">
                        <h3><i class="fas fa-layer-group me-2"></i>选择智能体类型</h3>
                        <p class="text-muted mb-4">不同类型的智能体有不同的基础行为模式和决策机制</p>
                        
                        <div class="row" id="agentTypesGrid">
                            <!-- 动态生成智能体类型卡片 -->
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="agent-visualization">
                            <div class="agent-avatar-large" id="previewAvatar">
                                <i class="fas fa-question"></i>
                            </div>
                            <h5 id="previewTypeName">选择智能体类型</h5>
                            <p class="text-muted" id="previewTypeDesc">请从左侧选择一个智能体类型</p>
                            
                            <div class="complexity-indicator">
                                <span class="me-2">复杂度:</span>
                                <div id="complexityDots"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: AI Model Selection -->
            <div class="step-content" id="step2Content">
                <div class="row">
                    <div class="col-lg-8">
                        <h3><i class="fas fa-brain me-2"></i>AI模型配置</h3>
                        <p class="text-muted mb-4">选择智能体的AI模型类型和学习算法</p>
                        
                        <div class="row">
                            <!-- 强化学习 -->
                            <div class="col-md-6 mb-3">
                                <div class="ai-model-card" data-model="reinforcement_learning" onclick="selectAIModel('reinforcement_learning')">
                                    <div class="text-center mb-3">
                                        <i class="fas fa-trophy fa-2x text-warning"></i>
                                    </div>
                                    <h5>强化学习 (RL)</h5>
                                    <p class="text-muted small">通过试错和奖励信号学习最优行为策略</p>
                                    <div class="complexity-indicator">
                                        <span class="me-2">复杂度:</span>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot"></div>
                                    </div>
                                    <ul class="list-unstyled small mt-3">
                                        <li><i class="fas fa-check text-success me-1"></i> Q-Learning算法</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 策略梯度方法</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 动态适应环境</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 深度神经网络 -->
                            <div class="col-md-6 mb-3">
                                <div class="ai-model-card" data-model="deep_neural" onclick="selectAIModel('deep_neural')">
                                    <div class="text-center mb-3">
                                        <i class="fas fa-project-diagram fa-2x text-info"></i>
                                    </div>
                                    <h5>深度神经网络</h5>
                                    <p class="text-muted small">使用多层神经网络处理复杂的决策问题</p>
                                    <div class="complexity-indicator">
                                        <span class="me-2">复杂度:</span>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                    </div>
                                    <ul class="list-unstyled small mt-3">
                                        <li><i class="fas fa-check text-success me-1"></i> 多层感知机</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 反向传播算法</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 非线性映射</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 基于规则 -->
                            <div class="col-md-6 mb-3">
                                <div class="ai-model-card" data-model="rule_based" onclick="selectAIModel('rule_based')">
                                    <div class="text-center mb-3">
                                        <i class="fas fa-list-ol fa-2x text-primary"></i>
                                    </div>
                                    <h5>基于规则系统</h5>
                                    <p class="text-muted small">使用预定义规则和逻辑进行决策</p>
                                    <div class="complexity-indicator">
                                        <span class="me-2">复杂度:</span>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot"></div>
                                        <div class="complexity-dot"></div>
                                        <div class="complexity-dot"></div>
                                    </div>
                                    <ul class="list-unstyled small mt-3">
                                        <li><i class="fas fa-check text-success me-1"></i> 确定性决策</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 可解释性强</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 易于调试</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 混合模型 -->
                            <div class="col-md-6 mb-3">
                                <div class="ai-model-card" data-model="hybrid" onclick="selectAIModel('hybrid')">
                                    <div class="text-center mb-3">
                                        <i class="fas fa-puzzle-piece fa-2x text-success"></i>
                                    </div>
                                    <h5>混合模型</h5>
                                    <p class="text-muted small">结合多种AI技术的混合决策系统</p>
                                    <div class="complexity-indicator">
                                        <span class="me-2">复杂度:</span>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot filled"></div>
                                        <div class="complexity-dot"></div>
                                        <div class="complexity-dot"></div>
                                    </div>
                                    <ul class="list-unstyled small mt-3">
                                        <li><i class="fas fa-check text-success me-1"></i> 规则+学习</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 灵活适应</li>
                                        <li><i class="fas fa-check text-success me-1"></i> 平衡性能</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- AI Model Specific Configuration -->
                        <div id="aiModelConfig" style="display: none;">
                            <!-- 强化学习配置 -->
                            <div id="rlConfig" class="rl-config" style="display: none;">
                                <h5><i class="fas fa-cog me-2"></i>强化学习配置</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="rlAlgorithm" class="form-label">RL算法</label>
                                            <select class="form-select" id="rlAlgorithm">
                                                <option value="q_learning">Q-Learning</option>
                                                <option value="dqn">Deep Q-Network</option>
                                                <option value="ppo">PPO (Proximal Policy Optimization)</option>
                                                <option value="a3c">A3C (Actor-Critic)</option>
                                                <option value="sarsa">SARSA</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="explorationStrategy" class="form-label">探索策略</label>
                                            <select class="form-select" id="explorationStrategy">
                                                <option value="epsilon_greedy">ε-贪婪策略</option>
                                                <option value="ucb">UCB (置信上界)</option>
                                                <option value="thompson">Thompson采样</option>
                                                <option value="softmax">Softmax探索</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="learningRate" class="form-label">
                                                学习率 <span class="badge bg-light text-dark" id="learningRateValue">0.001</span>
                                            </label>
                                            <input type="range" class="custom-range" id="learningRate" min="0.0001" max="0.1" step="0.0001" value="0.001" oninput="updateAdvancedValue('learningRate')">
                                        </div>
                                        <div class="mb-3">
                                            <label for="discountFactor" class="form-label">
                                                折扣因子 <span class="badge bg-light text-dark" id="discountFactorValue">0.95</span>
                                            </label>
                                            <input type="range" class="custom-range" id="discountFactor" min="0.1" max="1" step="0.05" value="0.95" oninput="updateAdvancedValue('discountFactor')">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="advanced-toggle">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableAdvancedRL">
                                        <label class="form-check-label" for="enableAdvancedRL">
                                            <strong>启用高级配置</strong>
                                        </label>
                                    </div>
                                </div>
                                
                                <div id="advancedRLConfig" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="explorationRate" class="form-label">
                                                    探索率 <span class="badge bg-light text-dark" id="explorationRateValue">0.1</span>
                                                </label>
                                                <input type="range" class="custom-range" id="explorationRate" min="0" max="1" step="0.01" value="0.1" oninput="updateAdvancedValue('explorationRate')">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="explorationDecay" class="form-label">
                                                    探索衰减 <span class="badge bg-light text-dark" id="explorationDecayValue">0.995</span>
                                                </label>
                                                <input type="range" class="custom-range" id="explorationDecay" min="0.9" max="1" step="0.001" value="0.995" oninput="updateAdvancedValue('explorationDecay')">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="replayBufferSize" class="form-label">
                                                    经验回放缓冲区 <span class="badge bg-light text-dark" id="replayBufferSizeValue">10000</span>
                                                </label>
                                                <input type="range" class="custom-range" id="replayBufferSize" min="1000" max="50000" step="1000" value="10000" oninput="updateAdvancedValue('replayBufferSize')">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 神经网络配置 -->
                            <div id="neuralConfig" class="neural-network-config" style="display: none;">
                                <h5><i class="fas fa-project-diagram me-2"></i>神经网络架构</h5>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="networkArchitecture" class="form-label">网络架构</label>
                                            <select class="form-select" id="networkArchitecture" onchange="updateNetworkVisualization()">
                                                <option value="simple">简单网络 (3层)</option>
                                                <option value="medium">中等网络 (5层)</option>
                                                <option value="complex">复杂网络 (7层)</option>
                                                <option value="custom">自定义</option>
                                            </select>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="hiddenLayers" class="form-label">
                                                        隐藏层数 <span class="badge bg-light text-dark" id="hiddenLayersValue">2</span>
                                                    </label>
                                                    <input type="range" class="custom-range" id="hiddenLayers" min="1" max="10" step="1" value="2" oninput="updateAdvancedValue('hiddenLayers'); updateNetworkVisualization()">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="neuronsPerLayer" class="form-label">
                                                        每层神经元 <span class="badge bg-light text-dark" id="neuronsPerLayerValue">64</span>
                                                    </label>
                                                    <input type="range" class="custom-range" id="neuronsPerLayer" min="16" max="512" step="16" value="64" oninput="updateAdvancedValue('neuronsPerLayer'); updateNetworkVisualization()">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="activationFunction" class="form-label">激活函数</label>
                                                    <select class="form-select" id="activationFunction">
                                                        <option value="relu">ReLU</option>
                                                        <option value="tanh">Tanh</option>
                                                        <option value="sigmoid">Sigmoid</option>
                                                        <option value="leaky_relu">Leaky ReLU</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="dropoutRate" class="form-label">
                                                        Dropout率 <span class="badge bg-light text-dark" id="dropoutRateValue">0.2</span>
                                                    </label>
                                                    <input type="range" class="custom-range" id="dropoutRate" min="0" max="0.8" step="0.1" value="0.2" oninput="updateAdvancedValue('dropoutRate')">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <h6>网络结构预览</h6>
                                            <div id="networkVisualization">
                                                <!-- 动态生成神经网络可视化 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="agent-visualization">
                            <div class="agent-avatar-large">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h5>AI模型预览</h5>
                            <p class="text-muted" id="aiModelDescription">选择AI模型查看详情</p>
                            
                            <div id="modelPerformance" class="mt-3" style="display: none;">
                                <h6>预期性能</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="h5 text-primary" id="performanceLearning">85%</div>
                                        <small class="text-muted">学习效率</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="h5 text-success" id="performanceSpeed">90%</div>
                                        <small class="text-muted">决策速度</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="h5 text-warning" id="performanceAccuracy">80%</div>
                                        <small class="text-muted">决策准确性</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Behavior Configuration -->
            <div class="step-content" id="step3Content">
                <h3><i class="fas fa-user-cog me-2"></i>行为模式配置</h3>
                <p class="text-muted mb-4">配置智能体的行为参数和决策特征</p>
                
                <div class="row">
                    <div class="col-lg-8">
                        <!-- 预设行为模式 -->
                        <div class="config-section">
                            <h5><i class="fas fa-palette me-2"></i>预设行为模式</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="behavior-pattern" data-pattern="conservative" onclick="selectBehaviorPattern('conservative')">
                                        <h6><i class="fas fa-shield-alt me-2"></i>保守型</h6>
                                        <p class="small text-muted">低风险、高稳定性</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="behavior-pattern" data-pattern="balanced" onclick="selectBehaviorPattern('balanced')">
                                        <h6><i class="fas fa-balance-scale me-2"></i>平衡型</h6>
                                        <p class="small text-muted">风险与收益平衡</p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="behavior-pattern" data-pattern="aggressive" onclick="selectBehaviorPattern('aggressive')">
                                        <h6><i class="fas fa-rocket me-2"></i>激进型</h6>
                                        <p class="small text-muted">高风险、高收益</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细参数配置 -->
                        <div class="config-section">
                            <h5><i class="fas fa-sliders-h me-2"></i>详细参数</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="parameter-slider">
                                        <label for="riskTolerance" class="form-label">
                                            风险容忍度 <span class="badge bg-primary" id="riskToleranceValue">0.5</span>
                                        </label>
                                        <input type="range" class="custom-range" id="riskTolerance" min="0" max="1" step="0.01" value="0.5" oninput="updateAdvancedValue('riskTolerance'); updateBehaviorPreview()">
                                        <div class="d-flex justify-content-between small text-muted">
                                            <span>保守</span>
                                            <span>激进</span>
                                        </div>
                                    </div>
                                    
                                    <div class="parameter-slider">
                                        <label for="cooperativeness" class="form-label">
                                            合作倾向 <span class="badge bg-success" id="cooperativenessValue">0.7</span>
                                        </label>
                                        <input type="range" class="custom-range" id="cooperativeness" min="0" max="1" step="0.01" value="0.7" oninput="updateAdvancedValue('cooperativeness'); updateBehaviorPreview()">
                                        <div class="d-flex justify-content-between small text-muted">
                                            <span>独立</span>
                                            <span>合作</span>
                                        </div>
                                    </div>
                                    
                                    <div class="parameter-slider">
                                        <label for="adaptability" class="form-label">
                                            适应性 <span class="badge bg-info" id="adaptabilityValue">0.6</span>
                                        </label>
                                        <input type="range" class="custom-range" id="adaptability" min="0" max="1" step="0.01" value="0.6" oninput="updateAdvancedValue('adaptability'); updateBehaviorPreview()">
                                        <div class="d-flex justify-content-between small text-muted">
                                            <span>固定</span>
                                            <span>灵活</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="parameter-slider">
                                        <label for="decisionSpeed" class="form-label">
                                            决策速度 <span class="badge bg-warning" id="decisionSpeedValue">0.8</span>
                                        </label>
                                        <input type="range" class="custom-range" id="decisionSpeed" min="0.1" max="1" step="0.01" value="0.8" oninput="updateAdvancedValue('decisionSpeed'); updateBehaviorPreview()">
                                        <div class="d-flex justify-content-between small text-muted">
                                            <span>慎重</span>
                                            <span>快速</span>
                                        </div>
                                    </div>
                                    
                                    <div class="parameter-slider">
                                        <label for="memoryStrength" class="form-label">
                                            记忆强度 <span class="badge bg-secondary" id="memoryStrengthValue">0.7</span>
                                        </label>
                                        <input type="range" class="custom-range" id="memoryStrength" min="0.1" max="1" step="0.01" value="0.7" oninput="updateAdvancedValue('memoryStrength'); updateBehaviorPreview()">
                                        <div class="d-flex justify-content-between small text-muted">
                                            <span>健忘</span>
                                            <span>记忆深刻</span>
                                        </div>
                                    </div>
                                    
                                    <div class="parameter-slider">
                                        <label for="socialInfluence" class="form-label">
                                            社会影响力 <span class="badge bg-danger" id="socialInfluenceValue">0.5</span>
                                        </label>
                                        <input type="range" class="custom-range" id="socialInfluence" min="0" max="1" step="0.01" value="0.5" oninput="updateAdvancedValue('socialInfluence'); updateBehaviorPreview()">
                                        <div class="d-flex justify-content-between small text-muted">
                                            <span>被动</span>
                                            <span>主导</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="agent-visualization">
                            <div class="agent-avatar-large" id="behaviorAvatar">
                                <i class="fas fa-user-cog"></i>
                            </div>
                            <h5>行为特征</h5>
                            <div id="behaviorPreview">
                                <div class="mt-3">
                                    <canvas id="radarChart" width="200" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Advanced Settings -->
            <div class="step-content" id="step4Content">
                <h3><i class="fas fa-cogs me-2"></i>高级设置</h3>
                <p class="text-muted mb-4">配置智能体的高级功能和专业特性</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="config-section">
                            <h5><i class="fas fa-database me-2"></i>数据处理</h5>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableDataMining" checked>
                                    <label class="form-check-label" for="enableDataMining">
                                        启用数据挖掘
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enablePatternRecognition" checked>
                                    <label class="form-check-label" for="enablePatternRecognition">
                                        启用模式识别
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enablePredictiveAnalysis">
                                    <label class="form-check-label" for="enablePredictiveAnalysis">
                                        启用预测分析
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h5><i class="fas fa-comments me-2"></i>通信能力</h5>
                            <div class="mb-3">
                                <label for="communicationRange" class="form-label">
                                    通信范围 <span class="badge bg-primary" id="communicationRangeValue">50</span>
                                </label>
                                <input type="range" class="custom-range" id="communicationRange" min="10" max="200" step="10" value="50" oninput="updateAdvancedValue('communicationRange')">
                            </div>
                            <div class="mb-3">
                                <label for="languageSkills" class="form-label">语言技能</label>
                                <select class="form-select" id="languageSkills" multiple>
                                    <option value="chinese">中文</option>
                                    <option value="english">英文</option>
                                    <option value="malay">马来文</option>
                                    <option value="thai">泰文</option>
                                    <option value="vietnamese">越南文</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="config-section">
                            <h5><i class="fas fa-chart-line me-2"></i>性能监控</h5>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enablePerformanceLogging" checked>
                                    <label class="form-check-label" for="enablePerformanceLogging">
                                        启用性能日志
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableDecisionTrace">
                                    <label class="form-check-label" for="enableDecisionTrace">
                                        启用决策追踪
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="loggingLevel" class="form-label">日志级别</label>
                                <select class="form-select" id="loggingLevel">
                                    <option value="basic">基础</option>
                                    <option value="detailed" selected>详细</option>
                                    <option value="debug">调试</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h5><i class="fas fa-shield-alt me-2"></i>安全设置</h5>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableSafeguards" checked>
                                    <label class="form-check-label" for="enableSafeguards">
                                        启用安全防护
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="maxIterations" class="form-label">
                                    最大迭代次数 <span class="badge bg-warning" id="maxIterationsValue">1000</span>
                                </label>
                                <input type="range" class="custom-range" id="maxIterations" min="100" max="10000" step="100" value="1000" oninput="updateAdvancedValue('maxIterations')">
                            </div>
                            <div class="mb-3">
                                <label for="timeoutLimit" class="form-label">
                                    超时限制(秒) <span class="badge bg-danger" id="timeoutLimitValue">300</span>
                                </label>
                                <input type="range" class="custom-range" id="timeoutLimit" min="60" max="3600" step="60" value="300" oninput="updateAdvancedValue('timeoutLimit')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 5: Preview & Create -->
            <div class="step-content" id="step5Content">
                <h3><i class="fas fa-eye me-2"></i>智能体预览</h3>
                <p class="text-muted mb-4">检查你的智能体配置并创建</p>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="config-section">
                            <h5><i class="fas fa-file-alt me-2"></i>配置摘要</h5>
                            <div id="configSummary">
                                <!-- 动态生成配置摘要 -->
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h5><i class="fas fa-code me-2"></i>生成的配置代码</h5>
                            <pre id="generatedCode" class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="agent-visualization">
                            <div class="agent-avatar-large" id="finalAvatar">
                                <i class="fas fa-check"></i>
                            </div>
                            <h5 id="finalName">智能体名称</h5>
                            <p class="text-muted" id="finalDescription">智能体描述</p>
                            
                            <div class="mt-4">
                                <button class="btn btn-success w-100 btn-lg" onclick="createFinalAgent()">
                                    <i class="fas fa-rocket me-2"></i>创建智能体
                                </button>
                            </div>
                            
                            <div class="mt-3">
                                <button class="btn btn-outline-primary w-100" onclick="testConfiguration()">
                                    <i class="fas fa-vial me-2"></i>测试配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between mt-4">
                <button id="prevButton" class="btn btn-outline-secondary btn-wizard-nav" onclick="previousStep()" disabled>
                    <i class="fas fa-chevron-left me-2"></i>上一步
                </button>
                <div>
                    <span class="text-muted">第 <span id="currentStepNum">1</span> 步，共 5 步</span>
                </div>
                <button id="nextButton" class="btn btn-primary btn-wizard-nav" onclick="nextStep()" disabled>
                    下一步 <i class="fas fa-chevron-right ms-2"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        let currentStep = 1;
        let agentConfig = {};
        let selectedAIModel = null;
        let selectedBehaviorPattern = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDesigner();
        });

        function initializeDesigner() {
            generateAgentTypes();
            updateStepDisplay();
        }

        // 生成智能体类型
        function generateAgentTypes() {
            const types = [
                {
                    id: 'migrant',
                    name: '海外移民',
                    description: '模拟海外华侨的汇款行为和决策过程',
                    icon: 'fas fa-user-friends',
                    complexity: 3
                },
                {
                    id: 'family',
                    name: '家乡家庭', 
                    description: '模拟接收汇款的家庭成员行为模式',
                    icon: 'fas fa-home',
                    complexity: 2
                },
                {
                    id: 'institution',
                    name: '金融机构',
                    description: '模拟银庄、批局等金融中介机构',
                    icon: 'fas fa-building',
                    complexity: 4
                },
                {
                    id: 'government',
                    name: '政策制定者',
                    description: '模拟政府政策对网络的影响',
                    icon: 'fas fa-landmark',
                    complexity: 5
                },
                {
                    id: 'merchant',
                    name: '商人智能体',
                    description: '模拟商业网络中的交易行为',
                    icon: 'fas fa-handshake',
                    complexity: 3
                }
            ];

            const grid = document.getElementById('agentTypesGrid');
            grid.innerHTML = '';

            types.forEach(type => {
                const col = document.createElement('div');
                col.className = 'col-md-6 col-lg-4 mb-3';
                col.innerHTML = `
                    <div class="ai-model-card" data-type="${type.id}" onclick="selectAgentType('${type.id}')">
                        <div class="text-center mb-3">
                            <i class="${type.icon} fa-3x text-primary"></i>
                        </div>
                        <h5>${type.name}</h5>
                        <p class="text-muted small">${type.description}</p>
                        <div class="complexity-indicator">
                            <span class="me-2">复杂度:</span>
                            ${generateComplexityDots(type.complexity)}
                        </div>
                    </div>
                `;
                grid.appendChild(col);
            });
        }

        function generateComplexityDots(level) {
            let dots = '';
            for (let i = 1; i <= 5; i++) {
                const filled = i <= level ? 'filled' : '';
                dots += `<div class="complexity-dot ${filled}"></div>`;
            }
            return dots;
        }

        // 选择智能体类型
        function selectAgentType(type) {
            agentConfig.type = type;
            
            document.querySelectorAll('[data-type]').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
            
            updatePreviewForType(type);
            enableNextButton();
        }

        // 更新类型预览
        function updatePreviewForType(type) {
            const typeInfo = {
                migrant: { name: '海外移民', icon: 'fas fa-user-friends', desc: '具有汇款行为和风险评估能力' },
                family: { name: '家乡家庭', icon: 'fas fa-home', desc: '具有需求表达和资源分配能力' },
                institution: { name: '金融机构', icon: 'fas fa-building', desc: '具有风险管控和网络优化能力' },
                government: { name: '政策制定者', icon: 'fas fa-landmark', desc: '具有政策分析和宏观调控能力' },
                merchant: { name: '商人智能体', icon: 'fas fa-handshake', desc: '具有市场分析和交易策略能力' }
            };

            const info = typeInfo[type];
            if (info) {
                document.getElementById('previewTypeName').textContent = info.name;
                document.getElementById('previewTypeDesc').textContent = info.desc;
                document.querySelector('#previewAvatar i').className = info.icon;
            }
        }

        // 选择AI模型
        function selectAIModel(model) {
            selectedAIModel = model;
            agentConfig.aiModel = model;
            
            document.querySelectorAll('[data-model]').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-model="${model}"]`).classList.add('selected');
            
            showAIModelConfig(model);
            updateModelPreview(model);
            enableNextButton();
        }

        // 显示AI模型配置
        function showAIModelConfig(model) {
            document.getElementById('aiModelConfig').style.display = 'block';
            
            // 隐藏所有配置
            document.getElementById('rlConfig').style.display = 'none';
            document.getElementById('neuralConfig').style.display = 'none';
            
            // 显示相应配置
            if (model === 'reinforcement_learning') {
                document.getElementById('rlConfig').style.display = 'block';
            } else if (model === 'deep_neural') {
                document.getElementById('neuralConfig').style.display = 'block';
            }
        }

        // 选择行为模式
        function selectBehaviorPattern(pattern) {
            selectedBehaviorPattern = pattern;
            agentConfig.behaviorPattern = pattern;
            
            document.querySelectorAll('[data-pattern]').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-pattern="${pattern}"]`).classList.add('selected');
            
            applyBehaviorPattern(pattern);
            enableNextButton();
        }

        // 应用行为模式
        function applyBehaviorPattern(pattern) {
            const patterns = {
                conservative: {
                    riskTolerance: 0.2,
                    cooperativeness: 0.9,
                    adaptability: 0.4,
                    decisionSpeed: 0.3,
                    memoryStrength: 0.8,
                    socialInfluence: 0.3
                },
                balanced: {
                    riskTolerance: 0.5,
                    cooperativeness: 0.7,
                    adaptability: 0.6,
                    decisionSpeed: 0.6,
                    memoryStrength: 0.6,
                    socialInfluence: 0.5
                },
                aggressive: {
                    riskTolerance: 0.8,
                    cooperativeness: 0.4,
                    adaptability: 0.8,
                    decisionSpeed: 0.9,
                    memoryStrength: 0.4,
                    socialInfluence: 0.8
                }
            };

            const config = patterns[pattern];
            if (config) {
                Object.entries(config).forEach(([key, value]) => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = value;
                        updateAdvancedValue(key);
                    }
                });
                updateBehaviorPreview();
            }
        }

        // 更新高级数值
        function updateAdvancedValue(id) {
            const element = document.getElementById(id);
            const valueSpan = document.getElementById(id + 'Value');
            
            if (element && valueSpan) {
                let value = parseFloat(element.value);
                agentConfig[id] = value;
                
                if (value < 1 && id !== 'maxIterations' && id !== 'timeoutLimit' && id !== 'communicationRange' && id !== 'neuronsPerLayer' && id !== 'hiddenLayers' && id !== 'memoryCapacity' && id !== 'replayBufferSize') {
                    valueSpan.textContent = value.toFixed(3);
                } else {
                    valueSpan.textContent = Math.round(value);
                }
            }
        }

        // 更新行为预览
        function updateBehaviorPreview() {
            // 使用Chart.js创建雷达图
            const ctx = document.getElementById('radarChart');
            if (ctx && window.Chart) {
                if (window.behaviorChart) {
                    window.behaviorChart.destroy();
                }
                
                window.behaviorChart = new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: ['风险容忍', '合作性', '适应性', '决策速度', '记忆力', '影响力'],
                        datasets: [{
                            label: '行为特征',
                            data: [
                                agentConfig.riskTolerance || 0.5,
                                agentConfig.cooperativeness || 0.7,
                                agentConfig.adaptability || 0.6,
                                agentConfig.decisionSpeed || 0.8,
                                agentConfig.memoryStrength || 0.7,
                                agentConfig.socialInfluence || 0.5
                            ],
                            backgroundColor: 'rgba(52, 152, 219, 0.2)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(52, 152, 219, 1)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            r: {
                                beginAtZero: true,
                                max: 1,
                                ticks: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        }

        // 更新网络可视化
        function updateNetworkVisualization() {
            const layers = parseInt(document.getElementById('hiddenLayers').value) + 2; // +输入层+输出层
            const neuronsPerLayer = parseInt(document.getElementById('neuronsPerLayer').value);
            
            const container = document.getElementById('networkVisualization');
            container.innerHTML = '';
            
            for (let i = 0; i < layers; i++) {
                const layer = document.createElement('div');
                layer.className = 'neural-layer';
                
                const neuronCount = i === 0 || i === layers - 1 ? Math.min(neuronsPerLayer / 2, 8) : Math.min(neuronsPerLayer / 4, 6);
                
                for (let j = 0; j < neuronCount; j++) {
                    const neuron = document.createElement('div');
                    neuron.className = 'neuron';
                    neuron.textContent = j + 1;
                    layer.appendChild(neuron);
                }
                
                container.appendChild(layer);
            }
        }

        // 导航函数
        function nextStep() {
            if (currentStep < 5) {
                currentStep++;
                updateStepDisplay();
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // 更新步骤指示器
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                }
            });

            // 更新内容显示
            document.querySelectorAll('.step-content').forEach((content, index) => {
                content.classList.remove('active');
                if (index + 1 === currentStep) {
                    content.classList.add('active');
                }
            });

            // 更新按钮状态
            document.getElementById('prevButton').disabled = currentStep === 1;
            document.getElementById('currentStepNum').textContent = currentStep;

            // 特殊步骤处理
            if (currentStep === 5) {
                generateConfigSummary();
                generateConfigCode();
                updateFinalPreview();
            }
        }

        function enableNextButton() {
            document.getElementById('nextButton').disabled = false;
        }

        // 生成配置摘要
        function generateConfigSummary() {
            const summary = document.getElementById('configSummary');
            summary.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基础信息</h6>
                        <table class="table table-sm">
                            <tr><td>类型</td><td>${agentConfig.type || '未选择'}</td></tr>
                            <tr><td>AI模型</td><td>${selectedAIModel || '未选择'}</td></tr>
                            <tr><td>行为模式</td><td>${selectedBehaviorPattern || '自定义'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>关键参数</h6>
                        <table class="table table-sm">
                            <tr><td>风险容忍度</td><td>${(agentConfig.riskTolerance || 0).toFixed(2)}</td></tr>
                            <tr><td>学习率</td><td>${(agentConfig.learningRate || 0).toFixed(4)}</td></tr>
                            <tr><td>合作倾向</td><td>${(agentConfig.cooperativeness || 0).toFixed(2)}</td></tr>
                        </table>
                    </div>
                </div>
            `;
        }

        // 生成配置代码
        function generateConfigCode() {
            const code = document.getElementById('generatedCode');
            const configObject = {
                agent_type: agentConfig.type,
                ai_model: selectedAIModel,
                behavior_pattern: selectedBehaviorPattern,
                parameters: agentConfig,
                created_at: new Date().toISOString()
            };
            
            code.textContent = JSON.stringify(configObject, null, 2);
        }

        // 更新最终预览
        function updateFinalPreview() {
            const name = agentConfig.name || `${agentConfig.type || '未知'}智能体`;
            const description = agentConfig.description || '自定义AI智能体';
            
            document.getElementById('finalName').textContent = name;
            document.getElementById('finalDescription').textContent = description;
        }

        // 创建最终智能体
        function createFinalAgent() {
            const finalConfig = {
                ...agentConfig,
                aiModel: selectedAIModel,
                behaviorPattern: selectedBehaviorPattern,
                id: generateAgentId(),
                createdAt: new Date().toISOString(),
                status: 'ready'
            };

            // 保存到本地存储
            const agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
            agents.push(finalConfig);
            localStorage.setItem('qiaopi_agents', JSON.stringify(agents));

            // 显示成功消息
            alert(`智能体 "${finalConfig.name || finalConfig.type}" 创建成功！\n\n你可以在主页面的AI智能体部分查看和管理它。`);
            
            // 跳转回主页面
            window.location.href = '/#agents';
        }

        // 测试配置
        function testConfiguration() {
            const testConfig = {
                ...agentConfig,
                aiModel: selectedAIModel,
                behaviorPattern: selectedBehaviorPattern
            };
            
            alert(`配置测试完成！\n\n配置评分: ${generateTestScore(testConfig)}/100\n性能预估: ${generatePerformanceEstimate(testConfig)}\n\n配置有效，可以创建智能体。`);
        }

        // 生成测试分数
        function generateTestScore(config) {
            let score = 50;
            
            if (config.type) score += 15;
            if (selectedAIModel) score += 15;
            if (selectedBehaviorPattern) score += 10;
            if (config.learningRate) score += 5;
            if (config.riskTolerance) score += 5;
            
            return Math.min(score, 100);
        }

        // 生成性能估算
        function generatePerformanceEstimate(config) {
            const estimates = ['优秀', '良好', '一般', '需要优化'];
            const score = generateTestScore(config);
            
            if (score >= 90) return estimates[0];
            if (score >= 75) return estimates[1];
            if (score >= 60) return estimates[2];
            return estimates[3];
        }

        function generateAgentId() {
            return 'agent_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 监听高级配置切换
        document.addEventListener('change', function(e) {
            if (e.target.id === 'enableAdvancedRL') {
                document.getElementById('advancedRLConfig').style.display = 
                    e.target.checked ? 'block' : 'none';
            }
        });

        // 更新模型预览
        function updateModelPreview(model) {
            const descriptions = {
                reinforcement_learning: '通过与环境交互学习最优策略，适合动态决策场景',
                deep_neural: '使用深度神经网络处理复杂非线性关系，适合模式识别',
                rule_based: '基于专家知识的确定性决策，可解释性强',
                hybrid: '结合多种方法的优势，平衡性能与可解释性'
            };
            
            document.getElementById('aiModelDescription').textContent = descriptions[model] || '';
            document.getElementById('modelPerformance').style.display = 'block';
            
            // 更新性能指标
            const performance = generateModelPerformance(model);
            document.getElementById('performanceLearning').textContent = performance.learning + '%';
            document.getElementById('performanceSpeed').textContent = performance.speed + '%';
            document.getElementById('performanceAccuracy').textContent = performance.accuracy + '%';
        }

        function generateModelPerformance(model) {
            const performances = {
                reinforcement_learning: { learning: 95, speed: 75, accuracy: 85 },
                deep_neural: { learning: 90, speed: 80, accuracy: 90 },
                rule_based: { learning: 60, speed: 95, accuracy: 75 },
                hybrid: { learning: 85, speed: 85, accuracy: 85 }
            };
            
            return performances[model] || { learning: 70, speed: 70, accuracy: 70 };
        }
    </script>
</body>
</html>