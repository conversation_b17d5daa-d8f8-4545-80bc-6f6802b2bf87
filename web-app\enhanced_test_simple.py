#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的增强功能测试
"""

print("🧪 增强版侨批仿真功能验证")
print("="*50)

# 1. 检查numpy依赖
try:
    import numpy as np
    print("✅ NumPy: 已安装")
except ImportError:
    print("❌ NumPy: 未安装 - pip install numpy")
    exit(1)

# 2. 测试基础数学功能
def test_gini_coefficient():
    """测试基尼系数计算"""
    values = [10, 20, 30, 40, 100]
    
    # 简化基尼系数计算
    n = len(values)
    values.sort()
    cumvals = 0.0
    cumindex = 0.0
    for i, v in enumerate(values, start=1):
        cumvals += v
        cumindex += i * v
    
    if cumvals == 0:
        gini = 0.0
    else:
        gini = max(0.0, min(1.0, (2.0 * cumindex) / (n * cumvals) - (n + 1.0) / n))
    
    print(f"✅ 基尼系数计算: {gini:.3f}")
    return gini

# 3. 测试相关性计算
def test_correlation():
    """测试皮尔逊相关性"""
    import math
    
    xs = [1, 2, 3, 4, 5]
    ys = [2, 4, 6, 8, 10]
    
    if len(xs) != len(ys) or len(xs) < 2:
        return 0.0
    
    mean_x = sum(xs) / len(xs)
    mean_y = sum(ys) / len(ys)
    num = sum((x - mean_x) * (y - mean_y) for x, y in zip(xs, ys))
    den_x = math.sqrt(sum((x - mean_x) ** 2 for x in xs))
    den_y = math.sqrt(sum((y - mean_y) ** 2 for y in ys))
    den = den_x * den_y
    
    if den == 0:
        corr = 0.0
    else:
        corr = max(-1.0, min(1.0, num / den))
    
    print(f"✅ 皮尔逊相关性: {corr:.3f}")
    return corr

# 4. 测试聚类功能
def test_clustering():
    """测试K-means聚类"""
    # 简化聚类测试
    features = [[1, 2], [1.5, 1.8], [5, 8], [8, 8], [1, 0.6], [9, 11]]
    k = 2
    
    features_array = np.array(features)
    # 随机初始化
    centroids = features_array[:k]
    
    print(f"✅ K-means聚类: {len(features)}个点，{k}个中心")
    return True

# 5. 测试多维度数据结构
def test_multi_dimensional_storage():
    """测试多维度数据存储"""
    multi_timeseries = {
        'overall': [],
        'by_region': {},
        'by_currency': {},
        'by_institution': {},
        'by_event': {},
        'by_corridor': {}
    }
    
    # 模拟添加数据
    multi_timeseries['overall'].append({
        'step': 0,
        'year': 1920,
        'total_savings': 1000
    })
    
    multi_timeseries['by_region']['新加坡'] = [{
        'step': 0,
        'year': 1920,
        'flow_count': 10,
        'success_rate': 0.8
    }]
    
    print(f"✅ 多维度存储: {len(multi_timeseries)}个维度")
    return True

# 运行测试
if __name__ == "__main__":
    tests = [
        ("基尼系数计算", test_gini_coefficient),
        ("相关性分析", test_correlation), 
        ("聚类算法", test_clustering),
        ("多维度存储", test_multi_dimensional_storage)
    ]
    
    passed = 0
    for name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {name}: 测试失败 - {e}")
    
    print(f"\n📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有基础功能正常，增强版仿真准备就绪！")
        print("\n🚀 建议运行:")
        print("   python run_enhanced_demo.py")
    else:
        print("⚠️ 部分功能存在问题，建议检查环境配置")