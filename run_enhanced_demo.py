#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版侨批仿真演示脚本
Enhanced Qiaopi Simulation Demo
"""

import os
import json
import time
from datetime import datetime
from enhanced_simulation_engine import (
    EnhancedQiaopiSimulationEngine, 
    EnhancedAnalysisConfig,
    PRESET_SCENARIOS,
    create_enhanced_simulation_demo
)
from simulation_engine import SimulationConfig


def print_demo_header():
    """打印演示标题"""
    print("🎯" + "="*80)
    print("         增强版侨批网络仿真系统 - 多时间序列与高级分析")
    print("           Enhanced Qiaopi Network Simulation System")
    print("="*82)
    print()


def print_enhancement_features():
    """打印增强功能特性"""
    print("🆕 新增功能特性:")
    print()
    
    features = [
        ("🕐 多时间序列分析", [
            "整体、地区、货币、机构、事件、通道等多维度时间序列",
            "月度、季度、年度等多粒度分析",
            "动态趋势检测与季节性分析"
        ]),
        ("📈 高级统计分析", [
            "基尼系数、不平等指标、百分位数分析",
            "相关性矩阵、趋势检测、周期性识别",
            "韧性评估、波动率计算、恢复能力分析"
        ]),
        ("🧠 机器学习分析", [
            "K-means聚类分析智能体行为模式",
            "网络拓扑分析与社群检测",
            "预测建模与情景分析"
        ]),
        ("🌍 多维度视角", [
            "按地理区域的差异化分析",
            "按货币类型的市场份额分析", 
            "按历史事件的冲击影响分析"
        ]),
        ("📊 可视化数据", [
            "时间序列图表数据生成",
            "热力图矩阵数据",
            "网络图拓扑数据",
            "相关性矩阵可视化"
        ])
    ]
    
    for title, items in features:
        print(f"   {title}")
        for item in items:
            print(f"      • {item}")
        print()


def run_comprehensive_demo():
    """运行综合演示"""
    print("🚀 启动综合增强仿真演示...")
    print()
    
    # 1. 运行单一增强仿真
    print("📍 步骤 1: 运行单一增强仿真")
    print("-" * 40)
    
    start_time = time.time()
    results = create_enhanced_simulation_demo()
    single_sim_time = time.time() - start_time
    
    print(f"✅ 单一仿真完成，用时: {single_sim_time:.2f}秒")
    print()
    
    # 2. 运行多场景对比
    print("📍 步骤 2: 运行多场景对比分析")
    print("-" * 40)
    
    scenarios = PRESET_SCENARIOS[:3]  # 运行前3个场景
    
    analysis_config = EnhancedAnalysisConfig(
        enable_scenario_comparison=True,
        comparison_scenarios=scenarios
    )
    
    base_config = SimulationConfig(
        start_year=1920,
        end_year=1935,
        num_migrants=200,
        num_families=200,
        num_institutions=8,
        output_directory="multi_scenario_results"
    )
    
    start_time = time.time()
    engine = EnhancedQiaopiSimulationEngine(base_config, analysis_config)
    multi_scenario_results = engine.run_multi_scenario_analysis(scenarios)
    multi_sim_time = time.time() - start_time
    
    print(f"✅ 多场景分析完成，用时: {multi_sim_time:.2f}秒")
    print()
    
    return results, multi_scenario_results


def analyze_and_report_results(single_results: dict, multi_results: dict):
    """分析并报告结果"""
    print("📊 结果分析与报告")
    print("="*50)
    
    # 1. 时间序列分析报告
    print("\n🕐 多时间序列分析:")
    multi_ts = single_results.get('multi_timeseries_analysis', {})
    
    overall_series = multi_ts.get('overall_series', {})
    if overall_series:
        print(f"   📏 整体时间序列长度: {overall_series.get('length', 0)}")
        print(f"   📅 时间范围: {overall_series.get('time_range', ('N/A', 'N/A'))}")
    
    regional_series = multi_ts.get('regional_series', {})
    print(f"   🌍 地区时间序列数量: {len(regional_series)}")
    
    for region, stats in list(regional_series.items())[:3]:  # 显示前3个
        resilience = stats.get('economic_resilience', 0)
        volatility = stats.get('remittance_volatility', 0)
        print(f"      📍 {region}: 韧性={resilience:.3f}, 波动率={volatility:.3f}")
    
    currency_series = multi_ts.get('currency_series', {})
    print(f"   💰 货币时间序列数量: {len(currency_series)}")
    
    # 2. 高级分析报告
    print("\n📈 高级统计分析:")
    
    trend_analysis = single_results.get('trend_analysis', {})
    overall_trends = trend_analysis.get('overall_trends', {})
    for metric, trend in overall_trends.items():
        print(f"   📊 {metric}: {trend}")
    
    clustering = single_results.get('clustering_analysis', {})
    migrant_clusters = clustering.get('migrant_behavior_clusters', {}).get('cluster_profiles', {})
    print(f"   🎯 移民行为聚类: {len(migrant_clusters)}个群体")
    
    for cluster_name, profile in list(migrant_clusters.items())[:3]:
        size = profile.get('size', 0)
        desc = profile.get('description', '未知')
        print(f"      🏷️  {cluster_name}: {size}人, 特征={desc}")
    
    # 3. 网络分析报告
    print("\n🌐 网络拓扑分析:")
    network_analysis = single_results.get('network_topology_analysis', {})
    density = network_analysis.get('network_density', 0)
    print(f"   🔗 网络密度: {density:.4f}")
    
    centrality = network_analysis.get('centrality_analysis', {}).get('top_nodes', [])
    print(f"   ⭐ 核心节点数量: {len(centrality)}")
    
    # 4. 预测分析报告
    print("\n🔮 预测分析:")
    prediction = single_results.get('prediction_analysis', {})
    forecast_5y = prediction.get('5_year_forecast', {})
    if forecast_5y:
        future_years = forecast_5y.get('future_years', [])
        pred_volume = forecast_5y.get('predicted_savings', [])
        confidence = forecast_5y.get('confidence_level', 0)
        print(f"   📅 5年预测: {future_years}")
        print(f"   💰 预测储蓄: {pred_volume}")
        print(f"   🎯 置信度: {confidence:.1%}")
    
    scenarios = prediction.get('scenario_forecasts', {})
    for scenario_name, scenario_pred in scenarios.items():
        vol = scenario_pred.get('predicted_volume', 0)
        success = scenario_pred.get('predicted_success_rate', 0)
        print(f"   📋 {scenario_name}情景: 预测量={vol:.1f}, 成功率={success:.1%}")
    
    # 5. 多场景对比报告
    print("\n🔬 多场景对比分析:")
    comparison = multi_results.get('comparison', {})
    
    for metric, comp_data in comparison.items():
        values = comp_data.get('values', {})
        best = comp_data.get('best_scenario', 'N/A')
        worst = comp_data.get('worst_scenario', 'N/A')
        print(f"   📊 {metric}:")
        print(f"      🥇 最佳场景: {best}")
        print(f"      🔴 最差场景: {worst}")
        print(f"      📈 场景值: {values}")
    
    print("\n" + "="*60)


def save_comprehensive_report(single_results: dict, multi_results: dict):
    """保存综合报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    comprehensive_report = {
        'generated_at': timestamp,
        'report_type': 'Enhanced Qiaopi Simulation Analysis',
        'single_simulation_results': single_results,
        'multi_scenario_results': multi_results,
        'analysis_summary': {
            'timeseries_count': len(single_results.get('multi_timeseries_analysis', {}).get('regional_series', {})),
            'cluster_count': len(single_results.get('clustering_analysis', {}).get('migrant_behavior_clusters', {}).get('cluster_profiles', {})),
            'scenario_count': len(multi_results.get('scenarios', {})),
            'prediction_horizon': 5,
            'analysis_features': [
                'multi_timeseries', 'trend_analysis', 'correlation_analysis',
                'clustering_analysis', 'prediction_analysis', 'network_topology',
                'event_impact_analysis', 'scenario_comparison'
            ]
        }
    }
    
    # 保存到多个位置
    report_files = [
        f"enhanced_comprehensive_report_{timestamp}.json",
        f"enhanced_results/comprehensive_report.json",
        f"simulation_results/enhanced_report_{timestamp}.json"
    ]
    
    for report_file in report_files:
        try:
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_report, f, ensure_ascii=False, indent=2, default=str)
            print(f"📝 报告已保存: {report_file}")
        except Exception as e:
            print(f"⚠️  保存报告失败 {report_file}: {e}")


def main():
    """主函数"""
    print_demo_header()
    print_enhancement_features()
    
    print("🔧 准备运行增强仿真演示...")
    print("   ⏱️  预计用时: 2-5分钟")
    print("   💾 输出文件: enhanced_results/ 目录")
    print("   📊 分析维度: 8个主要模块")
    print()
    
    response = input("🚀 是否开始运行? (y/n): ").lower().strip()
    
    if response == 'y':
        try:
            # 运行综合演示
            single_results, multi_results = run_comprehensive_demo()
            
            # 分析和报告
            analyze_and_report_results(single_results, multi_results)
            
            # 保存综合报告
            save_comprehensive_report(single_results, multi_results)
            
            print("\n🎉 增强版仿真演示完成!")
            print("📖 详细结果请查看 enhanced_results/ 目录")
            
        except KeyboardInterrupt:
            print("\n⚠️ 演示被用户中断")
        except Exception as e:
            print(f"\n❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("👋 演示取消")


if __name__ == "__main__":
    main()