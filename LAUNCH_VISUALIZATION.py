#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qiaopi Visualization System - Master Launcher
侨批可视化系统 - 主启动器

Complete launcher with all visualization options
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

class VisualizationLauncher:
    """Master launcher for all visualization components"""
    
    def __init__(self):
        self.processes = []
        self.options = {
            '1': {
                'name': 'Simple Dashboard (Recommended)',
                'description': 'Basic visualization with minimal dependencies',
                'script': 'visualization/simple_dashboard.py',
                'port': 8050,
                'requirements': ['dash', 'plotly', 'pandas', 'numpy']
            },
            '2': {
                'name': 'Advanced Dashboard',
                'description': 'Full-featured dashboard with real-time streaming',
                'script': 'visualization/advanced_dashboard.py',
                'port': 8050,
                'requirements': ['dash', 'plotly', 'dash-bootstrap-components', 'flask-socketio', 'networkx']
            },
            '3': {
                'name': 'Performance Monitor',
                'description': 'System performance monitoring',
                'script': 'visualization/performance_monitor.py',
                'port': 8051,
                'requirements': ['dash', 'plotly', 'psutil']
            },
            '4': {
                'name': 'Interactive Configuration',
                'description': 'Configure and run custom simulations',
                'script': 'visualization/interactive_config.py',
                'port': None,
                'requirements': []
            },
            '5': {
                'name': 'Data Export Tools',
                'description': 'Export simulation data to various formats',
                'script': 'visualization/data_export_tools.py',
                'port': None,
                'requirements': ['pandas', 'matplotlib', 'seaborn']
            }
        }
    
    def print_header(self):
        """Print welcome header"""
        print("""
╔════════════════════════════════════════════════════════════╗
║          QIAOPI VISUALIZATION SYSTEM LAUNCHER             ║
║          侨批可视化系统启动器                                ║
╚════════════════════════════════════════════════════════════╝
        """)
    
    def check_requirements(self, requirements):
        """Check if required packages are installed"""
        missing = []
        
        for package in requirements:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing.append(package)
        
        return missing
    
    def install_requirements(self, packages):
        """Install missing packages"""
        if not packages:
            return True
        
        print(f"\n📦 Installing required packages: {', '.join(packages)}")
        
        for package in packages:
            print(f"  Installing {package}...", end=" ", flush=True)
            try:
                subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    check=True
                )
                print("✓")
            except:
                print("✗")
                return False
        
        return True
    
    def show_menu(self):
        """Display main menu"""
        print("\n" + "─"*60)
        print("Available Options:")
        print("─"*60 + "\n")
        
        for key, option in self.options.items():
            print(f"  [{key}] {option['name']}")
            print(f"      {option['description']}")
            if option['port']:
                print(f"      Port: {option['port']}")
            print()
        
        print("  [A] Run All Dashboards")
        print("  [I] Install All Dependencies")
        print("  [Q] Quit")
        print()
    
    def run_option(self, choice):
        """Run selected option"""
        if choice not in self.options:
            print("❌ Invalid option")
            return
        
        option = self.options[choice]
        script_path = option['script']
        
        # Check if script exists
        if not Path(script_path).exists():
            print(f"❌ Script not found: {script_path}")
            return
        
        # Check requirements
        missing = self.check_requirements(option['requirements'])
        if missing:
            print(f"\n⚠️  Missing packages: {', '.join(missing)}")
            response = input("Install now? (y/n): ").lower().strip()
            
            if response in ['y', 'yes']:
                if not self.install_requirements(missing):
                    print("❌ Failed to install requirements")
                    return
            else:
                print("❌ Cannot run without required packages")
                return
        
        print(f"\n🚀 Starting {option['name']}...")
        
        # Run the script
        try:
            if option['port']:
                # For web applications, run in background
                process = subprocess.Popen([sys.executable, script_path])
                self.processes.append(process)
                
                # Wait a moment and open browser
                time.sleep(3)
                url = f"http://localhost:{option['port']}"
                print(f"📊 Opening browser to {url}")
                webbrowser.open(url)
                
                print(f"\n✅ {option['name']} is running")
                print("Press Enter to return to menu (dashboard will keep running)")
                input()
            else:
                # For scripts, run and wait
                subprocess.run([sys.executable, script_path])
        
        except Exception as e:
            print(f"❌ Error: {e}")
    
    def run_all_dashboards(self):
        """Run all web dashboards"""
        print("\n🚀 Starting all dashboards...")
        
        web_options = ['1', '3']  # Simple dashboard and performance monitor
        
        for opt in web_options:
            option = self.options[opt]
            
            # Check requirements
            missing = self.check_requirements(option['requirements'])
            if missing:
                print(f"⚠️  Skipping {option['name']} - missing: {', '.join(missing)}")
                continue
            
            # Start process
            try:
                process = subprocess.Popen([sys.executable, option['script']])
                self.processes.append(process)
                print(f"✅ Started {option['name']} on port {option['port']}")
                time.sleep(2)  # Give each service time to start
            except Exception as e:
                print(f"❌ Failed to start {option['name']}: {e}")
        
        print("\n✅ All dashboards started!")
        print("\nDashboards running at:")
        print("  - Simple Dashboard: http://localhost:8050")
        print("  - Performance Monitor: http://localhost:8051")
        
        print("\nPress Enter to return to menu")
        input()
    
    def install_all_dependencies(self):
        """Install all dependencies for all components"""
        print("\n📦 Installing all dependencies...")
        
        all_packages = set()
        for option in self.options.values():
            all_packages.update(option['requirements'])
        
        # Add optional packages
        all_packages.update([
            'redis', 'celery', 'reportlab', 'geopandas',
            'prophet', 'torch', 'tensorflow'
        ])
        
        print(f"\nPackages to install: {len(all_packages)}")
        print("This may take several minutes...\n")
        
        success = 0
        failed = []
        
        for package in sorted(all_packages):
            print(f"Installing {package}...", end=" ", flush=True)
            try:
                subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    check=True,
                    timeout=120
                )
                print("✓")
                success += 1
            except:
                print("✗")
                failed.append(package)
        
        print(f"\n✅ Installed: {success}")
        if failed:
            print(f"❌ Failed: {len(failed)} - {', '.join(failed[:5])}")
    
    def cleanup(self):
        """Clean up running processes"""
        if self.processes:
            print("\n🛑 Stopping running processes...")
            for process in self.processes:
                try:
                    process.terminate()
                except:
                    pass
            
            # Give processes time to stop
            time.sleep(2)
            
            # Force kill if needed
            for process in self.processes:
                try:
                    if process.poll() is None:
                        process.kill()
                except:
                    pass
            
            print("✅ All processes stopped")
    
    def run(self):
        """Main launcher loop"""
        self.print_header()
        
        # Quick dependency check
        core_missing = self.check_requirements(['dash', 'plotly'])
        if core_missing:
            print("⚠️  Core packages missing. Installing...")
            self.install_requirements(core_missing)
        
        while True:
            self.show_menu()
            
            choice = input("Select an option: ").strip().upper()
            
            if choice == 'Q':
                print("\n👋 Goodbye!")
                self.cleanup()
                break
            elif choice == 'A':
                self.run_all_dashboards()
            elif choice == 'I':
                self.install_all_dependencies()
            elif choice in self.options:
                self.run_option(choice)
            else:
                print("❌ Invalid option. Please try again.")


def main():
    """Main entry point"""
    
    # Ensure we're in the right directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    launcher = VisualizationLauncher()
    
    try:
        launcher.run()
    except KeyboardInterrupt:
        print("\n\n⚠️  Interrupted by user")
        launcher.cleanup()
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        launcher.cleanup()
        raise
    
    return 0


if __name__ == "__main__":
    sys.exit(main())