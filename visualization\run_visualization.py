#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integrated Visualization System Launcher
集成可视化系统启动器

Complete enterprise-grade visualization solution
"""

import sys
import os
import asyncio
import threading
import time
import signal
import json
from pathlib import Path
from datetime import datetime
import webbrowser

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import visualization components
from advanced_dashboard import AdvancedVisualizationSystem
from realtime_streaming import DataStreamProcessor, WebSocketServer, SimulationDataStreamer
from advanced_analytics import (
    TimeSeriesAnalyzer, NetworkAnalyzer, PredictiveModeling,
    create_analytics_dashboard_data
)

# Import simulation components
from simulation_engine import QiaopiSimulationEngine, SimulationConfig
from data_integration import create_sample_dataset

import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntegratedVisualizationSystem:
    """
    Complete integrated visualization system
    完整的集成可视化系统
    """
    
    def __init__(self):
        self.simulation = None
        self.dashboard = None
        self.stream_processor = None
        self.ws_server = None
        self.data_streamer = None
        self.analytics = {
            'time_series': TimeSeriesAnalyzer(),
            'network': NetworkAnalyzer(),
            'predictive': PredictiveModeling()
        }
        
        # System state
        self.is_running = False
        self.threads = []
        
        # Data storage
        self.simulation_data = {
            'time_series': {},
            'network_snapshots': [],
            'agent_states': [],
            'events': []
        }
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info("Shutdown signal received")
        self.shutdown()
        sys.exit(0)
    
    def initialize_simulation(self, config: SimulationConfig = None):
        """Initialize the simulation engine"""
        if config is None:
            config = SimulationConfig(
                start_year=1920,
                end_year=1930,  # 10 years for demo
                steps_per_year=12,
                num_migrants=100,
                num_families=100,
                num_institutions=10,
                output_directory="visualization_output"
            )
        
        logger.info(f"Initializing simulation: {config.start_year}-{config.end_year}")
        
        # Create output directory
        Path(config.output_directory).mkdir(parents=True, exist_ok=True)
        
        # Initialize simulation
        self.simulation = QiaopiSimulationEngine(config)
        
        # Create sample historical data
        create_sample_dataset()
        
        logger.info(f"Simulation initialized with {len(self.simulation.migrants)} migrants")
    
    def initialize_streaming(self):
        """Initialize real-time streaming components"""
        logger.info("Initializing streaming infrastructure")
        
        # Create stream processor
        self.stream_processor = DataStreamProcessor(buffer_size=10000)
        self.stream_processor.start()
        
        # Create WebSocket server
        self.ws_server = WebSocketServer(self.stream_processor)
        
        # Create data streamer
        if self.simulation:
            self.data_streamer = SimulationDataStreamer(self.simulation, self.stream_processor)
        
        logger.info("Streaming infrastructure initialized")
    
    def initialize_dashboard(self):
        """Initialize the visualization dashboard"""
        logger.info("Initializing dashboard")
        
        self.dashboard = AdvancedVisualizationSystem(self.simulation)
        
        logger.info("Dashboard initialized")
    
    def run_simulation_async(self):
        """Run simulation in background thread"""
        def simulation_worker():
            logger.info("Starting simulation worker")
            
            try:
                # Run simulation
                total_steps = (self.simulation.config.end_year - self.simulation.config.start_year) * \
                            self.simulation.config.steps_per_year
                
                for step in range(total_steps):
                    if not self.is_running:
                        break
                    
                    # Run simulation step
                    self.simulation.current_step = step
                    self.simulation._run_single_step()
                    
                    # Collect data periodically
                    if step % 10 == 0:  # Every 10 steps
                        self._collect_simulation_data()
                    
                    # Progress update
                    if step % 100 == 0:
                        progress = (step / total_steps) * 100
                        current_year = self.simulation.environment.get_current_year()
                        logger.info(f"Simulation progress: {progress:.1f}% - Year: {current_year:.1f}")
                    
                    # Small delay to allow visualization
                    time.sleep(0.1)
                
                logger.info("Simulation completed")
                
            except Exception as e:
                logger.error(f"Simulation error: {e}")
        
        # Start simulation thread
        sim_thread = threading.Thread(target=simulation_worker, daemon=True)
        sim_thread.start()
        self.threads.append(sim_thread)
    
    def _collect_simulation_data(self):
        """Collect data from simulation for analytics"""
        try:
            # Collect time series data
            stats = self.simulation._calculate_current_statistics()
            
            timestamp = self.simulation.environment.get_current_year()
            
            # Update time series
            if 'remittance_volume' not in self.simulation_data['time_series']:
                self.simulation_data['time_series']['remittance_volume'] = []
            
            self.simulation_data['time_series']['remittance_volume'].append(
                stats.get('total_migrant_savings', 0)
            )
            
            # Collect network snapshot periodically
            if self.simulation.current_step % 50 == 0:
                network_data = {
                    'kinship_networks': self.simulation.kinship_networks,
                    'migrant_family_pairs': self.simulation.migrant_family_pairs
                }
                self.simulation_data['network_snapshots'].append(network_data)
            
            # Run analytics periodically
            if self.simulation.current_step % 100 == 0:
                self._run_analytics()
                
        except Exception as e:
            logger.error(f"Error collecting simulation data: {e}")
    
    def _run_analytics(self):
        """Run analytics on collected data"""
        try:
            analytics_results = create_analytics_dashboard_data(self.simulation_data)
            
            # Save results
            output_file = Path("visualization_output") / f"analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(analytics_results, f, indent=2, default=str)
            
            logger.info(f"Analytics results saved to {output_file}")
            
        except Exception as e:
            logger.error(f"Analytics error: {e}")
    
    async def run_websocket_server(self):
        """Run WebSocket server asynchronously"""
        if self.ws_server:
            await self.ws_server.run(host='0.0.0.0', port=8081)
    
    def run(self):
        """Run the complete integrated system"""
        logger.info("=" * 60)
        logger.info("Starting Qiaopi Network Visualization System")
        logger.info("启动侨批网络可视化系统")
        logger.info("=" * 60)
        
        self.is_running = True
        
        # Initialize components
        logger.info("Initializing components...")
        self.initialize_simulation()
        self.initialize_streaming()
        self.initialize_dashboard()
        
        # Start simulation
        logger.info("Starting simulation...")
        self.run_simulation_async()
        
        # Start data streaming
        if self.data_streamer:
            logger.info("Starting data streaming...")
            self.data_streamer.start_streaming()
        
        # Start WebSocket server in separate thread
        def ws_worker():
            asyncio.run(self.run_websocket_server())
        
        ws_thread = threading.Thread(target=ws_worker, daemon=True)
        ws_thread.start()
        self.threads.append(ws_thread)
        
        # Open browser
        time.sleep(2)  # Wait for servers to start
        dashboard_url = "http://localhost:8050"
        logger.info(f"Opening dashboard at {dashboard_url}")
        webbrowser.open(dashboard_url)
        
        # Run dashboard (this blocks)
        logger.info("Starting dashboard server...")
        try:
            self.dashboard.run(debug=False, port=8050)
        except Exception as e:
            logger.error(f"Dashboard error: {e}")
        
        # Cleanup on exit
        self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down visualization system...")
        
        self.is_running = False
        
        # Stop streaming
        if self.data_streamer:
            self.data_streamer.stop_streaming()
        
        if self.stream_processor:
            self.stream_processor.stop()
        
        # Save final analytics
        self._run_analytics()
        
        # Save simulation results
        if self.simulation:
            try:
                results = self.simulation._generate_final_report()
                output_file = Path("visualization_output") / f"final_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                logger.info(f"Final results saved to {output_file}")
            except Exception as e:
                logger.error(f"Error saving final results: {e}")
        
        logger.info("Shutdown complete")


def main():
    """Main entry point"""
    
    print("""
    ╔════════════════════════════════════════════════════════════╗
    ║     Qiaopi Network Advanced Visualization System          ║
    ║     侨批网络高级可视化系统                                   ║
    ╔════════════════════════════════════════════════════════════╗
    ║                                                            ║
    ║  Features:                                                 ║
    ║  • Real-time simulation monitoring                        ║
    ║  • Interactive network visualization                      ║
    ║  • Advanced analytics and ML predictions                  ║
    ║  • WebSocket streaming for live updates                   ║
    ║  • Geographic migration pattern mapping                   ║
    ║  • Time series analysis and forecasting                   ║
    ║                                                            ║
    ║  Dashboard: http://localhost:8050                         ║
    ║  WebSocket: ws://localhost:8081                           ║
    ║                                                            ║
    ║  Press Ctrl+C to stop                                     ║
    ╚════════════════════════════════════════════════════════════╝
    """)
    
    # Check dependencies
    try:
        import plotly
        import dash
        import pandas
        import numpy
        import networkx
        import sklearn
        logger.info("All dependencies verified")
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        logger.error("Please install requirements: pip install -r visualization/requirements.txt")
        return 1
    
    # Create and run system
    system = IntegratedVisualizationSystem()
    
    try:
        system.run()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"System error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        system.shutdown()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())