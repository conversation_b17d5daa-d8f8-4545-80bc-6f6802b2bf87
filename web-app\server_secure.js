const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const compression = require('compression');
const moment = require('moment');
const _ = require('lodash');

const app = express();
const PORT = process.env.PORT || 3500;

// 中间件 - 使用安全的Helmet配置
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'", "https:", "http:", "data:"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
            scriptSrcAttr: ["'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
            styleSrcAttr: ["'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:", "http:"],
            fontSrc: ["'self'", "data:", "https://cdnjs.cloudflare.com"],
            connectSrc: ["'self'", "ws:", "wss:", "https:", "http:"],
            mediaSrc: ["'self'", "https:", "http:"],
            objectSrc: ["'none'"],
            childSrc: ["'self'"],
            workerSrc: ["'self'"],
            frameSrc: ["'self'"],
            formAction: ["'self'"],
            frameAncestors: ["'self'"],
            baseUri: ["'self'"],
            manifestSrc: ["'self'"],
            upgradeInsecureRequests: []
        }
    }
}));
app.use(compression());
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static('public', {
    setHeaders: (res, path) => {
        if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
        } else if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css');
        }
    }
}));

// 数据目录
const ROOT_DIR = path.join(__dirname, '..');
const DATA_DIR = path.join(ROOT_DIR, 'simulation_results');
const WEB_DATA_DIR = path.join(__dirname, 'simulation_results');
const REAL_DATA_DIR = path.join(ROOT_DIR, 'realistic_results');

// 确保数据目录存在
fs.ensureDirSync(DATA_DIR);
fs.ensureDirSync(REAL_DATA_DIR);
fs.ensureDirSync(WEB_DATA_DIR);

// API路由
app.get('/api/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// ===== 高级分析 API =====
// 探测可用的 final_report.json 列表
async function discoverFinalReports() {
    const candidates = [];

    async function pushIfExists(id, dirPath) {
        try {
            const reportPath = path.join(dirPath, 'final_report.json');
            if (await fs.pathExists(reportPath)) {
                const stat = await fs.stat(reportPath);
                candidates.push({ id, dir: dirPath, reportPath, mtime: stat.mtime });
            }
        } catch (_) {}
    }

    await pushIfExists('simulation_results', DATA_DIR);
    await pushIfExists('web_simulation_results', WEB_DATA_DIR);
    await pushIfExists('demo_results', path.join(ROOT_DIR, 'demo_results'));
    await pushIfExists('scenario_1_results', path.join(ROOT_DIR, 'scenario_1_results'));
    await pushIfExists('scenario_2_results', path.join(ROOT_DIR, 'scenario_2_results'));
    await pushIfExists('scenario_3_results', path.join(ROOT_DIR, 'scenario_3_results'));

    try {
        const items = await fs.readdir(ROOT_DIR);
        for (const item of items) {
            if (/^scenario_.*_results$/.test(item)) {
                await pushIfExists(item, path.join(ROOT_DIR, item));
            }
        }
    } catch (_) {}

    try {
        const reportPath = path.join(WEB_DATA_DIR, 'final_report.json');
        if (await fs.pathExists(reportPath)) {
            const stat = await fs.stat(reportPath);
            candidates.push({ id: 'web_final', dir: WEB_DATA_DIR, reportPath, mtime: stat.mtime });
        }
    } catch (_) {}

    const seen = new Set();
    const unique = [];
    for (const c of candidates) {
        if (!seen.has(c.id)) { seen.add(c.id); unique.push(c); }
    }
    return unique.sort((a, b) => b.mtime - a.mtime);
}

async function loadFinalReportById(id) {
    const list = await discoverFinalReports();
    const item = list.find(x => x.id === id);
    if (!item) return null;
    return fs.readJson(item.reportPath);
}

async function loadLatestFinalReport() {
    const list = await discoverFinalReports();
    if (list.length === 0) return null;
    return fs.readJson(list[0].reportPath);
}

// 列出可用报告
app.get('/api/advanced/list', async (req, res) => {
    try {
        const list = await discoverFinalReports();
        res.json(list.map(x => ({ id: x.id, dir: x.dir, mtime: x.mtime })));
    } catch (err) {
        console.error('Error discovering reports:', err);
        res.status(500).json({ error: 'Failed to list reports' });
    }
});

// 获取最新报告（包含 advanced_analysis）
app.get('/api/advanced/latest', async (req, res) => {
    try {
        const report = await loadLatestFinalReport();
        if (!report) return res.status(404).json({ error: 'No final_report.json found' });
        res.json(report);
    } catch (err) {
        console.error('Error loading latest report:', err);
        res.status(500).json({ error: 'Failed to load latest report' });
    }
});

// 获取指定ID报告
app.get('/api/advanced/:id', async (req, res) => {
    try {
        const report = await loadFinalReportById(req.params.id);
        if (!report) return res.status(404).json({ error: 'Report not found' });
        res.json(report);
    } catch (err) {
        console.error('Error loading report by id:', err);
        res.status(500).json({ error: 'Failed to load report' });
    }
});

// 兼容路径：/api/simulations/:id/report -> 返回final_report
app.get('/api/simulations/:id/report', async (req, res) => {
    try {
        const report = await loadFinalReportById(req.params.id);
        if (!report) return res.status(404).json({ error: 'Report not found' });
        res.json(report);
    } catch (err) {
        console.error('Error loading simulation report:', err);
        res.status(500).json({ error: 'Failed to load simulation report' });
    }
});

// 获取所有仿真结果
app.get('/api/simulations', async (req, res) => {
    try {
        const simulations = [];
        
        // 扫描仿真结果目录
        const directories = [DATA_DIR, REAL_DATA_DIR];
        
        for (const dir of directories) {
            if (await fs.pathExists(dir)) {
                const items = await fs.readdir(dir);
                for (const item of items) {
                    const itemPath = path.join(dir, item);
                    const stats = await fs.stat(itemPath);
                    
                    if (stats.isDirectory()) {
                        const simulation = await readSimulationInfo(itemPath, item);
                        if (simulation) {
                            simulations.push(simulation);
                        }
                    }
                }
            }
        }
        
        // 按时间排序
        simulations.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        res.json(simulations);
    } catch (error) {
        console.error('Error fetching simulations:', error);
        res.status(500).json({ error: 'Failed to fetch simulations' });
    }
});

// 获取特定仿真结果
app.get('/api/simulations/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const simulationPath = findSimulationPath(id);
        
        if (!simulationPath) {
            return res.status(404).json({ error: 'Simulation not found' });
        }
        
        const simulation = await loadSimulationData(simulationPath);
        res.json(simulation);
    } catch (error) {
        console.error('Error loading simulation:', error);
        res.status(500).json({ error: 'Failed to load simulation' });
    }
});

// 获取仿真统计数据
app.get('/api/simulations/:id/statistics', async (req, res) => {
    try {
        const { id } = req.params;
        const simulationPath = findSimulationPath(id);
        
        if (!simulationPath) {
            return res.status(404).json({ error: 'Simulation not found' });
        }
        
        const stats = await loadSimulationStatistics(simulationPath);
        res.json(stats);
    } catch (error) {
        console.error('Error loading statistics:', error);
        res.status(500).json({ error: 'Failed to load statistics' });
    }
});

// 获取时间序列数据
app.get('/api/simulations/:id/timeseries', async (req, res) => {
    try {
        const { id } = req.params;
        const simulationPath = findSimulationPath(id);
        
        if (!simulationPath) {
            return res.status(404).json({ error: 'Simulation not found' });
        }
        
        const timeseries = await loadTimeSeriesData(simulationPath);
        res.json(timeseries);
    } catch (error) {
        console.error('Error loading timeseries data:', error);
        res.status(500).json({ error: 'Failed to load timeseries data' });
    }
});

// 获取真实数据对比
app.get('/api/real-data', async (req, res) => {
    try {
        const realDataPath = path.join(__dirname, '..', 'real_qiaopi_analysis_results.json');
        
        if (await fs.pathExists(realDataPath)) {
            const realData = await fs.readJson(realDataPath);
            res.json(realData);
        } else {
            res.json({ error: 'Real data analysis not found' });
        }
    } catch (error) {
        console.error('Error loading real data:', error);
        res.status(500).json({ error: 'Failed to load real data' });
    }
});

// 上传仿真结果
app.post('/api/simulations/upload', async (req, res) => {
    try {
        // 这里可以添加文件上传功能
        res.json({ message: 'Upload functionality to be implemented' });
    } catch (error) {
        console.error('Error uploading simulation:', error);
        res.status(500).json({ error: 'Failed to upload simulation' });
    }
});

// 比较多个仿真结果
app.post('/api/simulations/compare', async (req, res) => {
    try {
        const { simulationIds } = req.body;
        
        if (!simulationIds || !Array.isArray(simulationIds)) {
            return res.status(400).json({ error: 'Invalid simulation IDs' });
        }
        
        const comparisons = [];
        
        for (const id of simulationIds) {
            const simulationPath = findSimulationPath(id);
            if (simulationPath) {
                const data = await loadSimulationData(simulationPath);
                comparisons.push({
                    id,
                    name: data.name || id,
                    statistics: data.statistics || {}
                });
            }
        }
        
        res.json(comparisons);
    } catch (error) {
        console.error('Error comparing simulations:', error);
        res.status(500).json({ error: 'Failed to compare simulations' });
    }
});

// 获取场景配置
app.get('/api/scenarios', async (req, res) => {
    try {
        const scenarios = [
            {
                id: 'network_resilience',
                name: '网络韧性',
                description: '系统冲击下的网络韧性分析',
                parameters: {
                    start_year: 1925,
                    end_year: 1945,
                    shocks: ['great_depression', 'japanese_occupation']
                }
            },
            {
                id: 'landscape_evolution',
                name: '景观演化',
                description: '侨乡景观的长期演化分析',
                parameters: {
                    start_year: 1900,
                    end_year: 2000,
                    focus: 'infrastructure_investment'
                }
            },
            {
                id: 'chain_migration',
                name: '链式移民',
                description: '链式移民与飞地形成分析',
                parameters: {
                    start_year: 1880,
                    end_year: 1930,
                    pioneers: 10
                }
            },
            {
                id: 'trust_mechanisms',
                name: '信任机制',
                description: '信任机制与机构演化分析',
                parameters: {
                    start_year: 1900,
                    end_year: 1950,
                    trust_levels: ['high', 'medium', 'low']
                }
            },
            {
                id: 'generational_wealth',
                name: '代际财富',
                description: '代际财富传递与社会流动分析',
                parameters: {
                    start_year: 1850,
                    end_year: 2000,
                    generations: 6
                }
            },
            {
                id: 'communication_technology',
                name: '通信技术',
                description: '通信技术与网络效率分析',
                parameters: {
                    start_year: 1880,
                    end_year: 1980,
                    tech_evolution: true
                }
            },
            {
                id: 'policy_intervention',
                name: '政策干预',
                description: '政策干预与网络适应分析',
                parameters: {
                    start_year: 1920,
                    end_year: 1970,
                    policies: ['free_market', 'active_intervention', 'strict_control']
                }
            }
        ];
        
        res.json(scenarios);
    } catch (error) {
        console.error('Error loading scenarios:', error);
        res.status(500).json({ error: 'Failed to load scenarios' });
    }
});

// Favicon路由
app.get('/favicon.ico', (req, res) => {
    res.status(204).end();
});

// CSP测试路由
app.get('/test-csp', (req, res) => {
    res.json({
        message: 'CSP Test',
        timestamp: new Date().toISOString(),
        headers: req.headers
    });
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// 错误处理
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// 辅助函数
async function readSimulationInfo(dirPath, dirName) {
    try {
        const infoPath = path.join(dirPath, 'simulation_info.json');
        const statsPath = path.join(dirPath, 'realistic_simulation_statistics.json');
        
        let info = {
            id: dirName,
            name: dirName,
            createdAt: null,
            status: 'unknown',
            agentCount: 0,
            timeSpan: {},
            description: ''
        };
        
        // 尝试读取基本信息
        if (await fs.pathExists(infoPath)) {
            const infoData = await fs.readJson(infoPath);
            info = { ...info, ...infoData };
        }
        
        // 尝试读取统计信息
        if (await fs.pathExists(statsPath)) {
            const statsData = await fs.readJson(statsPath);
            info.statistics = statsData;
            
            if (statsData.step_by_step && statsData.step_by_step.length > 0) {
                const firstStep = statsData.step_by_step[0];
                const lastStep = statsData.step_by_step[statsData.step_by_step.length - 1];
                
                info.timeSpan = {
                    start: firstStep.year,
                    end: lastStep.year,
                    steps: statsData.step_by_step.length
                };
            }
        }
        
        // 获取目录创建时间
        const stats = await fs.stat(dirPath);
        info.createdAt = stats.birthtime;
        
        return info;
    } catch (error) {
        console.error('Error reading simulation info:', error);
        return null;
    }
}

function findSimulationPath(id) {
    const possiblePaths = [
        path.join(DATA_DIR, id),
        path.join(REAL_DATA_DIR, id),
        path.join(DATA_DIR, id + '_results'),
        path.join(REAL_DATA_DIR, id + '_results')
    ];
    
    for (const path_ of possiblePaths) {
        if (fs.existsSync(path_)) {
            return path_;
        }
    }
    
    return null;
}

async function loadSimulationData(simulationPath) {
    try {
        const data = {
            id: path.basename(simulationPath),
            path: simulationPath,
            statistics: null,
            agents: {},
            configuration: {}
        };
        
        // 加载统计数据
        const statsPath = path.join(simulationPath, 'realistic_simulation_statistics.json');
        if (await fs.pathExists(statsPath)) {
            data.statistics = await fs.readJson(statsPath);
        }
        
        // 加载配置信息
        const configPath = path.join(simulationPath, 'simulation_config.json');
        if (await fs.pathExists(configPath)) {
            data.configuration = await fs.readJson(configPath);
        }
        
        // 加载智能体统计
        const agentStatsPath = path.join(simulationPath, 'agent_statistics.json');
        if (await fs.pathExists(agentStatsPath)) {
            data.agents = await fs.readJson(agentStatsPath);
        }
        
        return data;
    } catch (error) {
        console.error('Error loading simulation data:', error);
        throw error;
    }
}

async function loadSimulationStatistics(simulationPath) {
    try {
        const statsPath = path.join(simulationPath, 'realistic_simulation_statistics.json');
        
        if (await fs.pathExists(statsPath)) {
            return await fs.readJson(statsPath);
        }
        
        return null;
    } catch (error) {
        console.error('Error loading simulation statistics:', error);
        throw error;
    }
}

async function loadTimeSeriesData(simulationPath) {
    try {
        const timeseries = [];
        const statsPath = path.join(simulationPath, 'realistic_simulation_statistics.json');
        
        if (await fs.pathExists(statsPath)) {
            const stats = await fs.readJson(statsPath);
            
            if (stats.step_by_step) {
                timeseries.push(...stats.step_by_step);
            }
        }
        
        // 也尝试加载分步统计文件
        const files = await fs.readdir(simulationPath);
        const stepFiles = files.filter(file => file.startsWith('realistic_statistics_step_') && file.endsWith('.json'));
        
        for (const file of stepFiles) {
            const filePath = path.join(simulationPath, file);
            const stepData = await fs.readJson(filePath);
            timeseries.push(stepData);
        }
        
        // 按步骤排序
        timeseries.sort((a, b) => a.step - b.step);
        
        return timeseries;
    } catch (error) {
        console.error('Error loading timeseries data:', error);
        throw error;
    }
}

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 Qiaopi Simulation Web Server running on port ${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}`);
    console.log(`📈 API: http://localhost:${PORT}/api`);
    console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
    console.log(`🧪 CSP Test: http://localhost:${PORT}/test-csp`);
});

module.exports = app;