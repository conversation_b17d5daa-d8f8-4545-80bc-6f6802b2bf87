"""
实时预测系统模块
Real-time Prediction System Module

提供多时间尺度的预测功能，专门用于侨批网络的动态预测分析：
- 多尺度时间序列预测
- 不确定性量化
- 情景分析和敏感性分析
- 实时数据流处理
- 预测模型集成

主要组件：
- MultiScalePredictor: 多尺度预测器
- UncertaintyQuantifier: 不确定性量化器
- ScenarioAnalyzer: 情景分析器
- RealTimePredictor: 实时预测器
- EnsemblePredictor: 集成预测器
- PredictionMonitor: 预测监控器
"""

from .time_series_models import (
    MultiScalePredictor,
    TransformerPredictor,
    LSTMPredictor,
    PredictionEvaluator
)

# 其他模块将在后续版本中实现
# from .uncertainty_quantification import UncertaintyQuantifier
# from .scenario_analysis import ScenarioAnalyzer
# from .real_time_engine import RealTimePredictor
# from .ensemble_methods import EnsemblePredictor
# from .prediction_monitor import PredictionMonitor
# from .utils import DataPreprocessor

__version__ = "1.0.0"
__author__ = "Qiaopi Research Team"

__all__ = [
    # 时间序列模型
    "MultiScalePredictor",
    "TransformerPredictor", 
    "LSTMPredictor",
    "PredictionEvaluator",
]
