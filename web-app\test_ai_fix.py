#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI仿真修复
"""

import os
import sys
import subprocess

def test_imports():
    """测试所有必需模块的导入"""
    print("🔬 Testing module imports...")
    
    # 切换到正确目录
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        # 测试基础模块
        from environment import EventType
        print(f"  ✅ EventType imported - available events: {[e.name for e in EventType]}")
        
        from simulation_engine import QiaopiSimulationEngine, SimulationConfig
        print("  ✅ simulation_engine imported")
        
        from agents import MigrantAgent, FamilyAgent, InstitutionAgent
        print("  ✅ agents imported")
        
        # 测试增强模块
        try:
            from enhanced_simulation_engine import EnhancedQiaopiSimulationEngine, EnhancedAnalysisConfig
            print("  ✅ enhanced_simulation_engine imported")
            return True, True
        except ImportError as e:
            print(f"  ⚠️ enhanced_simulation_engine not available: {e}")
            return True, False
            
    except ImportError as e:
        print(f"  ❌ Import failed: {e}")
        return False, False
    finally:
        os.chdir(original_dir)

def test_basic_ai_simulation():
    """测试基础AI仿真（快速版本）"""
    print("\n🤖 Testing basic AI simulation...")
    
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        from simulation_engine import QiaopiSimulationEngine, SimulationConfig
        
        # 极小规模快速测试
        config = SimulationConfig(
            start_year=1920,
            end_year=1921,  # 只运行1年
            steps_per_year=4,  # 季度数据
            num_migrants=10,
            num_families=10,
            num_institutions=2,
            output_directory='quick_test_results'
        )
        
        print(f"  📊 Quick test: {config.num_migrants} migrants, {config.num_families} families")
        print(f"  ⏱️ Period: {config.start_year}-{config.end_year} ({config.steps_per_year} steps/year)")
        
        # 运行仿真
        engine = QiaopiSimulationEngine(config)
        print("  🔄 Running simulation...")
        results = engine.run_simulation()
        
        # 验证结果
        stats = results.get('statistics', [])
        agents = results.get('agents', {})
        
        print(f"  ✅ Simulation completed")
        print(f"  📈 Statistics entries: {len(stats)}")
        print(f"  🤖 Agent categories: {list(agents.keys())}")
        
        total_agents = sum(len(agent_list) for agent_list in agents.values())
        print(f"  👥 Total AI agents created: {total_agents}")
        
        if len(stats) > 0:
            last_stat = stats[-1]
            print(f"  💰 Final total amount: {last_stat.get('total_amount', 'N/A')}")
            print(f"  📊 Final success rate: {last_stat.get('success_rate', 'N/A')}")
        
        return True, results
        
    except Exception as e:
        print(f"  ❌ Basic simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        os.chdir(original_dir)

def test_enhanced_ai_simulation():
    """测试增强AI仿真"""
    print("\n🚀 Testing enhanced AI simulation...")
    
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        from enhanced_simulation_engine import EnhancedQiaopiSimulationEngine, EnhancedAnalysisConfig
        from simulation_engine import SimulationConfig
        
        # 小规模测试配置
        sim_config = SimulationConfig(
            start_year=1920,
            end_year=1922,
            steps_per_year=4,
            num_migrants=15,
            num_families=15,
            num_institutions=3,
            output_directory='test_enhanced_ai_results'
        )
        
        analysis_config = EnhancedAnalysisConfig(
            enable_multi_timeseries=True,
            enable_clustering_analysis=True,
            enable_prediction_analysis=True,
            enable_network_analysis=True
        )
        
        print(f"  📊 Enhanced test: {sim_config.num_migrants} migrants with advanced analysis")
        
        # 运行增强仿真
        engine = EnhancedQiaopiSimulationEngine(sim_config, analysis_config)
        print("  🧠 Running enhanced AI simulation...")
        results = engine.run_enhanced_simulation()
        
        print("  ✅ Enhanced AI simulation completed")
        
        # 验证增强分析结果
        analysis_modules = [
            'multi_timeseries_analysis',
            'trend_analysis', 
            'clustering_analysis',
            'prediction_analysis',
            'network_topology_analysis'
        ]
        
        for module in analysis_modules:
            if module in results:
                print(f"    ✅ {module}: Available")
            else:
                print(f"    ⚠️ {module}: Missing")
        
        return True, results
        
    except Exception as e:
        print(f"  ❌ Enhanced AI simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        os.chdir(original_dir)

def test_script_execution():
    """测试脚本执行"""
    print("\n📜 Testing script execution...")
    
    parent_dir = os.path.dirname(os.getcwd())
    script_path = os.path.join(parent_dir, 'run_real_enhanced_simulation.py')
    
    if not os.path.exists(script_path):
        print(f"  ❌ Script not found: {script_path}")
        return False
    
    print(f"  📍 Script found: {script_path}")
    
    try:
        # 运行快速测试
        cmd = [
            sys.executable, script_path,
            '--start-year', '1920',
            '--end-year', '1921',
            '--migrants', '10',
            '--families', '10',
            '--institutions', '2',
            '--output-dir', 'quick_ai_test_results'
        ]
        
        print(f"  💻 Running: {os.path.basename(cmd[1])} with minimal parameters")
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                              timeout=120, cwd=parent_dir)
        
        print(f"  📊 Exit code: {result.returncode}")
        
        if result.stdout:
            stdout_lines = [line for line in result.stdout.split('\n') if line.strip()]
            if stdout_lines:
                print(f"  📄 Output (first 5 lines):")
                for line in stdout_lines[:5]:
                    print(f"    {line}")
                if len(stdout_lines) > 5:
                    print(f"    ... ({len(stdout_lines)} total lines)")
        
        if result.stderr:
            stderr_lines = [line for line in result.stderr.split('\n') if line.strip()]
            if stderr_lines:
                print(f"  ⚠️ Errors:")
                for line in stderr_lines[:3]:
                    print(f"    {line}")
        
        # 检查生成的文件
        results_dir = os.path.join(parent_dir, 'quick_ai_test_results')
        if os.path.exists(results_dir):
            files = os.listdir(results_dir)
            print(f"  📂 Generated {len(files)} files: {files}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("  ⏰ Script timeout (120s)")
        return False
    except Exception as e:
        print(f"  ❌ Script test failed: {e}")
        return False

def main():
    print("🎯 AI Simulation Fix Verification")
    print("=" * 40)
    
    # 1. 测试导入
    imports_ok, enhanced_available = test_imports()
    
    # 2. 测试基础AI仿真
    basic_ok, basic_results = test_basic_ai_simulation()
    
    # 3. 测试增强AI仿真（如果可用）
    enhanced_ok = False
    if enhanced_available:
        enhanced_ok, enhanced_results = test_enhanced_ai_simulation()
    
    # 4. 测试脚本执行
    script_ok = test_script_execution()
    
    print("\n" + "=" * 40)
    print("📊 Final Results:")
    print(f"  Basic Modules: {'✅ WORKING' if imports_ok else '❌ BROKEN'}")
    print(f"  Enhanced Modules: {'✅ AVAILABLE' if enhanced_available else '❌ NOT AVAILABLE'}")
    print(f"  Basic AI Simulation: {'✅ WORKING' if basic_ok else '❌ BROKEN'}")
    print(f"  Enhanced AI Simulation: {'✅ WORKING' if enhanced_ok else ('⚠️ NOT TESTED' if not enhanced_available else '❌ BROKEN')}")
    print(f"  Script Execution: {'✅ WORKING' if script_ok else '❌ BROKEN'}")
    
    if script_ok or (basic_ok and enhanced_available):
        print("\n🎉 AI SIMULATION IS READY!")
        print("🚀 You can now run real AI agent enhanced simulation!")
        print("🌐 Start server: node server.js")
        print("📊 Visit: http://localhost:3508/enhanced-simulation.html")
    else:
        print("\n❌ AI simulation needs more fixes")
        
        if not imports_ok:
            print("  🔧 Fix module import issues first")
        elif not basic_ok:
            print("  🔧 Fix basic simulation engine issues")
        elif enhanced_available and not enhanced_ok:
            print("  🔧 Fix enhanced simulation engine issues")
        else:
            print("  🔧 Fix script execution issues")
    
    return script_ok or (basic_ok and enhanced_available)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)