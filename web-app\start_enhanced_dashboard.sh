#!/bin/bash

echo "🚀 启动增强版侨批仿真Dashboard"
echo "================================"

echo "📍 切换到web-app目录..."
cd web-app

echo "🔧 检查Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js"
    echo "💡 请先安装Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js检查通过 - $(node --version)"

echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "📥 安装依赖包..."
    npm install
fi

echo "🚀 启动Web服务器..."
echo ""
echo "🌐 Dashboard将在以下地址启动:"
echo "   主页面: http://localhost:3508"
echo "   增强仿真: http://localhost:3508/enhanced-simulation.html"
echo ""
echo "💡 按Ctrl+C停止服务器"
echo ""

node server.js