"""
深度强化学习智能体实现
Deep Reinforcement Learning Agents Implementation

基于PyTorch和Stable Baselines3实现的智能体系统，具备：
- 深度Q学习 (DQN)
- 策略梯度算法 (PPO, A2C)
- 演员-评论家方法 (SAC)
- 多智能体协作学习
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import gymnasium as gym
from stable_baselines3 import PPO, SAC, DQN
from stable_baselines3.common.policies import ActorCriticPolicy
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class AgentState:
    """智能体状态数据类"""
    agent_id: str
    agent_type: str
    economic_state: Dict[str, float]
    social_state: Dict[str, float]
    environmental_context: Dict[str, float]
    historical_actions: List[Dict]
    timestamp: int


class AdvancedFeaturesExtractor(BaseFeaturesExtractor):
    """高级特征提取器"""
    
    def __init__(self, observation_space: gym.spaces.Box, features_dim: int = 256):
        super().__init__(observation_space, features_dim)
        
        self.input_dim = observation_space.shape[0]
        
        # 多层感知机特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(self.input_dim, 512),
            nn.ReLU(),
            nn.BatchNorm1d(512),
            nn.Dropout(0.2),
            
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.BatchNorm1d(256),
            nn.Dropout(0.2),
            
            nn.Linear(256, features_dim),
            nn.ReLU()
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=features_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
    
    def forward(self, observations: torch.Tensor) -> torch.Tensor:
        # 特征提取
        features = self.feature_extractor(observations)
        
        # 应用注意力机制
        if len(features.shape) == 2:
            features = features.unsqueeze(1)  # 添加序列维度
        
        attended_features, _ = self.attention(features, features, features)
        
        return attended_features.squeeze(1)  # 移除序列维度


class DeepRLMigrantAgent:
    """基于深度强化学习的移民智能体"""
    
    def __init__(
        self,
        agent_id: str,
        state_dim: int = 30,
        action_dim: int = 10,
        learning_algorithm: str = "PPO",
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        self.agent_id = agent_id
        self.device = device
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 创建观测和动作空间
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(state_dim,), dtype=np.float32
        )
        
        self.action_space = gym.spaces.Discrete(action_dim)
        
        # 初始化强化学习算法
        self._initialize_rl_algorithm(learning_algorithm)
        
        # 状态历史和经验缓冲
        self.state_history = []
        self.action_history = []
        self.reward_history = []
        
        # 个人特征
        self.personal_traits = {
            'risk_tolerance': np.random.uniform(0.2, 0.8),
            'family_obligation': np.random.uniform(0.5, 0.9),
            'learning_rate': np.random.uniform(0.3, 0.7),
            'memory_capacity': np.random.randint(50, 200)
        }
        
        logger.info(f"初始化移民智能体 {agent_id}，使用 {learning_algorithm} 算法")
    
    def _initialize_rl_algorithm(self, algorithm: str):
        """初始化强化学习算法"""
        policy_kwargs = {
            "features_extractor_class": AdvancedFeaturesExtractor,
            "features_extractor_kwargs": {"features_dim": 256},
            "net_arch": [256, 128]  # 策略和价值网络架构
        }
        
        if algorithm == "PPO":
            self.model = PPO(
                "MlpPolicy",
                env=None,  # 将在训练时设置
                learning_rate=3e-4,
                n_steps=2048,
                batch_size=64,
                n_epochs=10,
                gamma=0.99,
                gae_lambda=0.95,
                clip_range=0.2,
                ent_coef=0.01,
                policy_kwargs=policy_kwargs,
                verbose=1,
                device=self.device
            )
        elif algorithm == "SAC":
            self.model = SAC(
                "MlpPolicy",
                env=None,
                learning_rate=3e-4,
                buffer_size=1000000,
                learning_starts=100,
                batch_size=256,
                tau=0.005,
                gamma=0.99,
                ent_coef="auto",
                policy_kwargs=policy_kwargs,
                verbose=1,
                device=self.device
            )
        elif algorithm == "DQN":
            self.model = DQN(
                "MlpPolicy",
                env=None,
                learning_rate=1e-4,
                buffer_size=1000000,
                learning_starts=50000,
                batch_size=32,
                target_update_interval=10000,
                train_freq=4,
                gamma=0.99,
                exploration_final_eps=0.1,
                exploration_fraction=0.1,
                policy_kwargs=policy_kwargs,
                verbose=1,
                device=self.device
            )
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
    
    def encode_state(self, raw_state: Dict[str, Any]) -> np.ndarray:
        """将原始状态编码为神经网络输入"""
        state_vector = []
        
        # 经济状态编码
        economic_features = [
            raw_state.get('income_level', 0.0),
            raw_state.get('savings', 0.0),
            raw_state.get('monthly_expenses', 0.0),
            raw_state.get('debt_level', 0.0),
            raw_state.get('years_abroad', 0.0)
        ]
        state_vector.extend(economic_features)
        
        # 社会状态编码
        social_features = [
            raw_state.get('social_capital', 0.0),
            raw_state.get('network_size', 0.0),
            raw_state.get('reputation_score', 0.0),
            raw_state.get('cultural_adaptation', 0.0)
        ]
        state_vector.extend(social_features)
        
        # 家庭状态编码
        family_features = [
            raw_state.get('family_urgent_need', 0.0),
            raw_state.get('family_economic_status', 0.0),
            raw_state.get('last_remittance_time', 0.0),
            raw_state.get('family_satisfaction', 0.0)
        ]
        state_vector.extend(family_features)
        
        # 环境状态编码
        env_features = [
            raw_state.get('economic_growth_rate', 0.0),
            raw_state.get('political_stability', 0.0),
            raw_state.get('exchange_rate', 1.0),
            raw_state.get('communication_cost', 0.0),
            raw_state.get('transportation_cost', 0.0)
        ]
        state_vector.extend(env_features)
        
        # 历史行为编码（简化版）
        historical_features = [
            raw_state.get('avg_remittance_amount', 0.0),
            raw_state.get('remittance_frequency', 0.0),
            raw_state.get('success_rate', 0.0),
            raw_state.get('preferred_institution', 0.0)
        ]
        state_vector.extend(historical_features)
        
        # 个人特征编码
        personal_features = [
            self.personal_traits['risk_tolerance'],
            self.personal_traits['family_obligation'],
            self.personal_traits['learning_rate']
        ]
        state_vector.extend(personal_features)
        
        # 填充到固定长度
        while len(state_vector) < self.state_dim:
            state_vector.append(0.0)
        
        return np.array(state_vector[:self.state_dim], dtype=np.float32)
    
    def decode_action(self, action: int) -> Dict[str, Any]:
        """将动作解码为具体的行为"""
        action_mapping = {
            0: {"type": "no_remittance", "amount": 0.0, "institution": None},
            1: {"type": "small_remittance", "amount": "low", "institution": "trusted"},
            2: {"type": "medium_remittance", "amount": "medium", "institution": "trusted"},
            3: {"type": "large_remittance", "amount": "high", "institution": "trusted"},
            4: {"type": "emergency_remittance", "amount": "emergency", "institution": "fastest"},
            5: {"type": "investment_remittance", "amount": "investment", "institution": "cheapest"},
            6: {"type": "festival_remittance", "amount": "festival", "institution": "traditional"},
            7: {"type": "change_institution", "amount": "previous", "institution": "alternative"},
            8: {"type": "explore_new_institution", "amount": "small", "institution": "new"},
            9: {"type": "wait_and_observe", "amount": 0.0, "institution": None}
        }
        
        return action_mapping.get(action, action_mapping[0])
    
    def make_decision(self, state: Dict[str, Any], training: bool = False) -> Tuple[Dict[str, Any], float]:
        """基于当前状态做出决策"""
        # 编码状态
        encoded_state = self.encode_state(state)
        
        # 使用模型预测动作
        if training and hasattr(self.model, 'predict'):
            action, _ = self.model.predict(encoded_state, deterministic=False)
        else:
            # 如果模型还未训练，使用随机策略
            action = self.action_space.sample()
        
        # 解码动作
        decoded_action = self.decode_action(action)
        
        # 计算动作价值（如果可用）
        action_value = 0.0
        if hasattr(self.model, 'policy') and self.model.policy is not None:
            with torch.no_grad():
                obs_tensor = torch.FloatTensor(encoded_state).unsqueeze(0).to(self.device)
                if hasattr(self.model.policy, 'evaluate_actions'):
                    action_tensor = torch.LongTensor([action]).to(self.device)
                    value, _, _ = self.model.policy.evaluate_actions(obs_tensor, action_tensor)
                    action_value = value.item()
        
        # 记录历史
        self.state_history.append(encoded_state)
        self.action_history.append(action)
        
        return decoded_action, action_value
    
    def learn_from_experience(self, reward: float, next_state: Dict[str, Any], done: bool):
        """从经验中学习"""
        self.reward_history.append(reward)
        
        # 如果有足够的经验，进行学习更新
        if len(self.reward_history) >= 32:  # 批量大小
            # 这里可以实现自定义的学习逻辑
            # 或者依赖于Stable Baselines3的自动学习
            pass
    
    def save_model(self, filepath: str):
        """保存模型"""
        if hasattr(self.model, 'save'):
            self.model.save(filepath)
            logger.info(f"模型已保存到 {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        if hasattr(self.model, 'load'):
            self.model = self.model.load(filepath, device=self.device)
            logger.info(f"模型已从 {filepath} 加载")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取智能体统计信息"""
        return {
            'agent_id': self.agent_id,
            'total_decisions': len(self.action_history),
            'average_reward': np.mean(self.reward_history) if self.reward_history else 0.0,
            'action_distribution': {
                str(i): self.action_history.count(i) for i in range(self.action_dim)
            },
            'personal_traits': self.personal_traits
        }


class DeepRLFamilyAgent:
    """基于深度强化学习的家庭智能体"""
    
    def __init__(
        self,
        agent_id: str,
        state_dim: int = 25,
        action_dim: int = 8,
        learning_algorithm: str = "PPO"
    ):
        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 创建观测和动作空间
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(state_dim,), dtype=np.float32
        )
        self.action_space = gym.spaces.Discrete(action_dim)
        
        # 初始化强化学习算法
        self._initialize_rl_algorithm(learning_algorithm)
        
        # 家庭特征
        self.family_traits = {
            'risk_aversion': np.random.uniform(0.1, 0.9),
            'education_preference': np.random.uniform(0.3, 0.8),
            'investment_tendency': np.random.uniform(0.2, 0.7),
            'tradition_value': np.random.uniform(0.4, 0.9)
        }
        
        logger.info(f"初始化家庭智能体 {agent_id}")
    
    def _initialize_rl_algorithm(self, algorithm: str):
        """初始化强化学习算法"""
        # 类似于移民智能体的实现
        policy_kwargs = {
            "features_extractor_class": AdvancedFeaturesExtractor,
            "features_extractor_kwargs": {"features_dim": 128},
            "net_arch": [128, 64]
        }
        
        if algorithm == "PPO":
            self.model = PPO(
                "MlpPolicy",
                env=None,
                learning_rate=3e-4,
                policy_kwargs=policy_kwargs,
                verbose=0
            )
    
    def encode_state(self, raw_state: Dict[str, Any]) -> np.ndarray:
        """编码家庭状态"""
        state_vector = []
        
        # 经济状态
        economic_features = [
            raw_state.get('cash_reserves', 0.0),
            raw_state.get('monthly_income', 0.0),
            raw_state.get('monthly_expenses', 0.0),
            raw_state.get('debt_level', 0.0),
            raw_state.get('asset_value', 0.0)
        ]
        state_vector.extend(economic_features)
        
        # 家庭状态
        family_features = [
            raw_state.get('family_size', 0.0),
            raw_state.get('dependency_ratio', 0.0),
            raw_state.get('education_needs', 0.0),
            raw_state.get('health_expenses', 0.0),
            raw_state.get('urgent_need_level', 0.0)
        ]
        state_vector.extend(family_features)
        
        # 社会状态
        social_features = [
            raw_state.get('social_status', 0.0),
            raw_state.get('community_involvement', 0.0),
            raw_state.get('cultural_events', 0.0)
        ]
        state_vector.extend(social_features)
        
        # 汇款历史
        remittance_features = [
            raw_state.get('last_remittance_amount', 0.0),
            raw_state.get('remittance_frequency', 0.0),
            raw_state.get('total_received', 0.0),
            raw_state.get('sender_reliability', 0.0)
        ]
        state_vector.extend(remittance_features)
        
        # 家庭特征
        personal_features = list(self.family_traits.values())
        state_vector.extend(personal_features)
        
        # 填充到固定长度
        while len(state_vector) < self.state_dim:
            state_vector.append(0.0)
        
        return np.array(state_vector[:self.state_dim], dtype=np.float32)
    
    def decode_action(self, action: int) -> Dict[str, Any]:
        """解码家庭行为"""
        action_mapping = {
            0: {"type": "save_money", "allocation": {"savings": 0.8, "consumption": 0.2}},
            1: {"type": "invest_education", "allocation": {"education": 0.6, "other": 0.4}},
            2: {"type": "invest_property", "allocation": {"property": 0.7, "other": 0.3}},
            3: {"type": "start_business", "allocation": {"business": 0.8, "reserve": 0.2}},
            4: {"type": "emergency_use", "allocation": {"emergency": 1.0}},
            5: {"type": "festival_celebration", "allocation": {"festival": 0.5, "other": 0.5}},
            6: {"type": "community_investment", "allocation": {"community": 0.4, "family": 0.6}},
            7: {"type": "request_more_remittance", "urgency": "high"}
        }
        
        return action_mapping.get(action, action_mapping[0])
    
    def make_decision(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """做出家庭决策"""
        encoded_state = self.encode_state(state)
        
        # 使用模型预测（如果已训练）
        if hasattr(self.model, 'predict'):
            action, _ = self.model.predict(encoded_state, deterministic=False)
        else:
            action = self.action_space.sample()
        
        return self.decode_action(action)


class DeepRLInstitutionAgent:
    """基于深度强化学习的机构智能体"""
    
    def __init__(
        self,
        agent_id: str,
        state_dim: int = 35,
        action_dim: int = 12,
        learning_algorithm: str = "PPO"
    ):
        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 创建观测和动作空间
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(state_dim,), dtype=np.float32
        )
        self.action_space = gym.spaces.Discrete(action_dim)
        
        # 初始化强化学习算法
        self._initialize_rl_algorithm(learning_algorithm)
        
        # 机构特征
        self.institution_traits = {
            'service_quality_focus': np.random.uniform(0.3, 0.9),
            'profit_optimization': np.random.uniform(0.4, 0.8),
            'risk_management': np.random.uniform(0.5, 0.9),
            'innovation_tendency': np.random.uniform(0.2, 0.7),
            'customer_relationship': np.random.uniform(0.4, 0.9)
        }
        
        logger.info(f"初始化机构智能体 {agent_id}")
    
    def _initialize_rl_algorithm(self, algorithm: str):
        """初始化强化学习算法"""
        policy_kwargs = {
            "features_extractor_class": AdvancedFeaturesExtractor,
            "features_extractor_kwargs": {"features_dim": 256},
            "net_arch": [256, 128, 64]
        }
        
        if algorithm == "PPO":
            self.model = PPO(
                "MlpPolicy",
                env=None,
                learning_rate=3e-4,
                policy_kwargs=policy_kwargs,
                verbose=0
            )
    
    def encode_state(self, raw_state: Dict[str, Any]) -> np.ndarray:
        """编码机构状态"""
        state_vector = []
        
        # 财务状态
        financial_features = [
            raw_state.get('liquidity', 0.0),
            raw_state.get('revenue', 0.0),
            raw_state.get('operational_cost', 0.0),
            raw_state.get('profit_margin', 0.0),
            raw_state.get('debt_ratio', 0.0)
        ]
        state_vector.extend(financial_features)
        
        # 运营状态
        operational_features = [
            raw_state.get('transaction_volume', 0.0),
            raw_state.get('success_rate', 0.0),
            raw_state.get('processing_speed', 0.0),
            raw_state.get('customer_satisfaction', 0.0),
            raw_state.get('market_share', 0.0)
        ]
        state_vector.extend(operational_features)
        
        # 风险状态
        risk_features = [
            raw_state.get('political_risk', 0.0),
            raw_state.get('economic_risk', 0.0),
            raw_state.get('operational_risk', 0.0),
            raw_state.get('regulatory_risk', 0.0),
            raw_state.get('reputation_risk', 0.0)
        ]
        state_vector.extend(risk_features)
        
        # 竞争状态
        competitive_features = [
            raw_state.get('competitor_count', 0.0),
            raw_state.get('price_competitiveness', 0.0),
            raw_state.get('service_differentiation', 0.0),
            raw_state.get('technology_advantage', 0.0)
        ]
        state_vector.extend(competitive_features)
        
        # 网络状态
        network_features = [
            raw_state.get('branch_count', 0.0),
            raw_state.get('geographic_coverage', 0.0),
            raw_state.get('partner_network_size', 0.0),
            raw_state.get('technology_infrastructure', 0.0)
        ]
        state_vector.extend(network_features)
        
        # 机构特征
        institutional_features = list(self.institution_traits.values())
        state_vector.extend(institutional_features)
        
        # 填充到固定长度
        while len(state_vector) < self.state_dim:
            state_vector.append(0.0)
        
        return np.array(state_vector[:self.state_dim], dtype=np.float32)
    
    def decode_action(self, action: int) -> Dict[str, Any]:
        """解码机构行为"""
        action_mapping = {
            0: {"type": "maintain_status", "changes": {}},
            1: {"type": "reduce_fees", "fee_change": -0.05, "target": "attract_customers"},
            2: {"type": "increase_fees", "fee_change": 0.03, "target": "increase_profit"},
            3: {"type": "improve_service", "investment": "service_quality", "amount": 0.1},
            4: {"type": "expand_network", "investment": "infrastructure", "amount": 0.15},
            5: {"type": "enhance_security", "investment": "security", "amount": 0.08},
            6: {"type": "marketing_campaign", "investment": "marketing", "amount": 0.05},
            7: {"type": "technology_upgrade", "investment": "technology", "amount": 0.12},
            8: {"type": "risk_management", "focus": "risk_reduction", "intensity": "high"},
            9: {"type": "partnership", "action": "form_alliance", "scope": "regional"},
            10: {"type": "differentiation", "strategy": "premium_service", "target_segment": "high_value"},
            11: {"type": "cost_optimization", "focus": "operational_efficiency", "target_saving": 0.07}
        }
        
        return action_mapping.get(action, action_mapping[0])
    
    def make_decision(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """做出机构决策"""
        encoded_state = self.encode_state(state)
        
        # 使用模型预测
        if hasattr(self.model, 'predict'):
            action, _ = self.model.predict(encoded_state, deterministic=False)
        else:
            action = self.action_space.sample()
        
        return self.decode_action(action)


def create_agent_ensemble(
    num_migrants: int = 100,
    num_families: int = 80,
    num_institutions: int = 10,
    algorithm: str = "PPO"
) -> Dict[str, List]:
    """创建智能体集合"""
    
    agents = {
        'migrants': [],
        'families': [],
        'institutions': []
    }
    
    # 创建移民智能体
    for i in range(num_migrants):
        agent = DeepRLMigrantAgent(f"migrant_{i}", learning_algorithm=algorithm)
        agents['migrants'].append(agent)
    
    # 创建家庭智能体
    for i in range(num_families):
        agent = DeepRLFamilyAgent(f"family_{i}", learning_algorithm=algorithm)
        agents['families'].append(agent)
    
    # 创建机构智能体
    for i in range(num_institutions):
        agent = DeepRLInstitutionAgent(f"institution_{i}", learning_algorithm=algorithm)
        agents['institutions'].append(agent)
    
    logger.info(f"创建了智能体集合：{num_migrants}个移民，{num_families}个家庭，{num_institutions}个机构")
    
    return agents


if __name__ == "__main__":
    # 测试智能体创建
    migrant = DeepRLMigrantAgent("test_migrant")
    
    # 模拟状态
    test_state = {
        'income_level': 100.0,
        'savings': 500.0,
        'family_urgent_need': 0.3,
        'political_stability': 0.8
    }
    
    # 测试决策
    action, value = migrant.make_decision(test_state)
    print(f"智能体决策: {action}")
    print(f"动作价值: {value}")
    
    # 获取统计信息
    stats = migrant.get_statistics()
    print(f"智能体统计: {stats}")
