// 立即生成演示数据的脚本
const fs = require('fs-extra');
const path = require('path');

async function createEnhancedResults() {
    const outputDir = path.resolve(__dirname, '..', 'enhanced_results');
    
    console.log('🚀 Creating enhanced simulation demo data...');
    console.log(`📁 Output directory: ${outputDir}`);
    
    try {
        // 确保目录存在
        await fs.ensureDir(outputDir);
        
        const timestamp = new Date().toISOString();
        
        // 创建增强分析结果
        const enhancedAnalysis = {
            "generated_at": timestamp,
            "simulation_status": "completed_with_demo_data",
            "note": "This is demonstration data for the enhanced simulation interface",
            "multi_timeseries_analysis": {
                "overall_series": {
                    "length": 20,
                    "time_range": ["1920", "1940"]
                },
                "regional_series": {
                    "guangdong": {
                        "economic_resilience": 0.75,
                        "remittance_volatility": 0.25,
                        "series_data": Array.from({length: 21}, (_, i) => ({
                            year: 1920 + i,
                            step: i,
                            flow_count: 100 + i * 5,
                            flow_amount: 1000 + i * 50,
                            success_rate: 0.85 + Math.random() * 0.1,
                            total_savings: 5000 + i * 200,
                            avg_income: 300 + i * 10
                        }))
                    },
                    "fujian": {
                        "economic_resilience": 0.70,
                        "remittance_volatility": 0.30,
                        "series_data": Array.from({length: 21}, (_, i) => ({
                            year: 1920 + i,
                            step: i,
                            flow_count: 80 + i * 3,
                            flow_amount: 800 + i * 30,
                            success_rate: 0.80 + Math.random() * 0.15,
                            total_savings: 4000 + i * 150,
                            avg_income: 250 + i * 8
                        }))
                    },
                    "jiangsu": {
                        "economic_resilience": 0.68,
                        "remittance_volatility": 0.35,
                        "series_data": Array.from({length: 21}, (_, i) => ({
                            year: 1920 + i,
                            step: i,
                            flow_count: 60 + i * 4,
                            flow_amount: 600 + i * 40,
                            success_rate: 0.78 + Math.random() * 0.18,
                            total_savings: 3500 + i * 120,
                            avg_income: 220 + i * 6
                        }))
                    }
                },
                "currency_series": {
                    "usd": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 120 + i * 6,
                        flow_amount: 1200 + i * 60,
                        success_rate: 0.90 + Math.random() * 0.08,
                        total_savings: 6000 + i * 300,
                        avg_income: 400 + i * 15
                    })),
                    "cny": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 90 + i * 4,
                        flow_amount: 900 + i * 40,
                        success_rate: 0.85 + Math.random() * 0.12,
                        total_savings: 4500 + i * 200,
                        avg_income: 300 + i * 10
                    })),
                    "hkd": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 70 + i * 3,
                        flow_amount: 700 + i * 35,
                        success_rate: 0.88 + Math.random() * 0.10,
                        total_savings: 3500 + i * 175,
                        avg_income: 280 + i * 8
                    }))
                },
                "institution_series": {
                    "traditional_banks": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 150 + i * 8,
                        flow_amount: 1500 + i * 80,
                        success_rate: 0.92 + Math.random() * 0.06
                    })),
                    "qiaopi_networks": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 100 + i * 5,
                        flow_amount: 1000 + i * 50,
                        success_rate: 0.85 + Math.random() * 0.12
                    })),
                    "informal_channels": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 50 + i * 2,
                        flow_amount: 500 + i * 25,
                        success_rate: 0.75 + Math.random() * 0.18
                    }))
                },
                "event_series": {
                    "economic_boom": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        impact_factor: i > 10 ? 1.2 + Math.random() * 0.3 : 1.0 + Math.random() * 0.2
                    })),
                    "political_stability": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        impact_factor: 0.8 + Math.random() * 0.4
                    }))
                },
                "corridor_series": {
                    "southeast_asia_guangdong": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 80 + i * 4,
                        flow_amount: 800 + i * 40,
                        success_rate: 0.87 + Math.random() * 0.10
                    })),
                    "americas_fujian": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 60 + i * 3,
                        flow_amount: 600 + i * 30,
                        success_rate: 0.82 + Math.random() * 0.15
                    }))
                }
            },
            "trend_analysis": {
                "overall_trends": {
                    "remittance_volume": "increasing",
                    "success_rate": "stable_with_fluctuation",
                    "network_density": "growing",
                    "regional_disparity": "decreasing"
                },
                "seasonal_patterns": {
                    "spring_festival_effect": 1.3,
                    "harvest_season_effect": 1.1,
                    "new_year_effect": 1.2
                }
            },
            "correlation_analysis": {
                "remittance_success_correlation": 0.85,
                "economic_event_impact": 0.65,
                "regional_differences": 0.72,
                "currency_stability_correlation": 0.78,
                "institution_reliability_correlation": 0.88
            },
            "clustering_analysis": {
                "migrant_behavior_clusters": {
                    "cluster_profiles": {
                        "conservative_savers": {
                            "size": 120,
                            "description": "Low-risk high-savings group",
                            "characteristics": ["Low risk preference", "Regular remittance", "Family-oriented"],
                            "avg_remittance_frequency": 4,
                            "avg_amount": 500,
                            "success_rate": 0.92
                        },
                        "risk_takers": {
                            "size": 80,
                            "description": "High-risk investment preference group", 
                            "characteristics": ["Investment-oriented", "Larger amounts", "Lower frequency"],
                            "avg_remittance_frequency": 2,
                            "avg_amount": 1200,
                            "success_rate": 0.85
                        },
                        "family_focused": {
                            "size": 100,
                            "description": "Family-oriented remittance group",
                            "characteristics": ["Regular small amounts", "High stability", "Emotion-driven"],
                            "avg_remittance_frequency": 6,
                            "avg_amount": 300,
                            "success_rate": 0.95
                        }
                    }
                }
            },
            "prediction_analysis": {
                "5_year_forecast": {
                    "future_years": [1941, 1942, 1943, 1944, 1945],
                    "predicted_savings": [15000, 16500, 18000, 19500, 21000],
                    "predicted_volume": [2500, 2750, 3000, 3250, 3500],
                    "confidence_level": 0.85,
                    "prediction_interval": {
                        "lower_bound": [13500, 14850, 16200, 17550, 18900],
                        "upper_bound": [16500, 18150, 19800, 21450, 23100]
                    }
                },
                "scenario_forecasts": {
                    "optimistic": {"predicted_volume": 25000, "predicted_success_rate": 0.95},
                    "realistic": {"predicted_volume": 20000, "predicted_success_rate": 0.85}, 
                    "pessimistic": {"predicted_volume": 15000, "predicted_success_rate": 0.70}
                }
            },
            "network_topology_analysis": {
                "network_density": 0.45,
                "clustering_coefficient": 0.62,
                "average_path_length": 3.2,
                "centrality_analysis": {
                    "top_nodes": [
                        {"id": "hong_kong", "betweenness": 0.85, "degree": 15},
                        {"id": "singapore", "betweenness": 0.78, "degree": 12}, 
                        {"id": "guangzhou", "betweenness": 0.72, "degree": 10},
                        {"id": "xiamen", "betweenness": 0.65, "degree": 8},
                        {"id": "manila", "betweenness": 0.58, "degree": 7}
                    ]
                },
                "community_structure": {
                    "num_communities": 4,
                    "modularity": 0.73
                }
            },
            "event_impact_analysis": {
                "major_events": [
                    {"year": 1929, "event": "Great Depression", "impact": -0.35},
                    {"year": 1931, "event": "Manchurian Incident", "impact": -0.28},
                    {"year": 1935, "event": "Currency Reform", "impact": 0.22},
                    {"year": 1937, "event": "Second Sino-Japanese War", "impact": -0.45}
                ],
                "recovery_patterns": {
                    "average_recovery_time": "18_months",
                    "resilience_score": 0.68
                }
            }
        };
        
        // 保存主要结果文件
        const files = [
            {name: 'enhanced_analysis.json', data: enhancedAnalysis},
            {name: 'multi_timeseries.json', data: enhancedAnalysis.multi_timeseries_analysis},
            {name: 'visualization_data.json', data: {
                timeseries: [
                    {
                        dimension: "regional_series",
                        series_name: "guangdong",
                        data: enhancedAnalysis.multi_timeseries_analysis.regional_series.guangdong.series_data,
                        type: "line_chart"
                    },
                    {
                        dimension: "currency_series", 
                        series_name: "usd",
                        data: enhancedAnalysis.multi_timeseries_analysis.currency_series.usd,
                        type: "line_chart"
                    }
                ],
                heatmaps: [{
                    type: "correlation_matrix",
                    data: enhancedAnalysis.correlation_analysis,
                    title: "Correlation Matrix"
                }],
                network_graphs: [{
                    type: "network_topology",
                    data: enhancedAnalysis.network_topology_analysis,
                    title: "Network Topology Structure"
                }]
            }},
            {name: 'final_report.json', data: {
                generated_at: timestamp,
                simulation_config: {
                    start_year: 1920,
                    end_year: 1940,
                    num_migrants: 300,
                    num_families: 300,
                    num_institutions: 10
                },
                analysis_summary: {
                    timeseries_analysis: {
                        dimensions_analyzed: Object.keys(enhancedAnalysis.multi_timeseries_analysis).length,
                        total_series: Object.values(enhancedAnalysis.multi_timeseries_analysis).reduce((sum, dim) => {
                            return sum + (typeof dim === 'object' ? Object.keys(dim).length : 0);
                        }, 0)
                    },
                    clustering_analysis: {
                        behavior_clusters_identified: Object.keys(enhancedAnalysis.clustering_analysis.migrant_behavior_clusters.cluster_profiles).length
                    },
                    prediction_analysis: {
                        forecast_horizon_years: enhancedAnalysis.prediction_analysis['5_year_forecast'].future_years.length,
                        scenarios_analyzed: Object.keys(enhancedAnalysis.prediction_analysis.scenario_forecasts).length
                    },
                    network_analysis: {
                        network_density: enhancedAnalysis.network_topology_analysis.network_density,
                        central_nodes: enhancedAnalysis.network_topology_analysis.centrality_analysis.top_nodes.length
                    }
                },
                key_findings: [
                    "Generated multi-dimensional time series analysis covering regional, currency, and institutional dimensions",
                    "Identified 3 distinct migrant behavior cluster patterns",
                    "Completed 5-year remittance trend prediction analysis",
                    "Network density of 0.45 shows moderate connectivity characteristics",
                    "Event impact analysis reveals average recovery time of 18 months"
                ],
                enhanced_analysis: enhancedAnalysis
            }}
        ];
        
        for (const file of files) {
            const filePath = path.join(outputDir, file.name);
            await fs.writeJson(filePath, file.data, {spaces: 2});
            console.log(`✅ Created: ${file.name} (${JSON.stringify(file.data).length} chars)`);
        }
        
        console.log('🎉 Enhanced simulation demo data created successfully!');
        console.log(`📁 Files saved to: ${outputDir}`);
        
        // 列出创建的文件
        const dirContents = await fs.readdir(outputDir);
        console.log('📋 Generated files:');
        for (const file of dirContents) {
            const stats = await fs.stat(path.join(outputDir, file));
            console.log(`   📄 ${file} (${stats.size} bytes)`);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error creating demo data:', error);
        return false;
    }
}

async function main() {
    console.log('🎯 Enhanced Simulation Demo Data Generator');
    console.log('===========================================');
    
    const success = await createEnhancedResults();
    
    if (success) {
        console.log('✅ Demo data generation completed successfully!');
        console.log('🌐 You can now visit: http://localhost:3508/enhanced-simulation.html');
        console.log('📊 Click "查看现有结果" to view the generated demo data');
    } else {
        console.log('❌ Demo data generation failed');
    }
    
    process.exit(success ? 0 : 1);
}

if (require.main === module) {
    main();
}

module.exports = { createEnhancedResults };