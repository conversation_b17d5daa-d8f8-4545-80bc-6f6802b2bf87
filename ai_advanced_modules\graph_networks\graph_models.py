"""
图神经网络模型实现
Graph Neural Network Models Implementation

实现了专门用于侨批网络分析的各种图神经网络模型：
- 基础图神经网络
- 时序图神经网络
- 层次化图神经网络
- 注意力图神经网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import (
    GCNConv, GATConv, SAGEConv, GraphConv,
    global_mean_pool, global_max_pool, global_add_pool,
    BatchNorm, LayerNorm, MessagePassing
)
from torch_geometric.data import Data, Batch
from torch_geometric.utils import to_dense_batch, add_self_loops
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from dataclasses import dataclass
import math

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class GraphConfig:
    """图神经网络配置"""
    # 模型架构
    hidden_dim: int = 256
    num_layers: int = 4
    num_heads: int = 8  # 注意力头数
    dropout: float = 0.1
    
    # 图结构
    node_feature_dim: int = 64
    edge_feature_dim: int = 32
    output_dim: int = 128
    
    # 训练配置
    learning_rate: float = 1e-3
    weight_decay: float = 1e-4
    
    # 特殊配置
    use_batch_norm: bool = True
    use_residual: bool = True
    activation: str = "relu"  # "relu", "gelu", "swish"


class QiaopiNetworkGNN(nn.Module):
    """侨批网络专用图神经网络"""
    
    def __init__(self, config: GraphConfig):
        super().__init__()
        self.config = config
        
        # 节点特征编码器
        self.node_encoder = nn.Sequential(
            nn.Linear(config.node_feature_dim, config.hidden_dim),
            self._get_activation(),
            nn.Dropout(config.dropout)
        )
        
        # 边特征编码器
        self.edge_encoder = nn.Sequential(
            nn.Linear(config.edge_feature_dim, config.hidden_dim),
            self._get_activation(),
            nn.Dropout(config.dropout)
        )
        
        # 图卷积层
        self.gnn_layers = nn.ModuleList()
        for i in range(config.num_layers):
            layer = QiaopiGraphLayer(
                in_dim=config.hidden_dim,
                out_dim=config.hidden_dim,
                edge_dim=config.hidden_dim,
                num_heads=config.num_heads,
                dropout=config.dropout,
                use_batch_norm=config.use_batch_norm,
                use_residual=config.use_residual
            )
            self.gnn_layers.append(layer)
        
        # 图级别聚合
        self.graph_pooling = GraphPooling(config.hidden_dim)
        
        # 输出层
        self.output_layers = nn.Sequential(
            nn.Linear(config.hidden_dim * 3, config.hidden_dim),  # 3种池化结果
            self._get_activation(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.output_dim)
        )
        
        # 任务特定头
        self.task_heads = nn.ModuleDict({
            'node_classification': nn.Linear(config.hidden_dim, 10),  # 节点分类
            'link_prediction': nn.Linear(config.hidden_dim * 2, 1),   # 链接预测
            'graph_classification': nn.Linear(config.output_dim, 5),  # 图分类
            'node_regression': nn.Linear(config.hidden_dim, 1),       # 节点回归
            'influence_score': nn.Linear(config.hidden_dim, 1)        # 影响力评分
        })
        
        logger.info(f"初始化侨批网络GNN，隐藏维度: {config.hidden_dim}，层数: {config.num_layers}")
    
    def _get_activation(self):
        """获取激活函数"""
        if self.config.activation == "relu":
            return nn.ReLU()
        elif self.config.activation == "gelu":
            return nn.GELU()
        elif self.config.activation == "swish":
            return nn.SiLU()
        else:
            return nn.ReLU()
    
    def forward(self, data: Data, task: str = "graph_classification") -> torch.Tensor:
        """前向传播"""
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        batch = getattr(data, 'batch', None)
        
        # 编码节点和边特征
        x = self.node_encoder(x)
        if edge_attr is not None:
            edge_attr = self.edge_encoder(edge_attr)
        
        # 通过图卷积层
        node_embeddings = []
        for layer in self.gnn_layers:
            x = layer(x, edge_index, edge_attr)
            node_embeddings.append(x)
        
        # 根据任务类型返回不同的输出
        if task == "node_classification" or task == "node_regression" or task == "influence_score":
            # 节点级别任务
            return self.task_heads[task](x)
        
        elif task == "link_prediction":
            # 链接预测任务
            return x  # 返回节点嵌入，后续计算边的嵌入
        
        elif task == "graph_classification":
            # 图级别任务
            graph_embedding = self.graph_pooling(x, batch)
            graph_embedding = self.output_layers(graph_embedding)
            return self.task_heads[task](graph_embedding)
        
        else:
            # 默认返回节点嵌入
            return x
    
    def get_node_embeddings(self, data: Data) -> torch.Tensor:
        """获取节点嵌入"""
        with torch.no_grad():
            x = self.forward(data, task="link_prediction")
        return x
    
    def predict_links(self, data: Data, edge_index: torch.Tensor) -> torch.Tensor:
        """预测链接概率"""
        node_embeddings = self.get_node_embeddings(data)
        
        # 获取边的嵌入
        src_embeddings = node_embeddings[edge_index[0]]
        dst_embeddings = node_embeddings[edge_index[1]]
        edge_embeddings = torch.cat([src_embeddings, dst_embeddings], dim=1)
        
        # 预测链接概率
        link_probs = torch.sigmoid(self.task_heads['link_prediction'](edge_embeddings))
        return link_probs


class QiaopiGraphLayer(MessagePassing):
    """侨批网络专用图卷积层"""
    
    def __init__(self, in_dim: int, out_dim: int, edge_dim: int, 
                 num_heads: int = 8, dropout: float = 0.1,
                 use_batch_norm: bool = True, use_residual: bool = True):
        super().__init__(aggr='add', node_dim=0)
        
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.edge_dim = edge_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.use_batch_norm = use_batch_norm
        self.use_residual = use_residual
        
        # 多头注意力
        self.attention = nn.MultiheadAttention(
            embed_dim=in_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 消息传递网络
        self.message_mlp = nn.Sequential(
            nn.Linear(in_dim * 2 + edge_dim, in_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(in_dim, in_dim)
        )
        
        # 节点更新网络
        self.update_mlp = nn.Sequential(
            nn.Linear(in_dim * 2, out_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 批标准化
        if use_batch_norm:
            self.batch_norm = BatchNorm(out_dim)
        
        # 残差连接
        if use_residual and in_dim == out_dim:
            self.residual = True
        else:
            self.residual = False
            if use_residual:
                self.residual_projection = nn.Linear(in_dim, out_dim)
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, 
                edge_attr: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        # 保存输入用于残差连接
        residual = x
        
        # 消息传递
        out = self.propagate(edge_index, x=x, edge_attr=edge_attr)
        
        # 残差连接
        if self.use_residual:
            if self.residual:
                out = out + residual
            else:
                out = out + self.residual_projection(residual)
        
        # 批标准化
        if self.use_batch_norm:
            out = self.batch_norm(out)
        
        return out
    
    def message(self, x_i: torch.Tensor, x_j: torch.Tensor, 
                edge_attr: Optional[torch.Tensor] = None) -> torch.Tensor:
        """消息函数"""
        if edge_attr is not None:
            # 包含边属性的消息
            message_input = torch.cat([x_i, x_j, edge_attr], dim=-1)
        else:
            # 不包含边属性的消息
            message_input = torch.cat([x_i, x_j], dim=-1)
            # 填充边属性维度
            edge_padding = torch.zeros(x_i.size(0), self.edge_dim, 
                                     device=x_i.device, dtype=x_i.dtype)
            message_input = torch.cat([message_input, edge_padding], dim=-1)
        
        return self.message_mlp(message_input)
    
    def update(self, aggr_out: torch.Tensor, x: torch.Tensor) -> torch.Tensor:
        """更新函数"""
        update_input = torch.cat([x, aggr_out], dim=-1)
        return self.update_mlp(update_input)


class TemporalGraphNetwork(nn.Module):
    """时序图神经网络"""
    
    def __init__(self, config: GraphConfig, sequence_length: int = 10):
        super().__init__()
        self.config = config
        self.sequence_length = sequence_length
        
        # 基础图神经网络
        self.base_gnn = QiaopiNetworkGNN(config)
        
        # 时序编码器
        self.temporal_encoder = nn.LSTM(
            input_size=config.output_dim,
            hidden_size=config.hidden_dim,
            num_layers=2,
            batch_first=True,
            dropout=config.dropout
        )
        
        # 时序注意力
        self.temporal_attention = nn.MultiheadAttention(
            embed_dim=config.hidden_dim,
            num_heads=config.num_heads,
            dropout=config.dropout,
            batch_first=True
        )
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.output_dim)
        )
        
        logger.info(f"初始化时序图神经网络，序列长度: {sequence_length}")
    
    def forward(self, graph_sequence: List[Data]) -> torch.Tensor:
        """前向传播"""
        # 处理图序列
        graph_embeddings = []
        for graph in graph_sequence:
            graph_emb = self.base_gnn(graph, task="graph_classification")
            graph_embeddings.append(graph_emb)
        
        # 堆叠为序列
        sequence = torch.stack(graph_embeddings, dim=1)  # (batch, seq_len, hidden_dim)
        
        # LSTM编码
        lstm_out, (hidden, cell) = self.temporal_encoder(sequence)
        
        # 时序注意力
        attended_out, attention_weights = self.temporal_attention(
            lstm_out, lstm_out, lstm_out
        )
        
        # 预测
        prediction = self.prediction_head(attended_out[:, -1, :])  # 使用最后时刻
        
        return prediction, attention_weights
    
    def predict_next_graph(self, graph_sequence: List[Data]) -> torch.Tensor:
        """预测下一个图的特征"""
        with torch.no_grad():
            prediction, _ = self.forward(graph_sequence)
        return prediction


class HierarchicalGraphNetwork(nn.Module):
    """层次化图神经网络"""
    
    def __init__(self, config: GraphConfig, hierarchy_levels: int = 3):
        super().__init__()
        self.config = config
        self.hierarchy_levels = hierarchy_levels
        
        # 多层次图卷积
        self.level_gnns = nn.ModuleList()
        for level in range(hierarchy_levels):
            gnn = QiaopiNetworkGNN(config)
            self.level_gnns.append(gnn)
        
        # 层次间通信
        self.level_communication = nn.ModuleList()
        for level in range(hierarchy_levels - 1):
            comm = nn.Sequential(
                nn.Linear(config.output_dim, config.hidden_dim),
                nn.ReLU(),
                nn.Linear(config.hidden_dim, config.output_dim)
            )
            self.level_communication.append(comm)
        
        # 最终融合
        self.fusion_layer = nn.Sequential(
            nn.Linear(config.output_dim * hierarchy_levels, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.output_dim)
        )
        
        logger.info(f"初始化层次化图神经网络，层次数: {hierarchy_levels}")
    
    def forward(self, hierarchical_graphs: List[Data]) -> torch.Tensor:
        """前向传播"""
        level_outputs = []
        
        # 处理每个层次
        for level, graph in enumerate(hierarchical_graphs):
            if level < len(self.level_gnns):
                output = self.level_gnns[level](graph, task="graph_classification")
                
                # 层次间通信
                if level > 0 and level - 1 < len(self.level_communication):
                    communication = self.level_communication[level - 1](level_outputs[-1])
                    output = output + communication
                
                level_outputs.append(output)
        
        # 融合所有层次的输出
        if level_outputs:
            fused_output = torch.cat(level_outputs, dim=-1)
            final_output = self.fusion_layer(fused_output)
        else:
            final_output = torch.zeros(1, self.config.output_dim)
        
        return final_output, level_outputs


class AttentionGraphNetwork(nn.Module):
    """注意力图神经网络"""
    
    def __init__(self, config: GraphConfig):
        super().__init__()
        self.config = config
        
        # 图注意力网络层
        self.gat_layers = nn.ModuleList()
        for i in range(config.num_layers):
            if i == 0:
                gat = GATConv(
                    in_channels=config.node_feature_dim,
                    out_channels=config.hidden_dim // config.num_heads,
                    heads=config.num_heads,
                    dropout=config.dropout,
                    edge_dim=config.edge_feature_dim
                )
            elif i == config.num_layers - 1:
                gat = GATConv(
                    in_channels=config.hidden_dim,
                    out_channels=config.output_dim,
                    heads=1,
                    dropout=config.dropout,
                    edge_dim=config.edge_feature_dim
                )
            else:
                gat = GATConv(
                    in_channels=config.hidden_dim,
                    out_channels=config.hidden_dim // config.num_heads,
                    heads=config.num_heads,
                    dropout=config.dropout,
                    edge_dim=config.edge_feature_dim
                )
            self.gat_layers.append(gat)
        
        # 全局注意力池化
        self.global_attention = GlobalAttentionPooling(config.output_dim)
        
        # 输出层
        self.classifier = nn.Sequential(
            nn.Linear(config.output_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.output_dim)
        )
        
        logger.info(f"初始化注意力图神经网络，注意力头数: {config.num_heads}")
    
    def forward(self, data: Data, return_attention: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, List]]:
        """前向传播"""
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        batch = getattr(data, 'batch', None)
        
        attention_weights = []
        
        # GAT层
        for i, gat_layer in enumerate(self.gat_layers):
            if return_attention and hasattr(gat_layer, 'return_attention_weights'):
                x, att_weights = gat_layer(x, edge_index, edge_attr, return_attention_weights=True)
                attention_weights.append(att_weights)
            else:
                x = gat_layer(x, edge_index, edge_attr)
            
            if i < len(self.gat_layers) - 1:
                x = F.elu(x)
                x = F.dropout(x, p=self.config.dropout, training=self.training)
        
        # 全局注意力池化
        graph_embedding, pool_attention = self.global_attention(x, batch)
        
        # 最终分类
        output = self.classifier(graph_embedding)
        
        if return_attention:
            attention_weights.append(pool_attention)
            return output, attention_weights
        else:
            return output


class GraphPooling(nn.Module):
    """图池化层"""
    
    def __init__(self, hidden_dim: int):
        super().__init__()
        self.hidden_dim = hidden_dim
        
        # 注意力池化
        self.attention_pooling = GlobalAttentionPooling(hidden_dim)
        
    def forward(self, x: torch.Tensor, batch: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        # 多种池化方式
        mean_pool = global_mean_pool(x, batch)
        max_pool = global_max_pool(x, batch)
        
        # 注意力池化
        att_pool, _ = self.attention_pooling(x, batch)
        
        # 拼接所有池化结果
        pooled = torch.cat([mean_pool, max_pool, att_pool], dim=-1)
        
        return pooled


class GlobalAttentionPooling(nn.Module):
    """全局注意力池化"""
    
    def __init__(self, hidden_dim: int):
        super().__init__()
        self.attention_net = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, x: torch.Tensor, batch: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播"""
        # 计算注意力权重
        attention_scores = self.attention_net(x)  # (num_nodes, 1)
        
        if batch is not None:
            # 批处理模式
            attention_weights = torch.softmax(attention_scores, dim=0)
            
            # 使用scatter_add进行池化
            pooled = torch.zeros(batch.max().item() + 1, x.size(1), 
                               device=x.device, dtype=x.dtype)
            
            for i in range(batch.max().item() + 1):
                mask = (batch == i)
                if mask.any():
                    node_features = x[mask]
                    node_weights = attention_weights[mask]
                    weighted_features = node_features * node_weights
                    pooled[i] = weighted_features.sum(dim=0)
        else:
            # 单图模式
            attention_weights = torch.softmax(attention_scores, dim=0)
            pooled = (x * attention_weights).sum(dim=0, keepdim=True)
        
        return pooled, attention_weights


class GraphEmbeddingModel(nn.Module):
    """图嵌入模型"""
    
    def __init__(self, config: GraphConfig, embedding_dim: int = 128):
        super().__init__()
        self.config = config
        self.embedding_dim = embedding_dim
        
        # 节点嵌入
        self.node_embedding = nn.Embedding(10000, embedding_dim)  # 假设最多10000个节点
        
        # 位置编码
        self.position_encoding = PositionalEncoding(embedding_dim)
        
        # 图变换器
        self.graph_transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=embedding_dim,
                nhead=config.num_heads,
                dim_feedforward=config.hidden_dim,
                dropout=config.dropout,
                batch_first=True
            ),
            num_layers=config.num_layers
        )
        
        # 输出投影
        self.output_projection = nn.Linear(embedding_dim, config.output_dim)
    
    def forward(self, node_ids: torch.Tensor, adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 节点嵌入
        node_emb = self.node_embedding(node_ids)
        
        # 位置编码
        node_emb = self.position_encoding(node_emb)
        
        # 创建注意力掩码（基于邻接矩阵）
        attention_mask = adjacency_matrix.bool()
        
        # 图变换器
        transformed = self.graph_transformer(node_emb, src_key_padding_mask=~attention_mask)
        
        # 输出投影
        output = self.output_projection(transformed)
        
        return output


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        return x + self.pe[:x.size(1), :].unsqueeze(0)


# 工厂函数
def create_qiaopi_gnn(model_type: str = "basic", **kwargs) -> nn.Module:
    """创建侨批网络图神经网络模型"""
    config = GraphConfig(**kwargs)
    
    if model_type == "basic":
        return QiaopiNetworkGNN(config)
    elif model_type == "temporal":
        sequence_length = kwargs.get('sequence_length', 10)
        return TemporalGraphNetwork(config, sequence_length)
    elif model_type == "hierarchical":
        hierarchy_levels = kwargs.get('hierarchy_levels', 3)
        return HierarchicalGraphNetwork(config, hierarchy_levels)
    elif model_type == "attention":
        return AttentionGraphNetwork(config)
    elif model_type == "embedding":
        embedding_dim = kwargs.get('embedding_dim', 128)
        return GraphEmbeddingModel(config, embedding_dim)
    else:
        logger.warning(f"未知模型类型: {model_type}，返回基础模型")
        return QiaopiNetworkGNN(config)


# 使用示例
def main():
    """主函数示例"""
    # 创建配置
    config = GraphConfig(
        hidden_dim=128,
        num_layers=3,
        num_heads=4,
        node_feature_dim=20,
        edge_feature_dim=10,
        output_dim=64
    )
    
    # 创建模型
    model = QiaopiNetworkGNN(config)
    
    # 创建示例数据
    num_nodes = 100
    x = torch.randn(num_nodes, config.node_feature_dim)
    edge_index = torch.randint(0, num_nodes, (2, 200))
    edge_attr = torch.randn(200, config.edge_feature_dim)
    
    data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
    
    # 前向传播
    output = model(data, task="graph_classification")
    print(f"图分类输出形状: {output.shape}")
    
    # 节点分类
    node_output = model(data, task="node_classification")
    print(f"节点分类输出形状: {node_output.shape}")
    
    # 获取节点嵌入
    node_embeddings = model.get_node_embeddings(data)
    print(f"节点嵌入形状: {node_embeddings.shape}")


if __name__ == "__main__":
    main()
