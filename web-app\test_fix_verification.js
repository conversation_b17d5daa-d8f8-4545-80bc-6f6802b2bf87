// 验证增强仿真修复的测试脚本
const http = require('http');
const fs = require('fs-extra');
const path = require('path');

const BASE_URL = 'http://localhost:3508';

async function testAPI(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(endpoint, BASE_URL);
        
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const req = http.request(options, (res) => {
            let body = '';
            
            res.on('data', (chunk) => {
                body += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(body);
                    resolve({ status: res.statusCode, data: result });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

async function runTests() {
    console.log('🧪 Testing Enhanced Simulation Fix');
    console.log('=' * 50);
    
    const tests = [
        {
            name: 'Health Check',
            endpoint: '/api/health',
            method: 'GET'
        },
        {
            name: 'Generate Demo Results', 
            endpoint: '/api/enhanced/generate-demo',
            method: 'POST'
        },
        {
            name: 'Check Enhanced Results',
            endpoint: '/api/enhanced/results',
            method: 'GET'
        },
        {
            name: 'Get Visualization Data',
            endpoint: '/api/enhanced/visualization', 
            method: 'GET'
        }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        try {
            console.log(`\n🧪 Testing: ${test.name}`);
            console.log(`   📡 ${test.method} ${test.endpoint}`);
            
            const result = await testAPI(test.endpoint, test.method, test.data);
            
            if (result.status === 200) {
                console.log(`   ✅ PASS (Status: ${result.status})`);
                if (test.name === 'Check Enhanced Results' && result.data.available) {
                    console.log(`   📊 Files loaded: ${result.data.files_loaded?.length || 'N/A'}`);
                }
                passedTests++;
            } else {
                console.log(`   ❌ FAIL (Status: ${result.status})`);
                if (result.data.error) {
                    console.log(`   🔴 Error: ${result.data.error}`);
                }
            }
            
        } catch (error) {
            console.log(`   ❌ FAIL (Network Error: ${error.message})`);
        }
        
        // 在生成演示数据后等待一秒
        if (test.name === 'Generate Demo Results') {
            console.log('   ⏳ Waiting for demo generation...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log('\n📊 Test Summary:');
    console.log(`   Tests Passed: ${passedTests}/${tests.length}`);
    console.log(`   Success Rate: ${((passedTests / tests.length) * 100).toFixed(1)}%`);
    
    if (passedTests === tests.length) {
        console.log('\n🎉 All tests passed! Enhanced simulation should be working.');
    } else {
        console.log('\n⚠️  Some tests failed. Check server logs for details.');
    }
    
    // 检查文件是否实际生成
    console.log('\n📁 Checking generated files:');
    const resultsDir = path.resolve('../enhanced_results');
    try {
        if (fs.existsSync(resultsDir)) {
            const files = fs.readdirSync(resultsDir);
            console.log(`   📂 Enhanced results directory: ${resultsDir}`);
            console.log(`   📄 Files found: ${files.length}`);
            files.forEach(file => {
                const size = fs.statSync(path.join(resultsDir, file)).size;
                console.log(`      📄 ${file} (${size} bytes)`);
            });
        } else {
            console.log(`   ❌ Results directory not found: ${resultsDir}`);
        }
    } catch (error) {
        console.log(`   ❌ Error checking files: ${error.message}`);
    }
    
    return passedTests === tests.length;
}

// 检查服务器是否运行
async function checkServerRunning() {
    try {
        const result = await testAPI('/api/health');
        return result.status === 200;
    } catch (error) {
        return false;
    }
}

async function main() {
    console.log('🚀 Enhanced Simulation Fix Verification');
    console.log('==========================================');
    
    // 检查服务器状态
    const serverRunning = await checkServerRunning();
    
    if (!serverRunning) {
        console.log('❌ Server is not running on http://localhost:3508');
        console.log('   Please start the server first: node server.js');
        return false;
    }
    
    console.log('✅ Server is running');
    
    // 运行测试
    const success = await runTests();
    
    console.log('\n' + '='.repeat(50));
    if (success) {
        console.log('🎯 VERIFICATION PASSED: Enhanced simulation is working!');
        console.log('🌐 You can now visit: http://localhost:3508/enhanced-simulation.html');
    } else {
        console.log('❌ VERIFICATION FAILED: Some issues need to be resolved.');
    }
    
    return success;
}

if (require.main === module) {
    main().then(success => process.exit(success ? 0 : 1));
}

module.exports = { runTests, checkServerRunning };