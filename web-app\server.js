const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const compression = require('compression');
const moment = require('moment');
const _ = require('lodash');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3508;

// 中间件
// app.use(helmet()); // 暂时禁用Helmet以解决CSP问题
app.use(compression());
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static('public', {
    setHeaders: (res, path) => {
        if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
        } else if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css');
        }
    }
}));

// 数据目录
const ROOT_DIR = path.join(__dirname, '..');
const DATA_DIR = path.join(ROOT_DIR, 'simulation_results'); // 仓库根目录
const WEB_DATA_DIR = path.join(__dirname, 'simulation_results'); // web-app下的演示数据
const REAL_DATA_DIR = path.join(ROOT_DIR, 'realistic_results');

// 确保数据目录存在
fs.ensureDirSync(DATA_DIR);
fs.ensureDirSync(REAL_DATA_DIR);
fs.ensureDirSync(WEB_DATA_DIR);

// API路由
app.get('/api/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// 获取智能体类型配置
app.get('/api/agents', async (req, res) => {
    try {
        const agentTypes = [
            {
                id: 'migrant',
                name: '海外移民',
                description: '模拟海外华侨的汇款行为和决策过程',
                icon: 'fas fa-user-friends',
                color: 'primary',
                defaultConfig: {
                    riskTolerance: 0.6,
                    cooperativeness: 0.8,
                    adaptability: 0.7,
                    learningRate: 0.02,
                    trustLevel: 0.7,
                    wealthLevel: 'middle',
                    remittanceFrequency: 3
                },
                capabilities: [
                    '强化学习算法',
                    '风险评估机制', 
                    '宗族网络连接',
                    '经济决策模型'
                ]
            },
            {
                id: 'family',
                name: '家乡家庭',
                description: '模拟接收汇款的家庭成员行为模式',
                icon: 'fas fa-home',
                color: 'success',
                defaultConfig: {
                    riskTolerance: 0.3,
                    cooperativeness: 0.9,
                    adaptability: 0.6,
                    learningRate: 0.01,
                    trustLevel: 0.8,
                    wealthLevel: 'modest',
                    remittanceFrequency: 0
                },
                capabilities: [
                    '需求评估算法',
                    '资源分配策略',
                    '社会网络维护',
                    '感恩表达机制'
                ]
            },
            {
                id: 'institution',
                name: '金融机构',
                description: '模拟银庄、批局等金融中介机构',
                icon: 'fas fa-building', 
                color: 'warning',
                defaultConfig: {
                    riskTolerance: 0.4,
                    cooperativeness: 0.6,
                    adaptability: 0.5,
                    learningRate: 0.015,
                    trustLevel: 0.6,
                    wealthLevel: 'wealthy',
                    remittanceFrequency: 0
                },
                capabilities: [
                    '风险管理系统',
                    '信用评估模型',
                    '网络优化算法',
                    '盈利最大化策略'
                ]
            }
        ];
        
        res.json(agentTypes);
    } catch (error) {
        console.error('Error loading agent types:', error);
        res.status(500).json({ error: 'Failed to load agent types' });
    }
});

// 创建智能体
app.post('/api/agents', async (req, res) => {
    try {
        const agentData = req.body;
        
        // 验证必需字段
        if (!agentData.type || !agentData.name) {
            return res.status(400).json({ error: 'Missing required fields: type and name' });
        }
        
        // 回退模式：直接创建智能体记录
        const agent = {
            ...agentData,
            id: `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date().toISOString(),
            status: 'active',
            source: 'web_interface'
        };
        
        res.json({
            success: true,
            agent_id: agent.id,
            agent_type: agent.type,
            agent_name: agent.name,
            created_at: agent.createdAt,
            message: `智能体 "${agent.name}" 创建成功`
        });
        
    } catch (error) {
        console.error('Error creating agent:', error);
        res.status(500).json({ error: 'Failed to create agent' });
    }
});

// 测试智能体
app.post('/api/agents/:id/test', async (req, res) => {
    try {
        const { id } = req.params;
        const { scenario, steps } = req.body;
        
        // 模拟测试过程
        const testResults = {
            agentId: id,
            scenario: scenario || 'basic',
            steps: steps || 50,
            results: {
                successRate: (Math.random() * 0.3 + 0.7).toFixed(3),
                avgDecisionTime: (Math.random() * 100 + 50).toFixed(1) + 'ms',
                learningProgress: (Math.random() * 30 + 70).toFixed(1) + '%',
                socialInteractions: Math.floor(Math.random() * 50 + 20),
                memoryUsage: Math.floor(Math.random() * 80 + 20) + '%',
                errors: Math.floor(Math.random() * 3),
                warnings: Math.floor(Math.random() * 5)
            },
            completedAt: new Date().toISOString()
        };
        
        res.json(testResults);
    } catch (error) {
        console.error('Error testing agent:', error);
        res.status(500).json({ error: 'Failed to test agent' });
    }
});

// ===== 增强版仿真 API =====
// 运行增强版仿真
app.post('/api/enhanced/run', async (req, res) => {
    console.log('🚀 Enhanced simulation requested');
    
    try {
        const { config } = req.body;
        
        // 检查Python脚本，优先使用真实AI智能体仿真
        let pythonScript = path.join(ROOT_DIR, 'run_real_enhanced_simulation.py');
        let scriptType = 'real_ai';
        
        console.log('📍 Checking for real AI agent script:', pythonScript);
        
        if (!await fs.pathExists(pythonScript)) {
            console.warn('⚠️ Real AI script not found, trying enhanced demo version...');
            pythonScript = path.join(ROOT_DIR, 'run_enhanced_demo_api.py');
            scriptType = 'enhanced_demo';
            
            if (!await fs.pathExists(pythonScript)) {
                console.warn('⚠️ Enhanced demo script not found, trying simple version...');
                pythonScript = path.join(ROOT_DIR, 'test_api_simple.py');
                scriptType = 'simple_test';
                
                if (!await fs.pathExists(pythonScript)) {
                    console.error('❌ No Python scripts found');
                    return res.status(500).json({ 
                        error: 'No simulation scripts found',
                        message: '请确保至少有一个仿真脚本存在于项目根目录'
                    });
                }
            }
        }
        
        console.log(`📍 Using ${scriptType} script: ${pythonScript}`);
        
        // 确保输出目录存在
        const outputDir = path.join(ROOT_DIR, 'enhanced_results');
        await fs.ensureDir(outputDir);
        
        // 准备命令行参数
        const args = [
            '--start-year', (config?.startYear || 1920).toString(),
            '--end-year', (config?.endYear || 1940).toString(),
            '--migrants', (config?.migrants || 300).toString(),
            '--families', (config?.families || 300).toString(),
            '--institutions', (config?.institutions || 10).toString(),
            '--output-dir', 'enhanced_results'
        ];
        
        console.log('🐍 Running Python script with args:', args);
        
        // 立即返回响应，避免超时
        const messages = {
            'real_ai': '真实AI智能体增强仿真已启动，正在运行...',
            'enhanced_demo': '增强仿真演示已启动，正在运行...',
            'simple_test': '简化测试仿真已启动，正在运行...'
        };
        
        res.json({ 
            success: true, 
            message: messages[scriptType],
            jobId: Date.now(),
            status: 'running',
            script_type: scriptType,
            is_real_ai_simulation: scriptType === 'real_ai'
        });
        
        // 异步运行Python脚本
        const python = spawn('python', [pythonScript, ...args], {
            cwd: ROOT_DIR,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let output = '';
        let error = '';
        
        python.stdout.on('data', (data) => {
            const chunk = data.toString();
            output += chunk;
            console.log('📊 Python output:', chunk.trim());
        });
        
        python.stderr.on('data', (data) => {
            const chunk = data.toString();
            error += chunk;
            console.error('⚠️ Python error:', chunk.trim());
        });
        
        python.on('close', async (code) => {
            if (code === 0) {
                console.log(`✅ ${scriptType === 'real_ai' ? 'Real AI agent' : 'Enhanced'} simulation completed successfully`);
                
                // 验证结果文件是否生成
                const resultsPath = path.join(ROOT_DIR, 'enhanced_results/enhanced_analysis.json');
                if (await fs.pathExists(resultsPath)) {
                    console.log('📄 AI agent simulation results file generated successfully');
                    
                    // 如果是真实AI仿真，记录特殊标记
                    if (scriptType === 'real_ai') {
                        const statusFile = path.join(ROOT_DIR, 'enhanced_results/simulation_status.json');
                        await fs.writeJson(statusFile, {
                            simulation_type: 'real_ai_agents',
                            completed_at: new Date().toISOString(),
                            script_used: pythonScript
                        });
                    }
                } else {
                    console.warn('⚠️ Results file not found, but script completed');
                    // 只有在非AI脚本失败时才创建回退结果
                    if (scriptType !== 'real_ai') {
                        console.log('🔧 Creating fallback results...');
                        await createFallbackResults(outputDir);
                    }
                }
            } else {
                console.error(`❌ ${scriptType} simulation failed with code:`, code);
                console.error('Error output:', error);
                
                // 只有在非AI脚本失败时才创建回退结果
                if (scriptType !== 'real_ai') {
                    console.log('🔧 Creating fallback results due to script failure...');
                    await createFallbackResults(outputDir);
                } else {
                    console.log('❌ Real AI simulation failed - no fallback for authentic simulation');
                }
            }
        });
        
        python.on('error', (err) => {
            console.error('❌ Failed to start Python process:', err);
            // 如果Python脚本失败，创建基本的模拟结果
            createFallbackResults(outputDir);
        });
        
    } catch (error) {
        console.error('❌ Enhanced simulation error:', error);
        res.status(500).json({ 
            error: 'Failed to start enhanced simulation',
            details: error.message,
            stack: error.stack
        });
    }
});

// 获取增强分析结果
app.get('/api/enhanced/results', async (req, res) => {
    console.log('📋 Results requested for enhanced simulation');
    
    try {
        const resultsPath = path.join(ROOT_DIR, 'enhanced_results');
        
        // 检查结果目录
        if (!await fs.pathExists(resultsPath)) {
            console.log('📂 No enhanced results directory found');
            return res.json({ available: false, message: '尚未运行增强分析' });
        }
        
        const results = {};
        let hasResults = false;
        
        // 读取各种结果文件
        const files = [
            { key: 'enhanced_analysis', file: 'enhanced_analysis.json' },
            { key: 'multi_timeseries', file: 'multi_timeseries.json' },
            { key: 'visualization_data', file: 'visualization_data.json' },
            { key: 'final_report', file: 'final_report.json' }
        ];
        
        for (const { key, file } of files) {
            const filePath = path.join(resultsPath, file);
            if (await fs.pathExists(filePath)) {
                try {
                    results[key] = await fs.readJson(filePath);
                    hasResults = true;
                    console.log(`✅ Loaded ${file}`);
                } catch (jsonError) {
                    console.error(`⚠️ Error reading ${file}:`, jsonError.message);
                }
            } else {
                console.log(`⚪ File not found: ${file}`);
            }
        }
        
        if (!hasResults) {
            console.log('📂 Enhanced results directory exists but no valid result files found');
            return res.json({ 
                available: false, 
                message: '增强分析正在进行中，请稍后再试' 
            });
        }
        
        console.log('✅ Enhanced results loaded successfully');
        res.json({ 
            available: true, 
            results, 
            timestamp: new Date().toISOString(),
            files_loaded: Object.keys(results)
        });
        
    } catch (error) {
        console.error('❌ Error loading enhanced results:', error);
        res.status(500).json({ 
            error: 'Failed to load enhanced results',
            details: error.message
        });
    }
});

// 获取多时间序列数据
app.get('/api/enhanced/timeseries', async (req, res) => {
    try {
        const { dimension, granularity } = req.query;
        
        const timeseriesPath = path.join(ROOT_DIR, 'enhanced_results/multi_timeseries.json');
        if (!await fs.pathExists(timeseriesPath)) {
            return res.status(404).json({ error: '多时间序列数据不存在' });
        }
        
        const timeseries = await fs.readJson(timeseriesPath);
        
        // 根据请求参数过滤数据
        let responseData = timeseries;
        
        if (dimension && timeseries[dimension]) {
            responseData = { [dimension]: timeseries[dimension] };
        }
        
        res.json(responseData);
        
    } catch (error) {
        console.error('Error loading timeseries data:', error);
        res.status(500).json({ error: 'Failed to load timeseries data' });
    }
});

// 获取可视化数据
app.get('/api/enhanced/visualization', async (req, res) => {
    try {
        const vizPath = path.join(ROOT_DIR, 'enhanced_results/visualization_data.json');
        if (!await fs.pathExists(vizPath)) {
            return res.status(404).json({ error: '可视化数据不存在' });
        }
        
        const vizData = await fs.readJson(vizPath);
        res.json(vizData);
        
    } catch (error) {
        console.error('Error loading visualization data:', error);
        res.status(500).json({ error: 'Failed to load visualization data' });
    }
});

// 手动生成演示结果 (用于测试和演示)
app.post('/api/enhanced/generate-demo', async (req, res) => {
    console.log('🎭 Generating demo enhanced results...');
    
    try {
        const outputDir = path.join(ROOT_DIR, 'enhanced_results');
        const success = await createFallbackResults(outputDir);
        
        if (success) {
            console.log('✅ Demo results generated successfully');
            res.json({ 
                success: true, 
                message: '演示结果已生成',
                output_directory: 'enhanced_results',
                note: 'This is demonstration data for testing the enhanced simulation interface'
            });
        } else {
            res.status(500).json({ error: 'Failed to generate demo results' });
        }
        
    } catch (error) {
        console.error('❌ Error generating demo results:', error);
        res.status(500).json({ 
            error: 'Failed to generate demo results',
            details: error.message 
        });
    }
});

// ===== 高级分析 API =====
// 探测可用的 final_report.json 列表
async function discoverFinalReports() {
    const candidates = [];

    async function pushIfExists(id, dirPath) {
        try {
            const reportPath = path.join(dirPath, 'final_report.json');
            if (await fs.pathExists(reportPath)) {
                const stat = await fs.stat(reportPath);
                candidates.push({ id, dir: dirPath, reportPath, mtime: stat.mtime });
            }
        } catch (_) {}
    }

    // 已知位置
    await pushIfExists('simulation_results', DATA_DIR);
    await pushIfExists('web_simulation_results', WEB_DATA_DIR);
    await pushIfExists('demo_results', path.join(ROOT_DIR, 'demo_results'));
    await pushIfExists('scenario_1_results', path.join(ROOT_DIR, 'scenario_1_results'));
    await pushIfExists('scenario_2_results', path.join(ROOT_DIR, 'scenario_2_results'));
    await pushIfExists('scenario_3_results', path.join(ROOT_DIR, 'scenario_3_results'));

    // 扫描根目录下其它 scenario_*_results 目录
    try {
        const items = await fs.readdir(ROOT_DIR);
        for (const item of items) {
            if (/^scenario_.*_results$/.test(item)) {
                await pushIfExists(item, path.join(ROOT_DIR, item));
            }
        }
    } catch (_) {}

    // 也尝试扫描 web-app 下 simulation_results 是否有单个文件
    try {
        const reportPath = path.join(WEB_DATA_DIR, 'final_report.json');
        if (await fs.pathExists(reportPath)) {
            const stat = await fs.stat(reportPath);
            candidates.push({ id: 'web_final', dir: WEB_DATA_DIR, reportPath, mtime: stat.mtime });
        }
    } catch (_) {}

    // 去重（按id）
    const seen = new Set();
    const unique = [];
    for (const c of candidates) {
        if (!seen.has(c.id)) { seen.add(c.id); unique.push(c); }
    }
    return unique.sort((a, b) => b.mtime - a.mtime);
}

async function loadFinalReportById(id) {
    const list = await discoverFinalReports();
    const item = list.find(x => x.id === id);
    if (!item) return null;
    return fs.readJson(item.reportPath);
}

async function loadLatestFinalReport() {
    const list = await discoverFinalReports();
    if (list.length === 0) return null;
    return fs.readJson(list[0].reportPath);
}

// 列出可用报告
app.get('/api/advanced/list', async (req, res) => {
    try {
        const list = await discoverFinalReports();
        res.json(list.map(x => ({ id: x.id, dir: x.dir, mtime: x.mtime })));
    } catch (err) {
        console.error('Error discovering reports:', err);
        res.status(500).json({ error: 'Failed to list reports' });
    }
});

// 获取最新报告（包含 advanced_analysis）
app.get('/api/advanced/latest', async (req, res) => {
    try {
        const report = await loadLatestFinalReport();
        if (!report) return res.status(404).json({ error: 'No final_report.json found' });
        res.json(report);
    } catch (err) {
        console.error('Error loading latest report:', err);
        res.status(500).json({ error: 'Failed to load latest report' });
    }
});

// 获取指定ID报告
app.get('/api/advanced/:id', async (req, res) => {
    try {
        const report = await loadFinalReportById(req.params.id);
        if (!report) return res.status(404).json({ error: 'Report not found' });
        res.json(report);
    } catch (err) {
        console.error('Error loading report by id:', err);
        res.status(500).json({ error: 'Failed to load report' });
    }
});

// 兼容路径：/api/simulations/:id/report -> 返回final_report
app.get('/api/simulations/:id/report', async (req, res) => {
    try {
        const report = await loadFinalReportById(req.params.id);
        if (!report) return res.status(404).json({ error: 'Report not found' });
        res.json(report);
    } catch (err) {
        console.error('Error loading simulation report:', err);
        res.status(500).json({ error: 'Failed to load simulation report' });
    }
});

// Favicon路由
app.get('/favicon.ico', (req, res) => {
    res.status(204).end();
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 智能体设计器路由
app.get('/agent-designer.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'agent-designer.html'));
});

// 高级智能体设计器路由
app.get('/advanced-agent-designer.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'advanced-agent-designer.html'));
});

// 增强仿真页面路由
app.get('/enhanced-simulation.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'enhanced-simulation.html'));
});

// 分析可视化页面路由
app.get('/analysis-visualizations.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'analysis-visualizations.html'));
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// 错误处理
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// 创建回退结果文件
async function createFallbackResults(outputDir) {
    console.log('🔧 Creating fallback enhanced results...');
    
    try {
        // 确保目录存在
        await fs.ensureDir(outputDir);
        
        const timestamp = new Date().toISOString();
        
        // 创建增强分析结果
        const enhancedAnalysis = {
            "generated_at": timestamp,
            "simulation_status": "completed_with_fallback",
            "note": "This is a demonstration result generated when the full simulation couldn't run",
            "multi_timeseries_analysis": {
                "overall": {
                    "总体趋势": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 180 + i * 8,
                        flow_amount: 1800 + i * 80,
                        success_rate: 0.83 + Math.random() * 0.1,
                        total_savings: 10000 + i * 500,
                        avg_income: 50 + i * 2
                    }))
                },
                "by_region": {
                    "广东": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 100 + i * 5,
                        flow_amount: 1000 + i * 50,
                        success_rate: 0.85 + Math.random() * 0.1,
                        total_savings: 5000 + i * 250,
                        avg_income: 55 + i * 2
                    })),
                    "福建": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 80 + i * 3,
                        flow_amount: 800 + i * 30,
                        success_rate: 0.80 + Math.random() * 0.15,
                        total_savings: 4000 + i * 200,
                        avg_income: 45 + i * 1.5
                    })),
                    "浙江": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 60 + i * 2,
                        flow_amount: 600 + i * 20,
                        success_rate: 0.82 + Math.random() * 0.12,
                        total_savings: 3000 + i * 150,
                        avg_income: 40 + i * 1.2
                    }))
                },
                "by_currency": {
                    "美元(USD)": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 120 + i * 6,
                        flow_amount: 1200 + i * 60,
                        success_rate: 0.90 + Math.random() * 0.08,
                        total_savings: 6000 + i * 300,
                        avg_income: 60 + i * 2.5
                    })),
                    "人民币(CNY)": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 90 + i * 4,
                        flow_amount: 900 + i * 40,
                        success_rate: 0.85 + Math.random() * 0.12,
                        total_savings: 4500 + i * 220,
                        avg_income: 45 + i * 1.8
                    })),
                    "港币(HKD)": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 70 + i * 3,
                        flow_amount: 700 + i * 30,
                        success_rate: 0.88 + Math.random() * 0.1,
                        total_savings: 3500 + i * 180,
                        avg_income: 50 + i * 2
                    }))
                },
                "by_institution": {
                    "汇丰银行": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 50 + i * 2.5,
                        flow_amount: 500 + i * 25,
                        success_rate: 0.92 + Math.random() * 0.05,
                        total_savings: 2500 + i * 125,
                        avg_income: 65 + i * 2.8
                    })),
                    "中国银行": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 45 + i * 2.2,
                        flow_amount: 450 + i * 22,
                        success_rate: 0.90 + Math.random() * 0.07,
                        total_savings: 2250 + i * 110,
                        avg_income: 55 + i * 2.3
                    })),
                    "侨批局": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 85 + i * 4,
                        flow_amount: 850 + i * 40,
                        success_rate: 0.87 + Math.random() * 0.09,
                        total_savings: 4250 + i * 200,
                        avg_income: 48 + i * 1.9
                    }))
                },
                "by_event": {
                    "经济繁荣期": Array.from({length: 8}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 200 + i * 10,
                        flow_amount: 2000 + i * 100,
                        success_rate: 0.90 + Math.random() * 0.05,
                        total_savings: 8000 + i * 400,
                        avg_income: 70 + i * 3
                    })),
                    "经济衰退期": Array.from({length: 5}, (_, i) => ({
                        year: 1928 + i,
                        step: i + 8,
                        flow_count: 150 - i * 8,
                        flow_amount: 1500 - i * 80,
                        success_rate: 0.75 + Math.random() * 0.1,
                        total_savings: 6000 - i * 200,
                        avg_income: 40 - i * 2
                    })),
                    "战争时期": Array.from({length: 8}, (_, i) => ({
                        year: 1933 + i,
                        step: i + 13,
                        flow_count: 100 - i * 5,
                        flow_amount: 1000 - i * 50,
                        success_rate: 0.60 + Math.random() * 0.15,
                        total_savings: 4000 - i * 150,
                        avg_income: 30 - i * 1
                    }))
                },
                "by_corridor": {
                    "东南亚-广东": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 70 + i * 3.5,
                        flow_amount: 700 + i * 35,
                        success_rate: 0.86 + Math.random() * 0.08,
                        total_savings: 3500 + i * 175,
                        avg_income: 52 + i * 2.1
                    })),
                    "美洲-福建": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 60 + i * 3,
                        flow_amount: 600 + i * 30,
                        success_rate: 0.83 + Math.random() * 0.1,
                        total_savings: 3000 + i * 150,
                        avg_income: 48 + i * 1.9
                    })),
                    "欧洲-浙江": Array.from({length: 21}, (_, i) => ({
                        year: 1920 + i,
                        step: i,
                        flow_count: 50 + i * 2.5,
                        flow_amount: 500 + i * 25,
                        success_rate: 0.81 + Math.random() * 0.11,
                        total_savings: 2500 + i * 125,
                        avg_income: 45 + i * 1.8
                    }))
                }
            },
            "trend_analysis": {
                "overall_trends": {
                    "remittance_volume": "increasing",
                    "success_rate": "stable",
                    "network_density": "growing"
                }
            },
            "correlation_analysis": {
                "remittance_success_correlation": 0.85,
                "economic_event_impact": 0.65,
                "regional_differences": 0.72,
                "correlation_matrix": [
                    [1.00, 0.85, 0.72, 0.68, 0.45],
                    [0.85, 1.00, 0.78, 0.71, 0.52],
                    [0.72, 0.78, 1.00, 0.65, 0.38],
                    [0.68, 0.71, 0.65, 1.00, 0.82],
                    [0.45, 0.52, 0.38, 0.82, 1.00]
                ],
                "variables": ["流量", "金额", "成功率", "储蓄", "收入"],
                "significant_correlations": [
                    {"pair": "流量-金额", "coefficient": 0.85, "p_value": 0.001},
                    {"pair": "储蓄-收入", "coefficient": 0.82, "p_value": 0.001},
                    {"pair": "流量-成功率", "coefficient": 0.72, "p_value": 0.005}
                ]
            },
            "clustering_analysis": {
                "migrant_behavior_clusters": {
                    "cluster_profiles": {
                        "conservative_savers": {
                            "size": 120,
                            "description": "低风险高储蓄率群体",
                            "characteristics": ["低风险偏好", "定期汇款", "家庭导向"]
                        },
                        "risk_takers": {
                            "size": 80,
                            "description": "高风险投资偏好群体",
                            "characteristics": ["投资导向", "金额较大", "频率较低"]
                        },
                        "family_focused": {
                            "size": 100,
                            "description": "家庭导向汇款群体",
                            "characteristics": ["定期小额", "稳定性高", "情感驱动"]
                        }
                    }
                }
            },
            "prediction_analysis": {
                "5_year_forecast": {
                    "future_years": [1941, 1942, 1943, 1944, 1945],
                    "predicted_savings": [15000, 16500, 18000, 19500, 21000],
                    "confidence_level": 0.85
                },
                "scenario_forecasts": {
                    "optimistic": {"predicted_volume": 25000, "predicted_success_rate": 0.95},
                    "realistic": {"predicted_volume": 20000, "predicted_success_rate": 0.85},
                    "pessimistic": {"predicted_volume": 15000, "predicted_success_rate": 0.70}
                }
            },
            "network_topology_analysis": {
                "network_density": 0.45,
                "centrality_analysis": {
                    "top_nodes": ["hong_kong", "singapore", "guangzhou", "xiamen", "manila"]
                },
                "clustering_coefficient": 0.68,
                "average_path_length": 3.2,
                "network_efficiency": 0.75
            },
            "event_impact_analysis": {
                "major_events": [
                    {
                        "name": "经济大萧条",
                        "period": "1929-1933",
                        "impact_level": "severe",
                        "affected_regions": ["全球"],
                        "recovery_time_months": 48,
                        "flow_reduction": 0.40
                    },
                    {
                        "name": "银价波动",
                        "period": "1934-1935",
                        "impact_level": "moderate",
                        "affected_regions": ["东南亚", "中国"],
                        "recovery_time_months": 12,
                        "flow_reduction": 0.20
                    },
                    {
                        "name": "抗日战争",
                        "period": "1937-1945",
                        "impact_level": "extreme",
                        "affected_regions": ["中国", "东南亚"],
                        "recovery_time_months": 96,
                        "flow_reduction": 0.60
                    }
                ],
                "impact_timeline": Array.from({length: 21}, (_, i) => {
                    const year = 1920 + i;
                    let impact = 0;
                    if (year >= 1929 && year <= 1933) impact = -0.40;
                    else if (year >= 1934 && year <= 1935) impact = -0.20;
                    else if (year >= 1937 && year <= 1940) impact = -0.60;
                    return {
                        year: year,
                        impact_factor: 1 + impact,
                        events: impact < 0 ? ["crisis"] : []
                    };
                }),
                "resilience_metrics": {
                    "average_recovery_time": 24,
                    "network_adaptability": 0.65,
                    "alternative_routes_efficiency": 0.70
                }
            }
        };
        
        // 保存主要结果文件
        const files = [
            {name: 'enhanced_analysis.json', data: enhancedAnalysis},
            {name: 'multi_timeseries.json', data: enhancedAnalysis.multi_timeseries_analysis},
            {name: 'visualization_data.json', data: {
                timeseries: enhancedAnalysis.multi_timeseries_analysis,
                heatmaps: [enhancedAnalysis.correlation_analysis],
                network_graphs: [enhancedAnalysis.network_topology_analysis]
            }},
            {name: 'final_report.json', data: {
                generated_at: timestamp,
                analysis_summary: {
                    timeseries_count: Object.keys(enhancedAnalysis.multi_timeseries_analysis.by_region).length + 
                                      Object.keys(enhancedAnalysis.multi_timeseries_analysis.by_currency).length +
                                      Object.keys(enhancedAnalysis.multi_timeseries_analysis.by_institution).length +
                                      Object.keys(enhancedAnalysis.multi_timeseries_analysis.by_event).length +
                                      Object.keys(enhancedAnalysis.multi_timeseries_analysis.by_corridor).length + 1,
                    cluster_count: Object.keys(enhancedAnalysis.clustering_analysis.migrant_behavior_clusters.cluster_profiles).length,
                    prediction_horizon: 5
                },
                key_findings: [
                    "生成多维度时间序列分析演示数据",
                    "识别出3个不同的移民行为群体模式",
                    "完成5年期汇款趋势预测分析",
                    "网络密度为0.45，显示中等连通性"
                ],
                enhanced_analysis: enhancedAnalysis
            }}
        ];
        
        for (const file of files) {
            const filePath = path.join(outputDir, file.name);
            await fs.writeJson(filePath, file.data, {spaces: 2});
            console.log(`✅ Created fallback file: ${file.name}`);
        }
        
        console.log('🎉 Fallback results created successfully!');
        return true;
        
    } catch (error) {
        console.error('❌ Error creating fallback results:', error);
        return false;
    }
}

// 辅助函数
async function readSimulationInfo(dirPath, dirName) {
    try {
        let info = {
            id: dirName,
            name: dirName,
            createdAt: null,
            status: 'unknown',
            agentCount: 0,
            timeSpan: {},
            description: ''
        };
        
        // 获取目录创建时间
        const stats = await fs.stat(dirPath);
        info.createdAt = stats.birthtime;
        
        return info;
    } catch (error) {
        console.error('Error reading simulation info:', error);
        return null;
    }
}

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 Qiaopi Simulation Web Server running on port ${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}`);
    console.log(`🤖 AI Agent Designer: http://localhost:${PORT}/agent-designer.html`);
    console.log(`🧠 Advanced Agent Designer: http://localhost:${PORT}/advanced-agent-designer.html`);
    console.log(`📈 API: http://localhost:${PORT}/api`);
    console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
    console.log('');
    console.log('🎯 AI智能体功能已启用！');
    console.log('   1. 访问主页面，点击"AI智能体"菜单');
    console.log('   2. 或直接访问智能体设计器页面');
    console.log('   3. 查看使用指南: AI_AGENT_GUIDE.md');
});

module.exports = app;