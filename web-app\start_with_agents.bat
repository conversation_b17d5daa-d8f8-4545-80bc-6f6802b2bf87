@echo off
echo ========================================
echo    侨批网络AI智能体仿真平台
echo ========================================
echo.

echo [1/3] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)
echo ✅ Node.js已安装

echo.
echo [2/3] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)
echo ✅ Python已安装

echo.
echo [3/3] 启动Web服务器...
echo 🚀 启动中，请稍候...
echo.
echo ================================
echo   访问地址:
echo   📊 主仪表盘: http://localhost:3508
echo   🤖 AI智能体设计器: http://localhost:3508/agent-designer.html
echo   🧠 高级AI设计器: http://localhost:3508/advanced-agent-designer.html
echo ================================
echo.
echo 按 Ctrl+C 停止服务器
echo.

node server.js
pause