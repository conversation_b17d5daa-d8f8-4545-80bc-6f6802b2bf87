#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web AI智能体系统
"""

import json
import subprocess
import os
import sys

def test_agent_bridge():
    """测试智能体桥接器"""
    print("🧪 测试AI智能体桥接系统...")
    
    # 测试配置
    test_config = {
        'type': 'migrant',
        'name': '测试海外移民智能体',
        'description': '这是一个用于测试的海外移民AI智能体',
        'count': 50,
        'lifespan': 60,
        'behavior': {
            'riskTolerance': 0.6,
            'cooperativeness': 0.8,
            'adaptability': 0.7,
            'decisionSpeed': 0.6
        },
        'learning': {
            'enabled': True,
            'learningRate': 0.02,
            'explorationRate': 0.1,
            'aiModel': 'reinforcement_learning',
            'enableReflection': True
        },
        'memory': {
            'capacity': 100,
            'episodic': True,
            'semantic': True,
            'procedural': False
        },
        'social': {
            'trustLevel': 0.7,
            'networkSize': 20,
            'influenceRadius': 10,
            'reputationSystem': True,
            'clanConnections': True
        },
        'economic': {
            'wealthLevel': 'middle',
            'savingsRate': 0.3,
            'investmentPropensity': 0.4,
            'remittanceFrequency': 3
        },
        'geographic': {
            'originRegion': 'guangdong',
            'destinationRegion': 'southeast_asia',
            'migrationYear': 1925,
            'mobilityLevel': 0.5
        }
    }
    
    print("📝 智能体配置:")
    print(json.dumps(test_config, indent=2, ensure_ascii=False))
    print()
    
    try:
        # 导入桥接器
        sys.path.append('web-app')
        from agent_api_bridge import create_agent_from_web, test_agent_from_web
        
        print("🤖 创建AI智能体...")
        result = create_agent_from_web(test_config)
        print("创建结果:", json.dumps(result, indent=2, ensure_ascii=False))
        
        if result.get('success'):
            print("✅ 智能体创建成功!")
            
            print("\n🧪 测试智能体功能...")
            test_result = test_agent_from_web(result['agent_id'], {
                'agent_config': test_config,
                'scenario': 'basic',
                'steps': 50
            })
            print("测试结果:", json.dumps(test_result, indent=2, ensure_ascii=False))
            
            if test_result.get('success'):
                print("✅ 智能体测试通过!")
                return True
            else:
                print("❌ 智能体测试失败!")
                return False
        else:
            print("❌ 智能体创建失败!")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保realistic_agents.py等依赖文件存在")
        return False
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        return False

def test_web_server():
    """测试Web服务器"""
    print("\n🌐 测试Web服务器...")
    
    try:
        import requests
        
        # 测试基础API
        response = requests.get('http://localhost:3508/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器正常运行")
            print(f"响应: {response.json()}")
            
            # 测试智能体API
            agent_response = requests.get('http://localhost:3508/api/agents', timeout=5)
            if agent_response.status_code == 200:
                print("✅ 智能体API正常")
                return True
            else:
                print("❌ 智能体API失败")
                return False
        else:
            print("❌ Web服务器响应失败")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器")
        print("💡 请确保服务器正在运行 (node server.js)")
        return False
    except ImportError:
        print("❌ requests库未安装")
        print("💡 请运行: pip install requests")
        return False
    except Exception as e:
        print(f"❌ Web服务器测试错误: {e}")
        return False

def main():
    print("=" * 50)
    print("   侨批网络AI智能体系统测试")
    print("=" * 50)
    
    # 测试智能体桥接
    agent_ok = test_agent_bridge()
    
    # 测试Web服务器
    server_ok = test_web_server()
    
    print("\n" + "=" * 50)
    print("   测试结果汇总")
    print("=" * 50)
    print(f"🤖 AI智能体系统: {'✅ 正常' if agent_ok else '❌ 异常'}")
    print(f"🌐 Web服务器系统: {'✅ 正常' if server_ok else '❌ 异常'}")
    
    if agent_ok and server_ok:
        print("\n🎉 系统测试通过！你可以开始使用AI智能体设计器了。")
        print("\n访问地址:")
        print("📊 主仪表盘: http://localhost:3508")
        print("🤖 AI智能体设计器: http://localhost:3508/agent-designer.html")
        print("🧠 高级AI设计器: http://localhost:3508/advanced-agent-designer.html")
    else:
        print("\n⚠️ 系统测试未完全通过，请检查上述错误信息。")
        
    return agent_ok and server_ok

if __name__ == '__main__':
    main()