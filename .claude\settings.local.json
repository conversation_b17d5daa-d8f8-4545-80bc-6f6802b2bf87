{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(npm:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(set:*)", "Bash(cp -r demo_results \"web-app/simulation_results/\")", "Bash(cp:*)", "Ba<PERSON>(cmd:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(PORT=3505 node server.js)", "Bash(PORT=3507 node server.js)", "Bash(PORT=3508 node server.js)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_click", "mcp__playwright__browser_evaluate", "Bash(PORT=3509 node server.js)", "mcp__playwright__browser_close", "Bash(PORT=3500 node server.js)"], "deny": [], "ask": []}}