"""
网络结构分析器
Network Structure Analyzer

专门用于分析侨批网络的结构特征和动力学属性，包括：
- 网络拓扑指标计算
- 中心性分析
- 网络演化追踪
- 鲁棒性评估
- 网络比较分析
"""

import networkx as nx
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from dataclasses import dataclass, asdict
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.cluster import SpectralClustering
from sklearn.metrics import silhouette_score
import torch
from torch_geometric.data import Data
from torch_geometric.utils import to_networkx, from_networkx

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class NetworkMetrics:
    """网络指标数据类"""
    # 基本指标
    num_nodes: int
    num_edges: int
    density: float
    
    # 连通性指标
    is_connected: bool
    num_components: int
    largest_component_size: int
    
    # 中心性指标
    avg_degree_centrality: float
    avg_betweenness_centrality: float
    avg_closeness_centrality: float
    avg_eigenvector_centrality: float
    
    # 聚类指标
    avg_clustering_coefficient: float
    global_clustering_coefficient: float
    transitivity: float
    
    # 路径指标
    avg_shortest_path_length: Optional[float]
    diameter: Optional[int]
    radius: Optional[int]
    
    # 度分布指标
    degree_assortativity: float
    avg_degree: float
    degree_variance: float
    
    # 小世界性质
    small_world_coefficient: Optional[float]
    
    # 模块化
    modularity: Optional[float]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass 
class GraphStatistics:
    """图统计信息"""
    node_statistics: Dict[str, Any]
    edge_statistics: Dict[str, Any] 
    community_statistics: Dict[str, Any]
    temporal_statistics: Optional[Dict[str, Any]] = None


class NetworkAnalyzer:
    """网络结构分析器"""
    
    def __init__(self):
        self.analysis_cache = {}
        logger.info("网络分析器初始化完成")
    
    def analyze_network(self, graph: Union[nx.Graph, Data], 
                       compute_expensive: bool = True) -> NetworkMetrics:
        """分析网络结构"""
        # 转换为NetworkX图
        if isinstance(graph, Data):
            G = to_networkx(graph, to_undirected=True)
        else:
            G = graph.copy()
        
        logger.info(f"分析网络: {G.number_of_nodes()} 个节点, {G.number_of_edges()} 条边")
        
        # 基本指标
        num_nodes = G.number_of_nodes()
        num_edges = G.number_of_edges()
        density = nx.density(G)
        
        # 连通性指标
        is_connected = nx.is_connected(G)
        components = list(nx.connected_components(G))
        num_components = len(components)
        largest_component_size = len(max(components, key=len)) if components else 0
        
        # 度相关指标
        degrees = dict(G.degree())
        avg_degree = np.mean(list(degrees.values()))
        degree_variance = np.var(list(degrees.values()))
        
        try:
            degree_assortativity = nx.degree_assortativity_coefficient(G)
        except:
            degree_assortativity = 0.0
        
        # 中心性指标
        degree_centrality = nx.degree_centrality(G)
        avg_degree_centrality = np.mean(list(degree_centrality.values()))
        
        # 计算其他中心性指标（可能耗时）
        if compute_expensive and is_connected:
            betweenness_centrality = nx.betweenness_centrality(G)
            closeness_centrality = nx.closeness_centrality(G)
            eigenvector_centrality = nx.eigenvector_centrality(G, max_iter=1000)
            
            avg_betweenness_centrality = np.mean(list(betweenness_centrality.values()))
            avg_closeness_centrality = np.mean(list(closeness_centrality.values()))
            avg_eigenvector_centrality = np.mean(list(eigenvector_centrality.values()))
        else:
            avg_betweenness_centrality = 0.0
            avg_closeness_centrality = 0.0
            avg_eigenvector_centrality = 0.0
        
        # 聚类指标
        clustering = nx.clustering(G)
        avg_clustering_coefficient = np.mean(list(clustering.values()))
        global_clustering_coefficient = nx.average_clustering(G)
        transitivity = nx.transitivity(G)
        
        # 路径指标
        if is_connected and compute_expensive:
            try:
                avg_shortest_path_length = nx.average_shortest_path_length(G)
                diameter = nx.diameter(G)
                radius = nx.radius(G)
            except:
                avg_shortest_path_length = None
                diameter = None
                radius = None
        else:
            avg_shortest_path_length = None
            diameter = None
            radius = None
        
        # 小世界系数
        small_world_coefficient = self._compute_small_world_coefficient(G)
        
        # 模块化
        modularity = self._compute_modularity(G)
        
        metrics = NetworkMetrics(
            num_nodes=num_nodes,
            num_edges=num_edges,
            density=density,
            is_connected=is_connected,
            num_components=num_components,
            largest_component_size=largest_component_size,
            avg_degree_centrality=avg_degree_centrality,
            avg_betweenness_centrality=avg_betweenness_centrality,
            avg_closeness_centrality=avg_closeness_centrality,
            avg_eigenvector_centrality=avg_eigenvector_centrality,
            avg_clustering_coefficient=avg_clustering_coefficient,
            global_clustering_coefficient=global_clustering_coefficient,
            transitivity=transitivity,
            avg_shortest_path_length=avg_shortest_path_length,
            diameter=diameter,
            radius=radius,
            degree_assortativity=degree_assortativity,
            avg_degree=avg_degree,
            degree_variance=degree_variance,
            small_world_coefficient=small_world_coefficient,
            modularity=modularity
        )
        
        return metrics
    
    def _compute_small_world_coefficient(self, G: nx.Graph) -> Optional[float]:
        """计算小世界系数"""
        try:
            if not nx.is_connected(G):
                return None
            
            # 实际的聚类系数和平均路径长度
            C = nx.average_clustering(G)
            L = nx.average_shortest_path_length(G)
            
            # 生成随机图作为比较基准
            n = G.number_of_nodes()
            m = G.number_of_edges()
            random_graph = nx.erdos_renyi_graph(n, 2*m/(n*(n-1)))
            
            C_rand = nx.average_clustering(random_graph)
            L_rand = nx.average_shortest_path_length(random_graph) if nx.is_connected(random_graph) else L
            
            # 小世界系数 = (C/C_rand) / (L/L_rand)
            if C_rand > 0 and L_rand > 0:
                small_world_coeff = (C / C_rand) / (L / L_rand)
                return small_world_coeff
            else:
                return None
        except:
            return None
    
    def _compute_modularity(self, G: nx.Graph) -> Optional[float]:
        """计算模块化"""
        try:
            # 使用贪心算法检测社区
            communities = nx.community.greedy_modularity_communities(G)
            modularity = nx.community.modularity(G, communities)
            return modularity
        except:
            return None
    
    def analyze_node_importance(self, graph: Union[nx.Graph, Data], 
                               top_k: int = 10) -> Dict[str, List[Tuple]]:
        """分析节点重要性"""
        # 转换为NetworkX图
        if isinstance(graph, Data):
            G = to_networkx(graph, to_undirected=True)
        else:
            G = graph.copy()
        
        importance_rankings = {}
        
        # 度中心性
        degree_centrality = nx.degree_centrality(G)
        importance_rankings['degree'] = sorted(
            degree_centrality.items(), key=lambda x: x[1], reverse=True
        )[:top_k]
        
        # 介数中心性
        if nx.is_connected(G):
            betweenness_centrality = nx.betweenness_centrality(G)
            importance_rankings['betweenness'] = sorted(
                betweenness_centrality.items(), key=lambda x: x[1], reverse=True
            )[:top_k]
        
        # 特征向量中心性
        try:
            eigenvector_centrality = nx.eigenvector_centrality(G, max_iter=1000)
            importance_rankings['eigenvector'] = sorted(
                eigenvector_centrality.items(), key=lambda x: x[1], reverse=True
            )[:top_k]
        except:
            importance_rankings['eigenvector'] = []
        
        # PageRank
        pagerank = nx.pagerank(G)
        importance_rankings['pagerank'] = sorted(
            pagerank.items(), key=lambda x: x[1], reverse=True
        )[:top_k]
        
        # HITS算法
        try:
            hits = nx.hits(G)
            hub_scores = hits[0]
            authority_scores = hits[1]
            
            importance_rankings['hub'] = sorted(
                hub_scores.items(), key=lambda x: x[1], reverse=True
            )[:top_k]
            
            importance_rankings['authority'] = sorted(
                authority_scores.items(), key=lambda x: x[1], reverse=True
            )[:top_k]
        except:
            importance_rankings['hub'] = []
            importance_rankings['authority'] = []
        
        return importance_rankings
    
    def analyze_degree_distribution(self, graph: Union[nx.Graph, Data]) -> Dict[str, Any]:
        """分析度分布"""
        # 转换为NetworkX图
        if isinstance(graph, Data):
            G = to_networkx(graph, to_undirected=True)
        else:
            G = graph.copy()
        
        degrees = [d for n, d in G.degree()]
        degree_sequence = sorted(degrees, reverse=True)
        
        # 基本统计
        degree_stats = {
            'mean': np.mean(degrees),
            'std': np.std(degrees),
            'min': np.min(degrees),
            'max': np.max(degrees),
            'median': np.median(degrees)
        }
        
        # 度分布
        degree_counts = defaultdict(int)
        for degree in degrees:
            degree_counts[degree] += 1
        
        # 幂律拟合
        power_law_fit = self._fit_power_law(degrees)
        
        # 度分布熵
        total_nodes = len(degrees)
        degree_probs = [count / total_nodes for count in degree_counts.values()]
        degree_entropy = -sum(p * np.log2(p) for p in degree_probs if p > 0)
        
        return {
            'degree_sequence': degree_sequence,
            'degree_counts': dict(degree_counts),
            'statistics': degree_stats,
            'power_law_fit': power_law_fit,
            'entropy': degree_entropy
        }
    
    def _fit_power_law(self, degrees: List[int]) -> Dict[str, Any]:
        """拟合幂律分布"""
        try:
            # 过滤零度节点
            non_zero_degrees = [d for d in degrees if d > 0]
            
            if len(non_zero_degrees) < 10:
                return {'fitted': False}
            
            # 对数变换
            log_degrees = np.log(non_zero_degrees)
            degree_counts = defaultdict(int)
            for degree in non_zero_degrees:
                degree_counts[degree] += 1
            
            unique_degrees = sorted(degree_counts.keys())
            counts = [degree_counts[d] for d in unique_degrees]
            log_counts = np.log(counts)
            
            # 线性回归
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                np.log(unique_degrees), log_counts
            )
            
            return {
                'fitted': True,
                'exponent': -slope,  # 幂指数
                'intercept': intercept,
                'r_squared': r_value ** 2,
                'p_value': p_value,
                'std_error': std_err
            }
        except:
            return {'fitted': False}
    
    def analyze_network_robustness(self, graph: Union[nx.Graph, Data], 
                                  attack_strategies: List[str] = None) -> Dict[str, Any]:
        """分析网络鲁棒性"""
        if attack_strategies is None:
            attack_strategies = ['random', 'degree', 'betweenness']
        
        # 转换为NetworkX图
        if isinstance(graph, Data):
            G = to_networkx(graph, to_undirected=True)
        else:
            G = graph.copy()
        
        original_size = G.number_of_nodes()
        robustness_results = {}
        
        for strategy in attack_strategies:
            G_copy = G.copy()
            removed_nodes = []
            connectivity_loss = []
            
            # 确定攻击顺序
            if strategy == 'random':
                attack_order = list(G_copy.nodes())
                np.random.shuffle(attack_order)
            elif strategy == 'degree':
                degree_centrality = nx.degree_centrality(G_copy)
                attack_order = sorted(degree_centrality.keys(), 
                                    key=lambda x: degree_centrality[x], reverse=True)
            elif strategy == 'betweenness':
                if nx.is_connected(G_copy):
                    betweenness_centrality = nx.betweenness_centrality(G_copy)
                    attack_order = sorted(betweenness_centrality.keys(),
                                        key=lambda x: betweenness_centrality[x], reverse=True)
                else:
                    attack_order = list(G_copy.nodes())
                    np.random.shuffle(attack_order)
            else:
                attack_order = list(G_copy.nodes())
            
            # 逐步移除节点
            for i, node in enumerate(attack_order):
                if node in G_copy:
                    G_copy.remove_node(node)
                    removed_nodes.append(node)
                    
                    # 计算连通性损失
                    if G_copy.number_of_nodes() > 0:
                        largest_cc = max(nx.connected_components(G_copy), key=len)
                        connectivity = len(largest_cc) / original_size
                    else:
                        connectivity = 0
                    
                    connectivity_loss.append(connectivity)
                    
                    # 如果网络完全分裂，停止
                    if connectivity < 0.1:
                        break
            
            robustness_results[strategy] = {
                'removed_nodes': removed_nodes,
                'connectivity_loss': connectivity_loss,
                'nodes_to_fragment': len(removed_nodes)
            }
        
        return robustness_results
    
    def compare_networks(self, graphs: List[Union[nx.Graph, Data]], 
                        labels: List[str] = None) -> pd.DataFrame:
        """比较多个网络"""
        if labels is None:
            labels = [f"Network_{i}" for i in range(len(graphs))]
        
        comparison_data = []
        
        for graph, label in zip(graphs, labels):
            metrics = self.analyze_network(graph, compute_expensive=False)
            metrics_dict = metrics.to_dict()
            metrics_dict['network_label'] = label
            comparison_data.append(metrics_dict)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        return comparison_df
    
    def analyze_temporal_evolution(self, graph_sequence: List[Union[nx.Graph, Data]], 
                                 timestamps: Optional[List] = None) -> Dict[str, Any]:
        """分析网络时序演化"""
        if timestamps is None:
            timestamps = list(range(len(graph_sequence)))
        
        evolution_metrics = defaultdict(list)
        
        for i, graph in enumerate(graph_sequence):
            metrics = self.analyze_network(graph, compute_expensive=False)
            
            # 记录各项指标的时序变化
            for key, value in metrics.to_dict().items():
                if isinstance(value, (int, float)) and value is not None:
                    evolution_metrics[key].append(value)
        
        # 计算变化趋势
        trends = {}
        for metric, values in evolution_metrics.items():
            if len(values) > 1:
                # 线性趋势
                x = np.arange(len(values))
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, values)
                
                trends[metric] = {
                    'slope': slope,
                    'r_squared': r_value ** 2,
                    'p_value': p_value,
                    'trend_direction': 'increasing' if slope > 0 else 'decreasing',
                    'significance': 'significant' if p_value < 0.05 else 'not_significant'
                }
        
        return {
            'timestamps': timestamps,
            'evolution_metrics': dict(evolution_metrics),
            'trends': trends,
            'summary': self._summarize_evolution(evolution_metrics, trends)
        }
    
    def _summarize_evolution(self, evolution_metrics: Dict, trends: Dict) -> Dict[str, Any]:
        """总结网络演化"""
        summary = {
            'total_timepoints': len(evolution_metrics.get('num_nodes', [])),
            'growth_metrics': {},
            'stability_metrics': {},
            'significant_trends': []
        }
        
        # 增长指标
        if 'num_nodes' in evolution_metrics:
            nodes = evolution_metrics['num_nodes']
            summary['growth_metrics']['node_growth'] = {
                'initial': nodes[0] if nodes else 0,
                'final': nodes[-1] if nodes else 0,
                'growth_rate': (nodes[-1] - nodes[0]) / len(nodes) if len(nodes) > 1 else 0
            }
        
        if 'num_edges' in evolution_metrics:
            edges = evolution_metrics['num_edges']
            summary['growth_metrics']['edge_growth'] = {
                'initial': edges[0] if edges else 0,
                'final': edges[-1] if edges else 0,
                'growth_rate': (edges[-1] - edges[0]) / len(edges) if len(edges) > 1 else 0
            }
        
        # 稳定性指标
        for metric, values in evolution_metrics.items():
            if len(values) > 1:
                summary['stability_metrics'][metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'coefficient_of_variation': np.std(values) / np.mean(values) if np.mean(values) != 0 else 0
                }
        
        # 显著趋势
        for metric, trend_info in trends.items():
            if trend_info['significance'] == 'significant':
                summary['significant_trends'].append({
                    'metric': metric,
                    'direction': trend_info['trend_direction'],
                    'slope': trend_info['slope'],
                    'r_squared': trend_info['r_squared']
                })
        
        return summary
    
    def detect_anomalies(self, graph: Union[nx.Graph, Data], 
                        reference_graphs: List[Union[nx.Graph, Data]] = None) -> Dict[str, Any]:
        """检测网络异常"""
        # 转换为NetworkX图
        if isinstance(graph, Data):
            G = to_networkx(graph, to_undirected=True)
        else:
            G = graph.copy()
        
        anomalies = {
            'structural_anomalies': [],
            'statistical_anomalies': [],
            'node_anomalies': [],
            'edge_anomalies': []
        }
        
        # 分析当前网络
        current_metrics = self.analyze_network(G)
        
        # 如果有参考网络，进行比较
        if reference_graphs:
            reference_metrics = []
            for ref_graph in reference_graphs:
                ref_metrics = self.analyze_network(ref_graph)
                reference_metrics.append(ref_metrics.to_dict())
            
            # 计算参考指标的分布
            ref_df = pd.DataFrame(reference_metrics)
            
            # 检测统计异常
            for metric in ref_df.columns:
                if metric in current_metrics.to_dict():
                    current_value = current_metrics.to_dict()[metric]
                    if current_value is not None:
                        ref_values = ref_df[metric].dropna()
                        
                        if len(ref_values) > 0:
                            mean_ref = ref_values.mean()
                            std_ref = ref_values.std()
                            
                            # Z-score异常检测
                            if std_ref > 0:
                                z_score = abs(current_value - mean_ref) / std_ref
                                if z_score > 3:  # 3-sigma规则
                                    anomalies['statistical_anomalies'].append({
                                        'metric': metric,
                                        'current_value': current_value,
                                        'reference_mean': mean_ref,
                                        'reference_std': std_ref,
                                        'z_score': z_score
                                    })
        
        # 结构异常检测
        # 检测度分布异常
        degree_dist = self.analyze_degree_distribution(G)
        if degree_dist['statistics']['max'] > 10 * degree_dist['statistics']['mean']:
            anomalies['structural_anomalies'].append({
                'type': 'extreme_hub',
                'description': '存在度数极端高的节点',
                'max_degree': degree_dist['statistics']['max'],
                'mean_degree': degree_dist['statistics']['mean']
            })
        
        # 检测孤立节点
        isolated_nodes = list(nx.isolates(G))
        if isolated_nodes:
            anomalies['node_anomalies'].append({
                'type': 'isolated_nodes',
                'count': len(isolated_nodes),
                'nodes': isolated_nodes[:10]  # 只显示前10个
            })
        
        # 检测异常密集的子图
        for component in nx.connected_components(G):
            if len(component) > 3:
                subgraph = G.subgraph(component)
                subgraph_density = nx.density(subgraph)
                
                if subgraph_density > 0.8:  # 密度过高
                    anomalies['structural_anomalies'].append({
                        'type': 'dense_subgraph',
                        'nodes': list(component),
                        'density': subgraph_density,
                        'size': len(component)
                    })
        
        return anomalies
    
    def generate_network_report(self, graph: Union[nx.Graph, Data], 
                               save_path: Optional[str] = None) -> Dict[str, Any]:
        """生成网络分析报告"""
        # 基础分析
        metrics = self.analyze_network(graph)
        node_importance = self.analyze_node_importance(graph)
        degree_distribution = self.analyze_degree_distribution(graph)
        robustness = self.analyze_network_robustness(graph)
        anomalies = self.detect_anomalies(graph)
        
        # 构建报告
        report = {
            'basic_metrics': metrics.to_dict(),
            'node_importance': node_importance,
            'degree_distribution': degree_distribution,
            'robustness_analysis': robustness,
            'anomaly_detection': anomalies,
            'summary': self._generate_network_summary(metrics, degree_distribution, robustness)
        }
        
        # 保存报告
        if save_path:
            import json
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, default=str, ensure_ascii=False)
            logger.info(f"网络分析报告已保存到 {save_path}")
        
        return report
    
    def _generate_network_summary(self, metrics: NetworkMetrics, 
                                 degree_dist: Dict, robustness: Dict) -> Dict[str, Any]:
        """生成网络摘要"""
        summary = {
            'network_type': self._classify_network_type(metrics, degree_dist),
            'connectivity_level': self._assess_connectivity(metrics),
            'clustering_level': self._assess_clustering(metrics),
            'robustness_level': self._assess_robustness(robustness),
            'key_insights': []
        }
        
        # 生成关键洞察
        insights = []
        
        # 连通性洞察
        if metrics.is_connected:
            insights.append("网络是连通的，信息可以在所有节点间传播")
        else:
            insights.append(f"网络有 {metrics.num_components} 个连通分量，存在信息传播障碍")
        
        # 小世界性质
        if metrics.small_world_coefficient and metrics.small_world_coefficient > 1:
            insights.append("网络具有小世界性质，兼具高聚类和短路径")
        
        # 度分布
        if degree_dist['power_law_fit']['fitted'] and degree_dist['power_law_fit']['r_squared'] > 0.8:
            insights.append("网络度分布符合幂律分布，具有无标度特性")
        
        # 中心化程度
        if metrics.avg_degree_centrality > 0.1:
            insights.append("网络具有明显的中心化结构")
        
        summary['key_insights'] = insights
        
        return summary
    
    def _classify_network_type(self, metrics: NetworkMetrics, degree_dist: Dict) -> str:
        """分类网络类型"""
        # 基于度分布和其他指标分类
        if degree_dist['power_law_fit']['fitted'] and degree_dist['power_law_fit']['r_squared'] > 0.8:
            return "scale_free"
        elif metrics.small_world_coefficient and metrics.small_world_coefficient > 1:
            return "small_world"
        elif metrics.density > 0.1:
            return "dense"
        elif metrics.avg_clustering_coefficient > 0.3:
            return "clustered"
        else:
            return "sparse"
    
    def _assess_connectivity(self, metrics: NetworkMetrics) -> str:
        """评估连通性水平"""
        if not metrics.is_connected:
            return "fragmented"
        elif metrics.avg_shortest_path_length and metrics.avg_shortest_path_length > 6:
            return "weakly_connected"
        elif metrics.avg_shortest_path_length and metrics.avg_shortest_path_length < 3:
            return "highly_connected"
        else:
            return "moderately_connected"
    
    def _assess_clustering(self, metrics: NetworkMetrics) -> str:
        """评估聚类水平"""
        if metrics.avg_clustering_coefficient > 0.5:
            return "highly_clustered"
        elif metrics.avg_clustering_coefficient > 0.2:
            return "moderately_clustered"
        else:
            return "loosely_clustered"
    
    def _assess_robustness(self, robustness: Dict) -> str:
        """评估鲁棒性水平"""
        # 基于随机攻击的结果评估
        if 'random' in robustness:
            nodes_to_fragment = robustness['random']['nodes_to_fragment']
            total_nodes = len(robustness['random']['removed_nodes']) + len(robustness['random']['connectivity_loss'])
            
            if total_nodes > 0:
                fragmentation_threshold = nodes_to_fragment / total_nodes
                if fragmentation_threshold > 0.7:
                    return "highly_robust"
                elif fragmentation_threshold > 0.3:
                    return "moderately_robust"
                else:
                    return "fragile"
        
        return "unknown"


# 使用示例
def main():
    """主函数示例"""
    # 创建示例网络
    G = nx.barabasi_albert_graph(100, 3)
    
    # 创建分析器
    analyzer = NetworkAnalyzer()
    
    # 分析网络
    metrics = analyzer.analyze_network(G)
    print("网络基本指标:")
    for key, value in metrics.to_dict().items():
        print(f"  {key}: {value}")
    
    # 分析节点重要性
    importance = analyzer.analyze_node_importance(G, top_k=5)
    print("\n节点重要性排名:")
    for centrality_type, rankings in importance.items():
        print(f"  {centrality_type}: {rankings[:3]}")
    
    # 分析度分布
    degree_dist = analyzer.analyze_degree_distribution(G)
    print(f"\n度分布统计: {degree_dist['statistics']}")
    
    # 生成完整报告
    report = analyzer.generate_network_report(G)
    print(f"\n网络类型: {report['summary']['network_type']}")
    print(f"连通性水平: {report['summary']['connectivity_level']}")


if __name__ == "__main__":
    main()
