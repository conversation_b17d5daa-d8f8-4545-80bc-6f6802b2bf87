#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能体演示脚本
"""

import json
import webbrowser
import time
import subprocess
import sys
import os

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("          🤖 侨批网络AI智能体仿真系统 🤖")
    print("=" * 60)
    print()

def print_features():
    """展示功能特性"""
    print("🚀 主要功能:")
    print("   ✨ 5种智能体类型: 移民、家庭、机构、政府、商人")
    print("   🧠 多种AI模型: 强化学习、神经网络、规则系统、混合模型") 
    print("   ⚙️ 详细参数配置: 行为、学习、记忆、社交等")
    print("   🧪 智能体测试: 行为验证、性能分析")
    print("   📊 仿真集成: 直接运行定制化仿真")
    print()

def show_agent_examples():
    """显示智能体示例"""
    print("🤖 AI智能体示例:")
    print()
    
    examples = [
        {
            "name": "保守型潮汕移民",
            "type": "migrant", 
            "characteristics": ["低风险偏好", "高合作性", "中等适应性", "重视宗族关系"],
            "ai_model": "强化学习",
            "use_case": "研究传统移民的稳健汇款策略"
        },
        {
            "name": "现代化金融机构",
            "type": "institution",
            "characteristics": ["数据驱动决策", "风险管控", "网络优化", "盈利导向"],
            "ai_model": "深度神经网络",
            "use_case": "分析现代金融科技对侨批业务的影响"
        },
        {
            "name": "政策敏感商人",
            "type": "merchant", 
            "characteristics": ["高风险偏好", "快速决策", "政策敏感", "市场适应"],
            "ai_model": "混合模型",
            "use_case": "研究政策变化对商业网络的冲击"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"   {i}. {example['name']} ({example['type']})")
        print(f"      特征: {', '.join(example['characteristics'])}")
        print(f"      AI模型: {example['ai_model']}")
        print(f"      应用: {example['use_case']}")
        print()

def show_web_interfaces():
    """显示Web界面说明"""
    print("🌐 Web界面说明:")
    print()
    print("   📊 主仪表盘 (http://localhost:3508)")
    print("      - 仿真结果可视化")
    print("      - 数据对比分析") 
    print("      - 系统概览统计")
    print()
    print("   🤖 智能体设计器 (http://localhost:3508/agent-designer.html)")
    print("      - 简单易用的智能体配置")
    print("      - 拖拽式参数调整")
    print("      - 实时预览效果")
    print()
    print("   🧠 高级智能体设计器 (http://localhost:3508/advanced-agent-designer.html)")
    print("      - 专业级AI模型配置")
    print("      - 深度学习参数调优")
    print("      - 神经网络架构设计")
    print()

def check_dependencies():
    """检查依赖"""
    print("🔧 检查系统依赖...")
    
    dependencies = []
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            dependencies.append(("Node.js", "✅", result.stdout.strip()))
        else:
            dependencies.append(("Node.js", "❌", "未安装"))
    except FileNotFoundError:
        dependencies.append(("Node.js", "❌", "未找到"))
    
    # 检查Python
    try:
        result = subprocess.run([sys.executable, '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            dependencies.append(("Python", "✅", result.stdout.strip()))
        else:
            dependencies.append(("Python", "❌", "版本检查失败"))
    except:
        dependencies.append(("Python", "❌", "未找到"))
    
    # 检查Python包
    required_packages = ['numpy', 'pandas']
    for package in required_packages:
        try:
            __import__(package)
            dependencies.append((f"Python {package}", "✅", "已安装"))
        except ImportError:
            dependencies.append((f"Python {package}", "❌", "未安装"))
    
    for name, status, version in dependencies:
        print(f"   {status} {name}: {version}")
    
    all_ok = all(status == "✅" for _, status, _ in dependencies)
    print(f"\n   总体状态: {'✅ 所有依赖正常' if all_ok else '❌ 部分依赖缺失'}")
    return all_ok

def start_demo():
    """启动演示"""
    print("🚀 启动AI智能体演示...")
    print()
    
    # 检查web-app目录
    web_app_dir = "web-app"
    if not os.path.exists(web_app_dir):
        print(f"❌ 错误: 未找到 {web_app_dir} 目录")
        print("💡 请确保在项目根目录运行此脚本")
        return False
    
    # 启动服务器说明
    print("📝 启动步骤:")
    print("   1. 打开终端，进入web-app目录")
    print("   2. 运行: node server.js")
    print("   3. 访问: http://localhost:3508")
    print("   4. 点击 'AI智能体' 菜单开始设计")
    print()
    
    # 询问是否自动启动
    response = input("🤔 是否尝试自动启动服务器? (y/n): ").lower()
    
    if response == 'y':
        try:
            print("🎯 启动Web服务器...")
            os.chdir(web_app_dir)
            
            # 启动服务器（非阻塞）
            process = subprocess.Popen(['node', 'server.js'], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE,
                                     text=True)
            
            # 等待服务器启动
            print("⏳ 等待服务器启动...")
            time.sleep(3)
            
            # 检查进程是否仍在运行
            if process.poll() is None:
                print("✅ 服务器启动成功!")
                print("🌐 正在打开浏览器...")
                
                # 打开浏览器
                webbrowser.open('http://localhost:3508')
                webbrowser.open('http://localhost:3508/agent-designer.html')
                
                print("\n" + "="*50)
                print("🎉 AI智能体设计器已启动!")
                print("📖 详细使用说明请参考: web-app/AI_AGENT_GUIDE.md")
                print("="*50)
                print("\n按 Ctrl+C 停止服务器...")
                
                # 等待用户中断
                try:
                    process.wait()
                except KeyboardInterrupt:
                    print("\n👋 正在停止服务器...")
                    process.terminate()
                    process.wait()
                    print("✅ 服务器已停止")
                
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ 服务器启动失败:")
                print(f"stdout: {stdout}")
                print(f"stderr: {stderr}")
                return False
                
        except FileNotFoundError:
            print("❌ 错误: 未找到node命令")
            print("💡 请确保Node.js已正确安装并添加到PATH")
            return False
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    return True

def show_usage_tips():
    """显示使用技巧"""
    print("💡 使用技巧:")
    print()
    print("   🎯 智能体设计:")
    print("      • 从简单配置开始，逐步增加复杂度")
    print("      • 使用预设模板快速开始")
    print("      • 每次配置后进行测试验证")
    print()
    print("   🧠 AI模型选择:")
    print("      • 强化学习: 适合动态决策和策略优化")
    print("      • 神经网络: 适合复杂模式识别")
    print("      • 规则系统: 适合明确逻辑的场景")
    print("      • 混合模型: 平衡性能与可解释性")
    print()
    print("   ⚡ 性能优化:")
    print("      • 智能体数量控制在100以内")
    print("      • 社交网络大小建议20-50") 
    print("      • 学习率设置在0.001-0.01范围")
    print("      • 定期清理不用的智能体")
    print()

def main():
    print_banner()
    print_features()
    show_agent_examples()
    show_web_interfaces()
    
    # 检查依赖
    deps_ok = check_dependencies()
    print()
    
    if not deps_ok:
        print("⚠️ 部分依赖缺失，可能影响功能使用")
        print("💡 建议安装缺失的依赖后重新运行")
        print()
    
    show_usage_tips()
    
    # 启动演示
    if start_demo():
        print("\n🎊 演示完成!")
    else:
        print("\n⚠️ 演示未能完全运行，请检查错误信息")
        print("📖 详细说明请参考: web-app/AI_AGENT_GUIDE.md")

if __name__ == '__main__':
    main()