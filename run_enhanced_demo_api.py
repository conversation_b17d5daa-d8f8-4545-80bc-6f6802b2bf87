#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版侨批仿真演示脚本 - API版本
Enhanced Qiaopi Simulation Demo - API Version
专为web API调用设计，无需用户交互
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime

try:
    from enhanced_simulation_engine import (
        EnhancedQiaopiSimulationEngine, 
        EnhancedAnalysisConfig,
        PRESET_SCENARIOS,
        create_enhanced_simulation_demo
    )
    from simulation_engine import SimulationConfig
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all required modules are available.")
    sys.exit(1)


def run_api_demo(config=None):
    """运行API版本的增强演示"""
    print("Starting enhanced Qiaopi simulation...")
    
    # 使用传入的配置或默认配置
    if config is None:
        config = {
            'start_year': 1920,
            'end_year': 1940,
            'num_migrants': 300,
            'num_families': 300,
            'num_institutions': 10,
            'output_directory': 'enhanced_results'
        }
    
    try:
        # 运行增强仿真演示
        results = create_enhanced_simulation_demo()
        
        # 确保输出目录存在
        output_dir = config.get('output_directory', 'enhanced_results')
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存主要结果文件
        enhanced_analysis_file = os.path.join(output_dir, 'enhanced_analysis.json')
        with open(enhanced_analysis_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存多时间序列数据
        if 'multi_timeseries_analysis' in results:
            multi_ts_file = os.path.join(output_dir, 'multi_timeseries.json')
            with open(multi_ts_file, 'w', encoding='utf-8') as f:
                json.dump(results['multi_timeseries_analysis'], f, ensure_ascii=False, indent=2, default=str)
        
        # 生成可视化数据
        viz_data = generate_visualization_data(results)
        viz_file = os.path.join(output_dir, 'visualization_data.json')
        with open(viz_file, 'w', encoding='utf-8') as f:
            json.dump(viz_data, f, ensure_ascii=False, indent=2, default=str)
        
        # 生成最终报告
        final_report = generate_final_report(results, config)
        report_file = os.path.join(output_dir, 'final_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2, default=str)
        
        print("Enhanced simulation completed successfully!")
        print(f"Results saved to: {output_dir}/")
        
        return {
            'success': True,
            'results': results,
            'output_directory': output_dir,
            'files_generated': [
                enhanced_analysis_file,
                multi_ts_file,
                viz_file,
                report_file
            ]
        }
        
    except Exception as e:
        print(f"Simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }


def generate_visualization_data(results):
    """生成可视化数据"""
    viz_data = {
        'charts': [],
        'heatmaps': [],
        'network_graphs': [],
        'timeseries': []
    }
    
    # 时间序列图表数据
    multi_ts = results.get('multi_timeseries_analysis', {})
    for dimension, series_data in multi_ts.items():
        if isinstance(series_data, dict):
            for series_name, data in series_data.items():
                if isinstance(data, list) and len(data) > 0:
                    viz_data['timeseries'].append({
                        'dimension': dimension,
                        'series_name': series_name,
                        'data': data,
                        'type': 'line_chart'
                    })
    
    # 相关性热力图数据
    correlation = results.get('correlation_analysis', {})
    if correlation:
        viz_data['heatmaps'].append({
            'type': 'correlation_matrix',
            'data': correlation,
            'title': '相关性矩阵'
        })
    
    # 网络图数据
    network = results.get('network_topology_analysis', {})
    if network:
        viz_data['network_graphs'].append({
            'type': 'network_topology',
            'data': network,
            'title': '网络拓扑结构'
        })
    
    return viz_data


def generate_final_report(results, config):
    """生成最终报告"""
    report = {
        'generated_at': datetime.now().isoformat(),
        'simulation_config': config,
        'analysis_summary': {},
        'key_findings': [],
        'enhanced_analysis': results
    }
    
    # 分析摘要
    summary = {}
    
    # 时间序列分析摘要
    multi_ts = results.get('multi_timeseries_analysis', {})
    if multi_ts:
        summary['timeseries_analysis'] = {
            'dimensions_analyzed': len(multi_ts),
            'total_series': sum(len(v) if isinstance(v, dict) else 0 for v in multi_ts.values()),
        }
    
    # 聚类分析摘要
    clustering = results.get('clustering_analysis', {})
    if clustering:
        clusters = clustering.get('migrant_behavior_clusters', {}).get('cluster_profiles', {})
        summary['clustering_analysis'] = {
            'behavior_clusters_identified': len(clusters),
        }
    
    # 预测分析摘要
    prediction = results.get('prediction_analysis', {})
    if prediction:
        summary['prediction_analysis'] = {
            'forecast_horizon_years': 5,
            'scenarios_analyzed': len(prediction.get('scenario_forecasts', {})),
        }
    
    # 网络分析摘要
    network = results.get('network_topology_analysis', {})
    if network:
        summary['network_analysis'] = {
            'network_density': network.get('network_density', 0),
            'central_nodes': len(network.get('centrality_analysis', {}).get('top_nodes', [])),
        }
    
    report['analysis_summary'] = summary
    
    # 关键发现
    findings = []
    
    if multi_ts:
        findings.append("成功生成多维度时间序列分析，涵盖地理、货币、机构等维度")
    
    if clustering:
        cluster_count = len(clustering.get('migrant_behavior_clusters', {}).get('cluster_profiles', {}))
        if cluster_count > 0:
            findings.append(f"识别出{cluster_count}个不同的移民行为群体模式")
    
    if prediction:
        findings.append("完成5年期汇款趋势预测分析")
    
    if network:
        density = network.get('network_density', 0)
        findings.append(f"网络密度为{density:.4f}，显示了网络连通性特征")
    
    report['key_findings'] = findings
    
    return report


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Enhanced Qiaopi Simulation API Demo')
    parser.add_argument('--config', type=str, help='配置文件路径 (JSON格式)')
    parser.add_argument('--start-year', type=int, default=1920, help='开始年份')
    parser.add_argument('--end-year', type=int, default=1940, help='结束年份')
    parser.add_argument('--migrants', type=int, default=300, help='移民数量')
    parser.add_argument('--families', type=int, default=300, help='家庭数量')
    parser.add_argument('--institutions', type=int, default=10, help='机构数量')
    parser.add_argument('--output-dir', type=str, default='enhanced_results', help='输出目录')
    
    args = parser.parse_args()
    
    # 准备配置
    if args.config:
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
        except Exception as e:
            print(f"Failed to read config file: {e}")
            sys.exit(1)
    else:
        config = {
            'start_year': args.start_year,
            'end_year': args.end_year,
            'num_migrants': args.migrants,
            'num_families': args.families,
            'num_institutions': args.institutions,
            'output_directory': args.output_dir
        }
    
    # 运行API演示
    result = run_api_demo(config)
    
    if result['success']:
        print("API demo completed successfully")
        sys.exit(0)
    else:
        print("API demo failed")
        sys.exit(1)


if __name__ == "__main__":
    main()