<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能演示 - 侨批网络AI智能体系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-container { 
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .feature-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: white;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #667eea;
        }
        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-working { background: #d4edda; color: #155724; }
        .status-enhanced { background: #cce5ff; color: #004085; }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <div class="text-center mb-5">
                <h1><i class="fas fa-rocket"></i> 侨批网络AI智能体系统功能演示</h1>
                <p class="lead">现在所有功能都有实际内容，不再是空的界面！</p>
                <div class="alert alert-success">
                    <strong>🎉 问题已解决!</strong> "界面太简单，不知道如何定义AI智能体" → 现在有专业的AI智能体设计系统！
                </div>
            </div>

            <div class="row">
                <!-- 主仪表盘功能 -->
                <div class="col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h4>主仪表盘</h4>
                        <p class="text-muted">包含丰富的仿真结果、场景分析和AI智能体管理</p>
                        <div class="mb-3">
                            <span class="status-badge status-enhanced">✨ 功能增强</span>
                        </div>
                        <ul class="text-start small">
                            <li>✅ 3个演示仿真结果</li>
                            <li>✅ 5个详细配置场景</li>
                            <li>✅ 完整AI智能体管理</li>
                            <li>✅ 真实数据统计显示</li>
                        </ul>
                        <a href="http://localhost:3508" class="demo-btn" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>访问主仪表盘
                        </a>
                    </div>
                </div>

                <!-- AI智能体设计器 -->
                <div class="col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h4>AI智能体设计器</h4>
                        <p class="text-muted">可视化设计和配置AI智能体，零编程门槛</p>
                        <div class="mb-3">
                            <span class="status-badge status-working">🆕 全新功能</span>
                        </div>
                        <ul class="text-start small">
                            <li>🤖 5种智能体类型</li>
                            <li>🧠 4种AI模型配置</li>
                            <li>⚙️ 详细参数调节</li>
                            <li>📊 实时预览效果</li>
                        </ul>
                        <a href="http://localhost:3508/agent-designer.html" class="demo-btn" target="_blank">
                            <i class="fas fa-magic me-2"></i>开始设计智能体
                        </a>
                    </div>
                </div>

                <!-- 高级AI设计器 -->
                <div class="col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>高级AI设计器</h4>
                        <p class="text-muted">专业级AI模型配置，支持深度学习和强化学习</p>
                        <div class="mb-3">
                            <span class="status-badge status-working">🔬 专业级</span>
                        </div>
                        <ul class="text-start small">
                            <li>🧠 深度神经网络配置</li>
                            <li>🎯 强化学习参数调优</li>
                            <li>📈 5步向导式流程</li>
                            <li>🎨 行为特征可视化</li>
                        </ul>
                        <a href="http://localhost:3508/advanced-agent-designer.html" class="demo-btn" target="_blank">
                            <i class="fas fa-cogs me-2"></i>高级配置
                        </a>
                    </div>
                </div>

                <!-- 完整功能演示 -->
                <div class="col-md-6 mb-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h4>完整功能演示</h4>
                        <p class="text-muted">体验从智能体设计到仿真运行的完整流程</p>
                        <div class="mb-3">
                            <span class="status-badge status-enhanced">🎪 互动演示</span>
                        </div>
                        <ul class="text-start small">
                            <li>🎯 完整设计流程</li>
                            <li>🔬 智能体测试验证</li>
                            <li>🚀 仿真运行体验</li>
                            <li>📊 结果分析展示</li>
                        </ul>
                        <button class="demo-btn" onclick="startFullDemo()">
                            <i class="fas fa-rocket me-2"></i>开始完整演示
                        </button>
                    </div>
                </div>
            </div>

            <!-- 功能对比 -->
            <div class="row mt-5">
                <div class="col-12">
                    <h3 class="text-center mb-4">🔥 功能对比：解决了你的问题</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>功能</th>
                                    <th class="text-danger">❌ 原来 (简单界面)</th>
                                    <th class="text-success">✅ 现在 (AI智能体系统)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>智能体定义</strong></td>
                                    <td>❌ 无法定义，需要编程</td>
                                    <td>✅ 可视化设计器，5种类型</td>
                                </tr>
                                <tr>
                                    <td><strong>AI模型配置</strong></td>
                                    <td>❌ 无配置选项</td>
                                    <td>✅ 4种AI模型，详细参数调节</td>
                                </tr>
                                <tr>
                                    <td><strong>参数调整</strong></td>
                                    <td>❌ 需要修改代码</td>
                                    <td>✅ 滑块拖拽，实时预览</td>
                                </tr>
                                <tr>
                                    <td><strong>仿真运行</strong></td>
                                    <td>❌ 复杂的命令行操作</td>
                                    <td>✅ 一键运行，自动生成结果</td>
                                </tr>
                                <tr>
                                    <td><strong>结果展示</strong></td>
                                    <td>❌ 基础数据显示</td>
                                    <td>✅ 丰富的可视化和统计</td>
                                </tr>
                                <tr>
                                    <td><strong>用户体验</strong></td>
                                    <td>❌ 需要专业知识</td>
                                    <td>✅ 零编程门槛，任何人可用</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 立即体验 -->
            <div class="text-center mt-5 p-4" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 15px; color: white;">
                <h3><i class="fas fa-star"></i> 立即体验全新的AI智能体系统</h3>
                <p class="mb-4">不再是简单界面，而是功能完整的专业级平台！</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="http://localhost:3508" class="btn btn-light btn-lg" target="_blank">
                        <i class="fas fa-home me-2"></i>主仪表盘
                    </a>
                    <a href="http://localhost:3508/#agents" class="btn btn-light btn-lg" target="_blank">
                        <i class="fas fa-robot me-2"></i>AI智能体管理
                    </a>
                    <a href="http://localhost:3508/agent-designer.html" class="btn btn-light btn-lg" target="_blank">
                        <i class="fas fa-magic me-2"></i>智能体设计器
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function startFullDemo() {
            alert(`🎪 完整演示流程：

1. 🖱️ 首先访问主仪表盘看到丰富内容
2. 🖱️ 点击"AI智能体"菜单进入管理界面  
3. 🖱️ 使用快速模板创建智能体
4. 🖱️ 在设计器中配置参数和AI模型
5. 🖱️ 创建智能体后运行专属仿真
6. 🖱️ 查看仿真结果和数据分析

现在将为你打开主页面开始演示...`);
            
            // 打开主页面
            window.open('http://localhost:3508', '_blank');
            
            // 延迟打开AI智能体页面
            setTimeout(() => {
                window.open('http://localhost:3508/#agents', '_blank');
            }, 2000);
            
            // 延迟打开设计器
            setTimeout(() => {
                window.open('http://localhost:3508/agent-designer.html', '_blank');
            }, 4000);
        }

        // 页面加载完成后显示状态
        document.addEventListener('DOMContentLoaded', function() {
            // 检查服务器状态
            fetch('http://localhost:3508/api/health')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'alert alert-success text-center';
                    statusDiv.innerHTML = `
                        <h5><i class="fas fa-check-circle me-2"></i>服务器运行正常</h5>
                        <p class="mb-0">状态: ${data.status} | 时间: ${new Date(data.timestamp).toLocaleString()}</p>
                    `;
                    document.querySelector('.demo-container').prepend(statusDiv);
                })
                .catch(error => {
                    const statusDiv = document.createElement('div');
                    statusDiv.className = 'alert alert-warning text-center';
                    statusDiv.innerHTML = `
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>请启动服务器</h5>
                        <p class="mb-0">请运行: <code>cd web-app && node server.js</code></p>
                    `;
                    document.querySelector('.demo-container').prepend(statusDiv);
                });
        });
    </script>
</body>
</html>