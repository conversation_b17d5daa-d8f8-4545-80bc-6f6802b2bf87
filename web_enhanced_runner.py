#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web环境增强仿真运行器
Web Enhanced Simulation Runner
"""

import sys
import os
import json
import argparse
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enhanced_simulation_engine import (
    EnhancedQiaopiSimulationEngine, 
    EnhancedAnalysisConfig
)
from simulation_engine import SimulationConfig


def run_web_enhanced_simulation(config_file=None, **kwargs):
    """为Web环境运行增强仿真"""
    print("🌐 Web环境增强仿真启动")
    print("="*40)
    
    # 读取配置
    if config_file and os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
    else:
        config_data = kwargs
    
    # 创建仿真配置
    sim_config = SimulationConfig(
        start_year=config_data.get('start_year', 1920),
        end_year=config_data.get('end_year', 1940),
        num_migrants=config_data.get('num_migrants', 300),
        num_families=config_data.get('num_families', 300),
        num_institutions=config_data.get('num_institutions', 10),
        output_directory=config_data.get('output_directory', 'web-app/enhanced_results'),
        enable_personalization=config_data.get('enable_personalization', True)
    )
    
    # 创建分析配置
    analysis_config = EnhancedAnalysisConfig(
        enable_multi_timeseries=config_data.get('enable_multi_timeseries', True),
        enable_geographical_analysis=config_data.get('enable_geographical_analysis', True),
        enable_currency_analysis=config_data.get('enable_currency_analysis', True),
        enable_network_analysis=config_data.get('enable_network_analysis', True),
        enable_event_analysis=config_data.get('enable_event_analysis', True),
        enable_trend_analysis=config_data.get('enable_trend_analysis', True),
        enable_correlation_analysis=config_data.get('enable_correlation_analysis', True),
        enable_clustering_analysis=config_data.get('enable_clustering_analysis', True),
        enable_prediction_analysis=config_data.get('enable_prediction_analysis', True),
        generate_detailed_reports=True,
        export_visualization_data=True
    )
    
    try:
        # 创建输出目录
        os.makedirs(sim_config.output_directory, exist_ok=True)
        
        # 运行增强仿真
        print(f"📊 配置: {sim_config.start_year}-{sim_config.end_year}, {sim_config.num_migrants}移民")
        print("🚀 开始仿真...")
        
        engine = EnhancedQiaopiSimulationEngine(sim_config, analysis_config)
        results = engine.run_enhanced_simulation()
        
        # 生成Web友好的摘要
        web_summary = generate_web_summary(results)
        
        # 保存Web摘要
        summary_path = os.path.join(sim_config.output_directory, 'web_summary.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(web_summary, f, ensure_ascii=False, indent=2)
        
        print("✅ 增强仿真完成!")
        print(f"📁 结果保存在: {sim_config.output_directory}")
        print(f"📊 时间序列数量: {web_summary['statistics']['timeseries_count']}")
        print(f"🎯 聚类群体数量: {web_summary['statistics']['cluster_count']}")
        print(f"📈 分析模块: {web_summary['statistics']['analysis_modules']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 仿真失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def generate_web_summary(results):
    """生成Web友好的摘要"""
    summary = {
        'generated_at': datetime.now().isoformat(),
        'simulation_info': results.get('simulation_summary', {}),
        'statistics': {
            'timeseries_count': 0,
            'cluster_count': 0,
            'analysis_modules': 0,
            'prediction_horizon': 0
        },
        'key_insights': [],
        'charts_data': {},
        'status': 'completed'
    }
    
    # 统计时间序列数量
    multi_ts = results.get('multi_timeseries_analysis', {})
    if multi_ts:
        summary['statistics']['timeseries_count'] = (
            len(multi_ts.get('regional_series', {})) + 
            len(multi_ts.get('currency_series', {})) + 
            1  # overall series
        )
    
    # 统计聚类数量
    clustering = results.get('clustering_analysis', {})
    if clustering:
        clusters = clustering.get('migrant_behavior_clusters', {}).get('cluster_profiles', {})
        summary['statistics']['cluster_count'] = len(clusters)
    
    # 统计分析模块
    enhanced = results.get('enhanced_analysis') or results
    if enhanced:
        analysis_keys = [
            'multi_timeseries_analysis', 'trend_analysis', 'correlation_analysis',
            'clustering_analysis', 'prediction_analysis', 'network_topology_analysis',
            'event_impact_analysis'
        ]
        summary['statistics']['analysis_modules'] = sum(1 for key in analysis_keys if key in enhanced)
    
    # 预测分析
    prediction = enhanced.get('prediction_analysis', {}) if enhanced else {}
    if prediction.get('5_year_forecast'):
        summary['statistics']['prediction_horizon'] = 5
    
    # 关键洞察
    if enhanced:
        # 趋势洞察
        trends = enhanced.get('trend_analysis', {}).get('overall_trends', {})
        for metric, trend in trends.items():
            summary['key_insights'].append(f"{metric}: {trend}")
        
        # 聚类洞察
        if summary['statistics']['cluster_count'] > 0:
            summary['key_insights'].append(f"识别出{summary['statistics']['cluster_count']}个不同的智能体行为群体")
        
        # 网络洞察
        network = enhanced.get('network_topology_analysis', {})
        if network.get('network_density'):
            density = network['network_density'] * 100
            summary['key_insights'].append(f"网络密度: {density:.1f}%")
    
    return summary


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Web环境增强仿真')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--start-year', type=int, default=1920, help='开始年份')
    parser.add_argument('--end-year', type=int, default=1940, help='结束年份')
    parser.add_argument('--migrants', type=int, default=300, help='移民数量')
    parser.add_argument('--families', type=int, default=300, help='家庭数量')
    parser.add_argument('--institutions', type=int, default=10, help='机构数量')
    parser.add_argument('--output-dir', type=str, default='web-app/enhanced_results', help='输出目录')
    
    args = parser.parse_args()
    
    # 运行仿真
    config_params = {
        'start_year': args.start_year,
        'end_year': args.end_year,
        'num_migrants': args.migrants,
        'num_families': args.families,
        'num_institutions': args.institutions,
        'output_directory': args.output_dir,
        'enable_multi_timeseries': True,
        'enable_advanced_analysis': True
    }
    
    success = run_web_enhanced_simulation(args.config, **config_params)
    
    if success:
        print("\n🎉 Web增强仿真完成!")
        print("🌐 可在Dashboard中查看结果: http://localhost:3508/enhanced-simulation.html")
    else:
        print("\n❌ 仿真失败，请检查错误信息")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)