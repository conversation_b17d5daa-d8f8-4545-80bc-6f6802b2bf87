#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版仿真功能
Test Enhanced Simulation Features
"""

import sys
import os
import json
from enhanced_simulation_engine import (
    EnhancedQiaopiSimulationEngine, 
    EnhancedAnalysisConfig
)
from simulation_engine import SimulationConfig


def test_multi_timeseries():
    """测试多时间序列功能"""
    print("🧪 测试多时间序列功能...")
    
    # 创建小规模测试配置
    config = SimulationConfig(
        start_year=1925,
        end_year=1930,  # 短期测试
        num_migrants=50,
        num_families=50,
        num_institutions=5,
        output_directory="test_results"
    )
    
    analysis_config = EnhancedAnalysisConfig(
        enable_multi_timeseries=True,
        enable_geographical_analysis=True,
        enable_currency_analysis=True,
        timeseries_granularities=['monthly']
    )
    
    # 运行测试
    engine = EnhancedQiaopiSimulationEngine(config, analysis_config)
    results = engine.run_enhanced_simulation()
    
    # 验证结果
    multi_ts = results.get('multi_timeseries_analysis', {})
    
    print("   📊 测试结果:")
    print(f"      整体时间序列: {'✅' if multi_ts.get('overall_series') else '❌'}")
    print(f"      地区时间序列: {'✅' if multi_ts.get('regional_series') else '❌'} ({len(multi_ts.get('regional_series', {}))}个)")
    print(f"      货币时间序列: {'✅' if multi_ts.get('currency_series') else '❌'} ({len(multi_ts.get('currency_series', {}))}个)")
    print()
    
    return len(multi_ts.get('regional_series', {})) > 0


def test_advanced_analysis():
    """测试高级分析功能"""
    print("🧪 测试高级分析功能...")
    
    config = SimulationConfig(
        start_year=1920,
        end_year=1925,
        num_migrants=80,
        num_families=80,
        num_institutions=6,
        output_directory="test_advanced"
    )
    
    analysis_config = EnhancedAnalysisConfig(
        enable_trend_analysis=True,
        enable_correlation_analysis=True,
        enable_clustering_analysis=True,
        enable_prediction_analysis=True
    )
    
    engine = EnhancedQiaopiSimulationEngine(config, analysis_config)
    results = engine.run_enhanced_simulation()
    
    # 验证高级分析结果
    print("   📊 测试结果:")
    
    trend_analysis = results.get('trend_analysis', {})
    print(f"      趋势分析: {'✅' if trend_analysis.get('overall_trends') else '❌'}")
    
    correlation_analysis = results.get('correlation_analysis', {})
    print(f"      相关性分析: {'✅' if correlation_analysis.get('key_metrics') else '❌'}")
    
    clustering_analysis = results.get('clustering_analysis', {})
    migrant_clusters = clustering_analysis.get('migrant_behavior_clusters', {}).get('cluster_profiles', {})
    print(f"      聚类分析: {'✅' if migrant_clusters else '❌'} ({len(migrant_clusters)}个聚类)")
    
    prediction_analysis = results.get('prediction_analysis', {})
    print(f"      预测分析: {'✅' if prediction_analysis.get('5_year_forecast') else '❌'}")
    
    network_analysis = results.get('network_topology_analysis', {})
    print(f"      网络分析: {'✅' if network_analysis.get('network_density') is not None else '❌'}")
    print()
    
    return len(migrant_clusters) > 0


def test_scenario_comparison():
    """测试场景对比功能"""
    print("🧪 测试场景对比功能...")
    
    # 定义两个对比场景
    test_scenarios = [
        {
            'description': '保守场景',
            'start_year': 1920,
            'end_year': 1925,
            'num_migrants': 40,
            'num_families': 40,
            'num_institutions': 3,
            'migration_probability_per_step': 0.005,
            'output_directory': 'test_conservative'
        },
        {
            'description': '激进场景', 
            'start_year': 1920,
            'end_year': 1925,
            'num_migrants': 60,
            'num_families': 60,
            'num_institutions': 5,
            'migration_probability_per_step': 0.02,
            'output_directory': 'test_aggressive'
        }
    ]
    
    base_config = SimulationConfig(
        start_year=1920,
        end_year=1925,
        num_migrants=50,
        num_families=50,
        num_institutions=4,
        output_directory="test_comparison_base"
    )
    
    analysis_config = EnhancedAnalysisConfig(enable_scenario_comparison=True)
    
    engine = EnhancedQiaopiSimulationEngine(base_config, analysis_config)
    results = engine.run_multi_scenario_analysis(test_scenarios)
    
    # 验证对比结果
    scenarios = results.get('scenarios', {})
    comparison = results.get('comparison', {})
    
    print("   📊 测试结果:")
    print(f"      场景数量: {len(scenarios)}")
    print(f"      对比指标: {len(comparison)}")
    
    for scenario_name, scenario_data in scenarios.items():
        final_stats = scenario_data['results']['final_statistics']
        total_qiaopi = final_stats['qiaopi_statistics']['total_qiaopi_sent']
        success_rate = final_stats['qiaopi_statistics']['success_rate']
        print(f"      📈 {scenario_name}: 侨批={total_qiaopi}, 成功率={success_rate:.2%}")
    
    print()
    return len(scenarios) > 1


def test_visualization_export():
    """测试可视化数据导出"""
    print("🧪 测试可视化数据导出...")
    
    config = SimulationConfig(
        start_year=1920,
        end_year=1923,
        num_migrants=30,
        num_families=30,
        num_institutions=3,
        output_directory="test_viz"
    )
    
    analysis_config = EnhancedAnalysisConfig(export_visualization_data=True)
    
    engine = EnhancedQiaopiSimulationEngine(config, analysis_config)
    results = engine.run_enhanced_simulation()
    
    # 检查可视化文件是否生成
    viz_file = "test_viz/visualization_data.json"
    viz_exists = os.path.exists(viz_file)
    
    print(f"   📊 可视化数据文件: {'✅' if viz_exists else '❌'}")
    
    if viz_exists:
        with open(viz_file, 'r', encoding='utf-8') as f:
            viz_data = json.load(f)
        
        charts = viz_data.get('timeseries_charts', {})
        heatmaps = viz_data.get('heatmaps', {})
        networks = viz_data.get('network_graphs', {})
        correlations = viz_data.get('correlation_matrices', {})
        
        print(f"      时间序列图表: {'✅' if charts else '❌'}")
        print(f"      热力图数据: {'✅' if heatmaps else '❌'}")
        print(f"      网络图数据: {'✅' if networks else '❌'}")
        print(f"      相关性矩阵: {'✅' if correlations else '❌'}")
    
    print()
    return viz_exists


def run_all_tests():
    """运行所有测试"""
    print("🧪 增强版仿真功能测试")
    print("="*50)
    print()
    
    test_results = []
    
    # 运行各项测试
    try:
        test_results.append(("多时间序列", test_multi_timeseries()))
        test_results.append(("高级分析", test_advanced_analysis()))
        test_results.append(("场景对比", test_scenario_comparison()))
        test_results.append(("可视化导出", test_visualization_export()))
    
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 汇总测试结果
    print("📋 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(test_results)} 项测试通过")
    
    success_rate = passed / len(test_results)
    if success_rate >= 0.8:
        print("🎉 测试整体通过！增强功能工作正常")
    elif success_rate >= 0.5:
        print("⚠️ 部分功能存在问题，建议检查")
    else:
        print("❌ 多项功能存在问题，需要调试")
    
    return success_rate >= 0.8


if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🚀 功能测试通过，可以运行完整演示:")
        print("     python run_enhanced_demo.py")
    else:
        print("\n🔧 建议先解决测试中发现的问题")
    
    sys.exit(0 if success else 1)