// 简化的服务器启动脚本 - 避免路径问题
const express = require('express');
const path = require('path');
const fs = require('fs-extra');

const app = express();
const PORT = 3508;

// 基础中间件
app.use(express.static('public'));
app.use(express.json());

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// 高级分析API - 返回我们创建的测试数据
app.get('/api/advanced/latest', async (req, res) => {
    try {
        // 读取我们创建的测试报告
        const reportPath = path.join(__dirname, 'simulation_results', 'final_report.json');
        if (await fs.pathExists(reportPath)) {
            const report = await fs.readJson(reportPath);
            res.json(report);
        } else {
            res.status(404).json({ error: 'No report found' });
        }
    } catch (err) {
        console.error('Error loading report:', err);
        res.status(500).json({ error: 'Failed to load report' });
    }
});

// 列出报告
app.get('/api/advanced/list', async (req, res) => {
    try {
        const reports = [];
        const reportPath = path.join(__dirname, 'simulation_results', 'final_report.json');
        if (await fs.pathExists(reportPath)) {
            const stat = await fs.stat(reportPath);
            reports.push({
                id: 'simulation_results',
                dir: path.dirname(reportPath),
                mtime: stat.mtime
            });
        }
        res.json(reports);
    } catch (err) {
        res.status(500).json({ error: 'Failed to list reports' });
    }
});

// 主页
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 测试页面
app.get('/test', (req, res) => {
    res.sendFile(path.join(__dirname, 'test_advanced_analysis.html'));
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 Qiaopi Simulation Server running on http://localhost:${PORT}`);
    console.log(`🧪 Test Page: http://localhost:${PORT}/test`);
    console.log(`📊 Main Dashboard: http://localhost:${PORT}`);
    console.log(`🔧 API Health: http://localhost:${PORT}/api/health`);
    console.log(`📈 Advanced Analysis: http://localhost:${PORT}/api/advanced/latest`);
});