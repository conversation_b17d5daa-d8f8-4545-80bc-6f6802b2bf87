#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Export and Report Generation Tools
数据导出和报告生成工具

Export simulation data to various formats for analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
import csv
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional
import base64
from io import BytesIO

# For PDF generation
try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("Warning: reportlab not installed. PDF export will be limited.")

class DataExporter:
    """
    Export simulation data to various formats
    将仿真数据导出为各种格式
    """
    
    def __init__(self, output_dir: str = "exports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Configure matplotlib for better Chinese support
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # Seaborn style
        sns.set_theme(style="whitegrid")
    
    def export_to_excel(self, simulation_data: Dict[str, Any], filename: str = None) -> str:
        """Export comprehensive data to Excel with multiple sheets"""
        if filename is None:
            filename = f"qiaopi_simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        filepath = self.output_dir / filename
        
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            workbook = writer.book
            
            # Define formats
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BD',
                'border': 1
            })
            
            # 1. Summary Sheet
            summary_df = pd.DataFrame([simulation_data.get('summary', {})])
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # 2. Time Series Data
            if 'time_series' in simulation_data:
                ts_df = pd.DataFrame(simulation_data['time_series'])
                ts_df.to_excel(writer, sheet_name='Time Series', index=False)
                
                # Add chart
                worksheet = writer.sheets['Time Series']
                chart = workbook.add_chart({'type': 'line'})
                
                for i, col in enumerate(ts_df.columns[1:], 1):  # Skip first column (time)
                    chart.add_series({
                        'name': col,
                        'categories': ['Time Series', 1, 0, len(ts_df), 0],
                        'values': ['Time Series', 1, i, len(ts_df), i],
                    })
                
                chart.set_title({'name': 'Simulation Time Series'})
                chart.set_x_axis({'name': 'Time'})
                chart.set_y_axis({'name': 'Value'})
                worksheet.insert_chart('H2', chart, {'x_scale': 2, 'y_scale': 1.5})
            
            # 3. Agent Statistics
            if 'agent_stats' in simulation_data:
                agent_df = pd.DataFrame(simulation_data['agent_stats'])
                agent_df.to_excel(writer, sheet_name='Agent Statistics', index=False)
            
            # 4. Network Analysis
            if 'network_metrics' in simulation_data:
                network_df = pd.DataFrame([simulation_data['network_metrics']])
                network_df.to_excel(writer, sheet_name='Network Analysis', index=False)
            
            # 5. Remittance Data
            if 'remittances' in simulation_data:
                remit_df = pd.DataFrame(simulation_data['remittances'])
                remit_df.to_excel(writer, sheet_name='Remittances', index=False)
                
                # Add pivot table
                if len(remit_df) > 0:
                    pivot_df = remit_df.pivot_table(
                        values='amount',
                        index='origin',
                        columns='destination',
                        aggfunc='sum',
                        fill_value=0
                    )
                    pivot_df.to_excel(writer, sheet_name='Remittance Matrix')
        
        print(f"✅ Excel report exported to: {filepath}")
        return str(filepath)
    
    def export_to_csv_bundle(self, simulation_data: Dict[str, Any], prefix: str = None) -> List[str]:
        """Export data to multiple CSV files"""
        if prefix is None:
            prefix = f"qiaopi_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        exported_files = []
        
        # Export different data categories
        categories = {
            'time_series': simulation_data.get('time_series', {}),
            'agents': simulation_data.get('agents', []),
            'remittances': simulation_data.get('remittances', []),
            'network': simulation_data.get('network_edges', []),
            'events': simulation_data.get('events', [])
        }
        
        for category, data in categories.items():
            if data:
                filename = f"{prefix}_{category}.csv"
                filepath = self.output_dir / filename
                
                if isinstance(data, list):
                    df = pd.DataFrame(data)
                elif isinstance(data, dict):
                    df = pd.DataFrame([data])
                else:
                    continue
                
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                exported_files.append(str(filepath))
                print(f"✅ Exported {category} to: {filename}")
        
        return exported_files
    
    def generate_statistical_plots(self, simulation_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate statistical plots and save as images"""
        plots = {}
        
        # Create figure directory
        plots_dir = self.output_dir / "plots"
        plots_dir.mkdir(exist_ok=True)
        
        # 1. Time Series Plot
        if 'time_series' in simulation_data:
            fig, axes = plt.subplots(2, 1, figsize=(12, 8))
            
            ts_data = simulation_data['time_series']
            time = ts_data.get('time', [])
            
            # Economic indicators
            axes[0].plot(time, ts_data.get('migrant_savings', []), label='Migrant Savings', linewidth=2)
            axes[0].plot(time, ts_data.get('family_cash', []), label='Family Cash', linewidth=2)
            axes[0].set_xlabel('Time')
            axes[0].set_ylabel('Amount')
            axes[0].set_title('Economic Indicators Over Time')
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)
            
            # Success rate
            axes[1].plot(time, ts_data.get('success_rate', []), color='green', linewidth=2)
            axes[1].fill_between(time, ts_data.get('success_rate', []), alpha=0.3, color='green')
            axes[1].set_xlabel('Time')
            axes[1].set_ylabel('Success Rate')
            axes[1].set_title('Remittance Success Rate')
            axes[1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            filepath = plots_dir / 'time_series.png'
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            plots['time_series'] = str(filepath)
        
        # 2. Distribution Plots
        if 'agents' in simulation_data:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            agents_df = pd.DataFrame(simulation_data['agents'])
            
            # Location distribution
            if 'location' in agents_df.columns:
                location_counts = agents_df['location'].value_counts()
                axes[0, 0].bar(location_counts.index, location_counts.values)
                axes[0, 0].set_xlabel('Location')
                axes[0, 0].set_ylabel('Count')
                axes[0, 0].set_title('Agent Distribution by Location')
                axes[0, 0].tick_params(axis='x', rotation=45)
            
            # Income distribution
            if 'income' in agents_df.columns:
                axes[0, 1].hist(agents_df['income'], bins=30, edgecolor='black', alpha=0.7)
                axes[0, 1].set_xlabel('Income Level')
                axes[0, 1].set_ylabel('Frequency')
                axes[0, 1].set_title('Income Distribution')
            
            # Savings distribution
            if 'savings' in agents_df.columns:
                axes[1, 0].hist(agents_df['savings'], bins=30, edgecolor='black', alpha=0.7, color='green')
                axes[1, 0].set_xlabel('Savings')
                axes[1, 0].set_ylabel('Frequency')
                axes[1, 0].set_title('Savings Distribution')
            
            # Occupation distribution
            if 'occupation' in agents_df.columns:
                occupation_counts = agents_df['occupation'].value_counts()
                axes[1, 1].pie(occupation_counts.values, labels=occupation_counts.index, autopct='%1.1f%%')
                axes[1, 1].set_title('Occupation Distribution')
            
            plt.tight_layout()
            filepath = plots_dir / 'distributions.png'
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            plots['distributions'] = str(filepath)
        
        # 3. Network Visualization (simplified)
        if 'network_metrics' in simulation_data:
            fig, ax = plt.subplots(figsize=(10, 8))
            
            metrics = simulation_data['network_metrics']
            metric_names = list(metrics.keys())
            metric_values = list(metrics.values())
            
            # Radar chart
            angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
            metric_values += metric_values[:1]
            angles += angles[:1]
            
            ax = plt.subplot(111, polar=True)
            ax.plot(angles, metric_values, 'o-', linewidth=2)
            ax.fill(angles, metric_values, alpha=0.25)
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metric_names)
            ax.set_ylim(0, max(metric_values) * 1.1)
            ax.set_title('Network Metrics', y=1.08)
            
            filepath = plots_dir / 'network_metrics.png'
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            plots['network_metrics'] = str(filepath)
        
        # 4. Heatmap
        if 'remittance_matrix' in simulation_data:
            fig, ax = plt.subplots(figsize=(10, 8))
            
            matrix = simulation_data['remittance_matrix']
            sns.heatmap(matrix, annot=True, fmt='.0f', cmap='YlOrRd', 
                       cbar_kws={'label': 'Total Remittance Amount'})
            ax.set_title('Remittance Flow Matrix')
            ax.set_xlabel('Destination')
            ax.set_ylabel('Origin')
            
            plt.tight_layout()
            filepath = plots_dir / 'remittance_heatmap.png'
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            plots['remittance_heatmap'] = str(filepath)
        
        return plots
    
    def generate_pdf_report(self, simulation_data: Dict[str, Any], 
                           filename: str = None) -> Optional[str]:
        """Generate comprehensive PDF report"""
        if not REPORTLAB_AVAILABLE:
            print("❌ PDF generation requires reportlab. Install with: pip install reportlab")
            return None
        
        if filename is None:
            filename = f"qiaopi_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        filepath = self.output_dir / filename
        
        # Create PDF
        doc = SimpleDocTemplate(str(filepath), pagesize=letter)
        story = []
        styles = getSampleStyleSheet()
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            textColor=colors.HexColor('#2c3e50'),
            spaceAfter=30,
            alignment=1  # Center
        )
        
        story.append(Paragraph("Qiaopi Network Simulation Report", title_style))
        story.append(Paragraph("侨批网络仿真报告", title_style))
        story.append(Spacer(1, 0.5*inch))
        
        # Metadata
        metadata = simulation_data.get('metadata', {})
        story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
        story.append(Paragraph(f"Simulation Period: {metadata.get('start_year', 'N/A')} - {metadata.get('end_year', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 0.3*inch))
        
        # Executive Summary
        story.append(Paragraph("Executive Summary", styles['Heading2']))
        summary = simulation_data.get('summary', {})
        summary_text = f"""
        Total Agents: {summary.get('total_agents', 0):,}<br/>
        Total Remittances: {summary.get('total_remittances', 0):,}<br/>
        Success Rate: {summary.get('success_rate', 0):.1%}<br/>
        Total Amount Transferred: ${summary.get('total_amount', 0):,.2f}
        """
        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(PageBreak())
        
        # Generate plots
        plots = self.generate_statistical_plots(simulation_data)
        
        # Add plots to PDF
        for plot_name, plot_path in plots.items():
            if Path(plot_path).exists():
                story.append(Paragraph(plot_name.replace('_', ' ').title(), styles['Heading2']))
                img = Image(plot_path, width=6*inch, height=4*inch)
                story.append(img)
                story.append(Spacer(1, 0.3*inch))
        
        # Key Findings
        story.append(PageBreak())
        story.append(Paragraph("Key Findings", styles['Heading2']))
        
        findings = simulation_data.get('key_findings', [])
        if findings:
            for finding in findings:
                story.append(Paragraph(f"• {finding}", styles['Normal']))
        else:
            story.append(Paragraph("• Analysis pending", styles['Normal']))
        
        # Build PDF
        doc.build(story)
        print(f"✅ PDF report generated: {filepath}")
        
        return str(filepath)
    
    def export_for_tableau(self, simulation_data: Dict[str, Any]) -> str:
        """Export data in Tableau-friendly format"""
        # Flatten nested data structures
        flat_data = []
        
        time_series = simulation_data.get('time_series', {})
        if time_series and 'time' in time_series:
            for i in range(len(time_series['time'])):
                row = {'time': time_series['time'][i]}
                for key, values in time_series.items():
                    if key != 'time' and i < len(values):
                        row[key] = values[i]
                flat_data.append(row)
        
        # Save as Tableau-ready CSV
        filename = f"tableau_qiaopi_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        filepath = self.output_dir / filename
        
        df = pd.DataFrame(flat_data)
        df.to_csv(filepath, index=False)
        
        print(f"✅ Tableau-ready data exported to: {filepath}")
        return str(filepath)
    
    def create_json_archive(self, simulation_data: Dict[str, Any], 
                           compress: bool = True) -> str:
        """Create JSON archive of complete simulation data"""
        filename = f"qiaopi_archive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        if compress:
            filename += '.gz'
        
        filepath = self.output_dir / filename
        
        # Add metadata
        archive_data = {
            'version': '1.0',
            'created': datetime.now().isoformat(),
            'data': simulation_data
        }
        
        if compress:
            import gzip
            with gzip.open(filepath, 'wt', encoding='utf-8') as f:
                json.dump(archive_data, f, indent=2, default=str)
        else:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ JSON archive created: {filepath}")
        return str(filepath)


class ReportGenerator:
    """
    Generate analytical reports from simulation data
    从仿真数据生成分析报告
    """
    
    def __init__(self):
        self.exporter = DataExporter()
    
    def generate_full_report(self, simulation_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate all report formats"""
        reports = {}
        
        print("\n" + "="*60)
        print("Generating Comprehensive Reports")
        print("="*60 + "\n")
        
        # 1. Excel Report
        try:
            reports['excel'] = self.exporter.export_to_excel(simulation_data)
        except Exception as e:
            print(f"❌ Excel export failed: {e}")
        
        # 2. CSV Bundle
        try:
            csv_files = self.exporter.export_to_csv_bundle(simulation_data)
            reports['csv_files'] = csv_files
        except Exception as e:
            print(f"❌ CSV export failed: {e}")
        
        # 3. PDF Report
        try:
            reports['pdf'] = self.exporter.generate_pdf_report(simulation_data)
        except Exception as e:
            print(f"❌ PDF generation failed: {e}")
        
        # 4. Tableau Export
        try:
            reports['tableau'] = self.exporter.export_for_tableau(simulation_data)
        except Exception as e:
            print(f"❌ Tableau export failed: {e}")
        
        # 5. JSON Archive
        try:
            reports['json_archive'] = self.exporter.create_json_archive(simulation_data)
        except Exception as e:
            print(f"❌ JSON archive failed: {e}")
        
        print("\n" + "="*60)
        print("Report Generation Complete")
        print("="*60)
        
        return reports
    
    def generate_quick_summary(self, simulation_data: Dict[str, Any]) -> str:
        """Generate a quick text summary"""
        summary = []
        summary.append("="*60)
        summary.append("QIAOPI NETWORK SIMULATION - QUICK SUMMARY")
        summary.append("="*60)
        
        # Basic info
        metadata = simulation_data.get('metadata', {})
        summary.append(f"\nSimulation Period: {metadata.get('start_year')} - {metadata.get('end_year')}")
        summary.append(f"Total Steps: {metadata.get('total_steps', 0)}")
        
        # Agent statistics
        agent_stats = simulation_data.get('summary', {})
        summary.append(f"\nAgent Statistics:")
        summary.append(f"  - Total Migrants: {agent_stats.get('num_migrants', 0)}")
        summary.append(f"  - Total Families: {agent_stats.get('num_families', 0)}")
        summary.append(f"  - Total Institutions: {agent_stats.get('num_institutions', 0)}")
        
        # Economic metrics
        summary.append(f"\nEconomic Metrics:")
        summary.append(f"  - Total Remittances: {agent_stats.get('total_remittances', 0):,}")
        summary.append(f"  - Success Rate: {agent_stats.get('success_rate', 0):.1%}")
        summary.append(f"  - Total Amount: ${agent_stats.get('total_amount', 0):,.2f}")
        summary.append(f"  - Average Remittance: ${agent_stats.get('avg_remittance', 0):,.2f}")
        
        # Network metrics
        network = simulation_data.get('network_metrics', {})
        summary.append(f"\nNetwork Analysis:")
        summary.append(f"  - Network Density: {network.get('density', 0):.3f}")
        summary.append(f"  - Avg Clustering: {network.get('clustering', 0):.3f}")
        summary.append(f"  - Communities: {network.get('num_communities', 0)}")
        
        summary.append("\n" + "="*60)
        
        summary_text = "\n".join(summary)
        
        # Save to file
        filepath = self.exporter.output_dir / f"summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(summary_text)
        
        print(summary_text)
        print(f"\nSummary saved to: {filepath}")
        
        return summary_text


# Example usage function
def demo_export():
    """Demonstrate export functionality with sample data"""
    
    # Generate sample simulation data
    sample_data = {
        'metadata': {
            'start_year': 1920,
            'end_year': 1930,
            'total_steps': 120
        },
        'summary': {
            'total_agents': 150,
            'num_migrants': 50,
            'num_families': 50,
            'num_institutions': 10,
            'total_remittances': 1234,
            'success_rate': 0.85,
            'total_amount': 125000.50,
            'avg_remittance': 101.25
        },
        'time_series': {
            'time': list(range(120)),
            'migrant_savings': [1000 + i * 10 + np.random.normal(0, 50) for i in range(120)],
            'family_cash': [500 + i * 5 + np.random.normal(0, 25) for i in range(120)],
            'success_rate': [0.8 + np.random.normal(0, 0.05) for i in range(120)]
        },
        'agents': [
            {
                'id': f'agent_{i}',
                'type': 'migrant' if i < 50 else 'family',
                'location': np.random.choice(['Singapore', 'Malaysia', 'Thailand']),
                'income': np.random.uniform(10, 100),
                'savings': np.random.uniform(100, 1000),
                'occupation': np.random.choice(['Merchant', 'Laborer', 'Craftsman'])
            }
            for i in range(100)
        ],
        'network_metrics': {
            'density': 0.15,
            'clustering': 0.35,
            'num_communities': 5,
            'avg_degree': 3.2
        },
        'key_findings': [
            'Remittance success rate increased over time',
            'Singapore emerged as the primary hub',
            'Family networks showed strong clustering',
            'Economic conditions improved steadily'
        ]
    }
    
    # Generate reports
    generator = ReportGenerator()
    reports = generator.generate_full_report(sample_data)
    
    print("\n📁 Generated Reports:")
    for report_type, path in reports.items():
        if isinstance(path, list):
            print(f"  - {report_type}: {len(path)} files")
        else:
            print(f"  - {report_type}: {path}")


if __name__ == "__main__":
    print("Data Export Tools Demo")
    print("="*60)
    demo_export()