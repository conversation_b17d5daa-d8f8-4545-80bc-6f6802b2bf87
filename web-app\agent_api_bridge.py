#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web API与Python智能体系统的桥接模块
Bridge module between Web API and Python agent system
"""

import json
import sys
import os
from typing import Dict, List, Optional, Any
from dataclasses import asdict
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from realistic_agents import (
    RealisticMigrantAgent, RealisticFamilyAgent, RealisticInstitutionAgent,
    RealQiaoxiangRegion, RealOccupation, AILearningConfig, MemoryConfig, SocialConfig
)
from realistic_simulation_engine import RealisticSimulationEngine

class AgentFactory:
    """智能体工厂类，用于从Web配置创建Python智能体对象"""
    
    def __init__(self):
        self.agent_types = {
            'migrant': RealisticMigrantAgent,
            'family': RealisticFamilyAgent,
            'institution': RealisticInstitutionAgent,
            'government': RealisticInstitutionAgent,  # 使用机构智能体作为政府代理
            'merchant': RealisticMigrantAgent  # 使用移民智能体作为商人代理
        }
    
    def create_agent_from_web_config(self, web_config: Dict) -> Any:
        """从Web配置创建智能体"""
        try:
            agent_type = web_config.get('type', 'migrant')
            AgentClass = self.agent_types.get(agent_type, RealisticMigrantAgent)
            
            # 基础参数
            agent_id = web_config.get('id', f"agent_{datetime.now().timestamp()}")
            name = web_config.get('name', f'{agent_type}_agent')
            
            # 地理配置
            geographic = web_config.get('geographic', {})
            origin_region = self._convert_origin_region(geographic.get('originRegion', 'guangdong'))
            
            # 经济配置
            economic = web_config.get('economic', {})
            wealth_level = self._convert_wealth_level(economic.get('wealthLevel', 'middle'))
            
            # AI学习配置
            learning = web_config.get('learning', {})
            ai_config = AILearningConfig(
                enabled=learning.get('enabled', True),
                learning_rate=learning.get('learningRate', 0.01),
                exploration_rate=learning.get('explorationRate', 0.1),
                memory_size=learning.get('memorySize', 1000),
                batch_size=learning.get('batchSize', 32),
                update_frequency=learning.get('updateFrequency', 10)
            )
            
            # 记忆配置
            memory = web_config.get('memory', {})
            memory_config = MemoryConfig(
                capacity=memory.get('capacity', 100),
                episodic_enabled=memory.get('episodic', True),
                semantic_enabled=memory.get('semantic', True),
                procedural_enabled=memory.get('procedural', False),
                forgetting_rate=learning.get('forgettingFactor', 0.01)
            )
            
            # 社交配置
            social = web_config.get('social', {})
            social_config = SocialConfig(
                trust_threshold=social.get('trustLevel', 0.7),
                max_connections=social.get('networkSize', 20),
                influence_radius=social.get('influenceRadius', 10),
                reputation_weight=0.3,
                clan_loyalty=social.get('clanConnections', True)
            )
            
            # 行为配置
            behavior = web_config.get('behavior', {})
            
            # 创建智能体
            if agent_type == 'migrant':
                agent = RealisticMigrantAgent(
                    agent_id=agent_id,
                    name=name,
                    qiaoxiang_region=origin_region,
                    destination_location=self._convert_destination_region(geographic.get('destinationRegion', 'southeast_asia')),
                    wealth_level=wealth_level,
                    ai_config=ai_config,
                    memory_config=memory_config,
                    social_config=social_config
                )
            elif agent_type == 'family':
                agent = RealisticFamilyAgent(
                    agent_id=agent_id,
                    name=name,
                    qiaoxiang_region=origin_region,
                    wealth_level=wealth_level,
                    ai_config=ai_config,
                    memory_config=memory_config,
                    social_config=social_config
                )
            elif agent_type == 'institution':
                agent = RealisticInstitutionAgent(
                    agent_id=agent_id,
                    name=name,
                    region=origin_region,
                    institution_type=self._convert_institution_type(web_config.get('institutionType', 'qianzhuang')),
                    ai_config=ai_config,
                    memory_config=memory_config,
                    social_config=social_config
                )
            else:
                # 默认创建移民智能体
                agent = RealisticMigrantAgent(
                    agent_id=agent_id,
                    name=name,
                    qiaoxiang_region=origin_region,
                    destination_location='东南亚',
                    wealth_level=wealth_level,
                    ai_config=ai_config,
                    memory_config=memory_config,
                    social_config=social_config
                )
            
            # 应用行为参数
            if hasattr(agent, 'risk_tolerance'):
                agent.risk_tolerance = behavior.get('riskTolerance', 0.5)
            if hasattr(agent, 'cooperativeness'):
                agent.cooperativeness = behavior.get('cooperativeness', 0.7)
            if hasattr(agent, 'adaptability'):
                agent.adaptability = behavior.get('adaptability', 0.6)
            
            return agent
            
        except Exception as e:
            print(f"Error creating agent from web config: {e}")
            raise
    
    def _convert_origin_region(self, web_region: str) -> RealQiaoxiangRegion:
        """转换Web地区到Python枚举"""
        region_map = {
            'guangdong': RealQiaoxiangRegion.CHAOZHOU,
            'fujian': RealQiaoxiangRegion.CHAOZHOU,  # 简化映射
            'hainan': RealQiaoxiangRegion.CHAOZHOU,
            'guangxi': RealQiaoxiangRegion.CHAOZHOU,
            'other': RealQiaoxiangRegion.OTHER
        }
        return region_map.get(web_region, RealQiaoxiangRegion.CHAOZHOU)
    
    def _convert_destination_region(self, web_destination: str) -> str:
        """转换Web目的地到字符串"""
        destination_map = {
            'southeast_asia': '东南亚',
            'americas': '美洲',
            'australia': '澳洲',
            'europe': '欧洲',
            'other': '其他'
        }
        return destination_map.get(web_destination, '东南亚')
    
    def _convert_wealth_level(self, web_wealth: str) -> str:
        """转换Web财富等级"""
        wealth_map = {
            'poor': '贫困',
            'modest': '温饱',
            'middle': '中等',
            'wealthy': '富裕',
            'very_wealthy': '非常富裕'
        }
        return wealth_map.get(web_wealth, '中等')
    
    def _convert_institution_type(self, web_type: str) -> str:
        """转换机构类型"""
        type_map = {
            'qianzhuang': '钱庄',
            'piju': '批局',
            'bank': '银行',
            'post_office': '邮局'
        }
        return type_map.get(web_type, '钱庄')

class SimulationRunner:
    """仿真运行器，用于运行包含Web配置智能体的仿真"""
    
    def __init__(self):
        self.agent_factory = AgentFactory()
        self.engine = None
    
    def run_simulation_with_agents(self, simulation_config: Dict) -> Dict:
        """使用Web配置的智能体运行仿真"""
        try:
            # 解析配置
            agents_config = simulation_config.get('agents', [])
            scenario_config = simulation_config.get('scenario', {})
            
            # 创建智能体
            agents = []
            for agent_config in agents_config:
                agent = self.agent_factory.create_agent_from_web_config(agent_config)
                agents.append(agent)
            
            # 创建仿真引擎
            self.engine = RealisticSimulationEngine()
            
            # 设置仿真参数
            start_year = scenario_config.get('startYear', 1920)
            end_year = scenario_config.get('endYear', 1940)
            steps = scenario_config.get('steps', (end_year - start_year) * 12)  # 月步长
            
            # 运行仿真
            print(f"开始运行仿真: {start_year}-{end_year}, {len(agents)}个智能体")
            
            results = self.engine.run_advanced_simulation(
                agents=agents,
                start_year=start_year,
                total_steps=steps,
                output_dir=f"web_simulation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            return {
                'status': 'completed',
                'simulation_id': f"web_sim_{datetime.now().timestamp()}",
                'agents_count': len(agents),
                'steps_completed': steps,
                'results_summary': results,
                'output_directory': self.engine.output_dir if self.engine else None,
                'completed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"仿真运行错误: {e}")
            return {
                'status': 'error',
                'error_message': str(e),
                'completed_at': datetime.now().isoformat()
            }
    
    def test_agent_configuration(self, agent_config: Dict) -> Dict:
        """测试智能体配置"""
        try:
            agent = self.agent_factory.create_agent_from_web_config(agent_config)
            
            # 简单测试
            test_results = {
                'agent_id': agent.id,
                'agent_type': agent_config.get('type'),
                'validation_status': 'passed',
                'warnings': [],
                'performance_estimate': self._estimate_performance(agent_config),
                'memory_usage': self._estimate_memory_usage(agent_config),
                'computational_complexity': self._estimate_complexity(agent_config)
            }
            
            # 检查配置有效性
            warnings = []
            
            # 检查学习率
            learning_rate = agent_config.get('learning', {}).get('learningRate', 0.01)
            if learning_rate > 0.1:
                warnings.append('学习率过高，可能导致训练不稳定')
            elif learning_rate < 0.001:
                warnings.append('学习率过低，可能导致学习缓慢')
            
            # 检查风险容忍度
            risk_tolerance = agent_config.get('behavior', {}).get('riskTolerance', 0.5)
            if risk_tolerance > 0.9:
                warnings.append('风险容忍度极高，智能体可能做出不理性决策')
            
            # 检查网络大小
            network_size = agent_config.get('social', {}).get('networkSize', 20)
            if network_size > 100:
                warnings.append('社交网络规模过大，可能影响计算性能')
            
            test_results['warnings'] = warnings
            
            return test_results
            
        except Exception as e:
            return {
                'validation_status': 'failed',
                'error_message': str(e),
                'tested_at': datetime.now().isoformat()
            }
    
    def _estimate_performance(self, config: Dict) -> Dict:
        """估算性能指标"""
        learning_config = config.get('learning', {})
        behavior_config = config.get('behavior', {})
        
        # 简化的性能估算
        learning_efficiency = 0.7
        if learning_config.get('enabled'):
            learning_rate = learning_config.get('learningRate', 0.01)
            if 0.005 <= learning_rate <= 0.05:
                learning_efficiency += 0.2
        
        decision_speed = behavior_config.get('decisionSpeed', 0.5) * 0.8 + 0.2
        
        adaptability = behavior_config.get('adaptability', 0.5)
        accuracy_estimate = (adaptability + behavior_config.get('cooperativeness', 0.7)) / 2
        
        return {
            'learning_efficiency': round(learning_efficiency, 3),
            'decision_speed': round(decision_speed, 3),
            'accuracy_estimate': round(accuracy_estimate, 3)
        }
    
    def _estimate_memory_usage(self, config: Dict) -> Dict:
        """估算内存使用"""
        memory_config = config.get('memory', {})
        learning_config = config.get('learning', {})
        
        base_memory = 10  # MB
        
        # 记忆容量影响
        capacity = memory_config.get('capacity', 100)
        memory_overhead = capacity * 0.1  # 每100容量约10MB
        
        # AI模型影响
        ai_model = learning_config.get('aiModel', 'rule_based')
        model_memory = {
            'rule_based': 5,
            'reinforcement_learning': 20,
            'deep_neural': 50,
            'hybrid': 30
        }
        
        total_memory = base_memory + memory_overhead + model_memory.get(ai_model, 10)
        
        return {
            'estimated_mb': round(total_memory, 1),
            'base_mb': base_memory,
            'memory_overhead_mb': round(memory_overhead, 1),
            'model_overhead_mb': model_memory.get(ai_model, 10)
        }
    
    def _estimate_complexity(self, config: Dict) -> Dict:
        """估算计算复杂度"""
        learning_config = config.get('learning', {})
        social_config = config.get('social', {})
        
        base_complexity = 1.0
        
        # AI模型复杂度
        ai_model = learning_config.get('aiModel', 'rule_based')
        model_complexity = {
            'rule_based': 1.0,
            'reinforcement_learning': 3.0,
            'deep_neural': 5.0,
            'hybrid': 4.0
        }
        
        # 社交网络复杂度
        network_size = social_config.get('networkSize', 20)
        network_complexity = min(network_size / 50, 2.0)  # 最大2倍复杂度
        
        total_complexity = base_complexity * model_complexity.get(ai_model, 1.0) * (1 + network_complexity)
        
        return {
            'total_score': round(total_complexity, 2),
            'model_factor': model_complexity.get(ai_model, 1.0),
            'network_factor': round(1 + network_complexity, 2),
            'performance_impact': 'low' if total_complexity < 5 else 'medium' if total_complexity < 10 else 'high'
        }

class AgentAPIBridge:
    """Web API桥接器"""
    
    def __init__(self):
        self.agent_factory = AgentFactory()
        self.simulation_runner = SimulationRunner()
        self.created_agents = {}
    
    def handle_agent_creation(self, web_request: Dict) -> Dict:
        """处理智能体创建请求"""
        try:
            # 验证请求
            if not web_request.get('type') or not web_request.get('name'):
                return {
                    'success': False,
                    'error': 'Missing required fields: type and name'
                }
            
            # 创建智能体
            agent = self.agent_factory.create_agent_from_web_config(web_request)
            
            # 保存到内存（实际应用中应该保存到数据库）
            self.created_agents[agent.id] = {
                'agent_object': agent,
                'web_config': web_request,
                'created_at': datetime.now().isoformat()
            }
            
            return {
                'success': True,
                'agent_id': agent.id,
                'agent_type': web_request['type'],
                'agent_name': web_request['name'],
                'created_at': datetime.now().isoformat(),
                'message': f'智能体 "{agent.name}" 创建成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def handle_agent_test(self, agent_id: str, test_config: Dict) -> Dict:
        """处理智能体测试请求"""
        try:
            if agent_id not in self.created_agents:
                # 尝试从Web配置重新创建
                agent_data = test_config.get('agent_config')
                if agent_data:
                    agent = self.agent_factory.create_agent_from_web_config(agent_data)
                else:
                    return {
                        'success': False,
                        'error': f'Agent {agent_id} not found'
                    }
            else:
                agent = self.created_agents[agent_id]['agent_object']
            
            # 运行测试
            test_scenario = test_config.get('scenario', 'basic')
            test_steps = test_config.get('steps', 50)
            
            test_results = self.simulation_runner.test_agent_configuration(test_config)
            
            # 添加实际智能体测试结果
            if hasattr(agent, 'ai_config') and agent.ai_config.enabled:
                test_results['ai_learning_active'] = True
                test_results['model_type'] = getattr(agent, 'ai_model_type', 'unknown')
            else:
                test_results['ai_learning_active'] = False
            
            return {
                'success': True,
                'agent_id': agent_id,
                'test_scenario': test_scenario,
                'test_steps': test_steps,
                'results': test_results,
                'tested_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def handle_simulation_run(self, simulation_request: Dict) -> Dict:
        """处理仿真运行请求"""
        try:
            agent_ids = simulation_request.get('agent_ids', [])
            scenario_config = simulation_request.get('scenario', {})
            
            # 获取智能体
            agents_config = []
            for agent_id in agent_ids:
                if agent_id in self.created_agents:
                    agents_config.append(self.created_agents[agent_id]['web_config'])
                else:
                    return {
                        'success': False,
                        'error': f'Agent {agent_id} not found'
                    }
            
            # 准备仿真配置
            full_simulation_config = {
                'agents': agents_config,
                'scenario': scenario_config,
                'simulation_name': simulation_request.get('name', f'Web仿真_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            }
            
            # 运行仿真
            results = self.simulation_runner.run_simulation_with_agents(full_simulation_config)
            
            return {
                'success': True,
                'simulation_results': results,
                'started_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }
    
    def get_agent_templates(self) -> Dict:
        """获取智能体模板"""
        return {
            'migrant': {
                'name': '海外移民',
                'description': '模拟海外华侨的汇款行为和决策过程',
                'icon': 'fas fa-user-friends',
                'color': 'primary',
                'complexity': 3,
                'default_config': {
                    'behavior': {
                        'riskTolerance': 0.6,
                        'cooperativeness': 0.8,
                        'adaptability': 0.7,
                        'decisionSpeed': 0.6
                    },
                    'learning': {
                        'enabled': True,
                        'learningRate': 0.02,
                        'explorationRate': 0.1,
                        'aiModel': 'reinforcement_learning'
                    },
                    'economic': {
                        'wealthLevel': 'middle',
                        'remittanceFrequency': 3
                    }
                }
            },
            'family': {
                'name': '家乡家庭',
                'description': '模拟接收汇款的家庭成员行为模式',
                'icon': 'fas fa-home',
                'color': 'success',
                'complexity': 2,
                'default_config': {
                    'behavior': {
                        'riskTolerance': 0.3,
                        'cooperativeness': 0.9,
                        'adaptability': 0.6,
                        'decisionSpeed': 0.5
                    },
                    'learning': {
                        'enabled': True,
                        'learningRate': 0.01,
                        'explorationRate': 0.05,
                        'aiModel': 'rule_based'
                    },
                    'economic': {
                        'wealthLevel': 'modest',
                        'remittanceFrequency': 0
                    }
                }
            },
            'institution': {
                'name': '金融机构',
                'description': '模拟银庄、批局等金融中介机构',
                'icon': 'fas fa-building',
                'color': 'warning',
                'complexity': 4,
                'default_config': {
                    'behavior': {
                        'riskTolerance': 0.4,
                        'cooperativeness': 0.6,
                        'adaptability': 0.5,
                        'decisionSpeed': 0.7
                    },
                    'learning': {
                        'enabled': True,
                        'learningRate': 0.015,
                        'explorationRate': 0.08,
                        'aiModel': 'hybrid'
                    },
                    'economic': {
                        'wealthLevel': 'wealthy',
                        'remittanceFrequency': 0
                    }
                }
            }
        }

# 单例模式的API桥接器
api_bridge = AgentAPIBridge()

def create_agent_from_web(web_config: Dict) -> Dict:
    """从Web配置创建智能体的便捷函数"""
    return api_bridge.handle_agent_creation(web_config)

def test_agent_from_web(agent_id: str, test_config: Dict) -> Dict:
    """测试Web配置智能体的便捷函数"""
    return api_bridge.handle_agent_test(agent_id, test_config)

def run_simulation_from_web(simulation_config: Dict) -> Dict:
    """运行Web配置仿真的便捷函数"""
    return api_bridge.handle_simulation_run(simulation_config)

def handle_command_line():
    """处理命令行调用"""
    if len(sys.argv) < 2:
        print("Usage: python agent_api_bridge.py <action> [<data_json>]")
        sys.exit(1)
    
    action = sys.argv[1]
    data = {}
    
    if len(sys.argv) > 2:
        try:
            data = json.loads(sys.argv[2])
        except json.JSONDecodeError as e:
            print(json.dumps({'success': False, 'error': f'Invalid JSON: {e}'}))
            sys.exit(1)
    
    result = {}
    
    try:
        if action == 'create_agent':
            result = create_agent_from_web(data)
        elif action == 'test_agent':
            agent_id = data.get('agent_id')
            test_config = data.get('test_config', {})
            result = test_agent_from_web(agent_id, test_config)
        elif action == 'run_simulation':
            result = run_simulation_from_web(data)
        elif action == 'test':
            result = {
                'success': True,
                'message': 'Python bridge is working',
                'received_data': data,
                'timestamp': datetime.now().isoformat()
            }
        elif action == 'get_templates':
            result = {
                'success': True,
                'templates': api_bridge.get_agent_templates()
            }
        else:
            result = {
                'success': False,
                'error': f'Unknown action: {action}'
            }
        
        print(json.dumps(result, ensure_ascii=False))
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__,
            'timestamp': datetime.now().isoformat()
        }
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

if __name__ == '__main__':
    handle_command_line()