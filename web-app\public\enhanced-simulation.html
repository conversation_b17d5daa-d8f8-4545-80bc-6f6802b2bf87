<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版仿真 - 侨批网络</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .hero-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        
        .feature-card {
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            height: 450px;
            max-height: 500px;
            overflow: hidden;
        }
        
        .timeseries-selector {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .comparison-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-running { background: #f39c12; }
        .status-completed { background: #27ae60; }
        .status-error { background: #e74c3c; }

        /* 图表加载状态 */
        .chart-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .chart-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 确保canvas不会超出容器 */
        #timeseriesChart {
            max-width: 100% !important;
            max-height: 350px !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-network-wired"></i> 侨批网络仿真
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-home"></i> 返回主页</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-enhanced">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">
                🚀 增强版仿真系统
            </h1>
            <p class="lead mb-4">
                多时间序列 · 高级分析 · 机器学习 · 预测建模
            </p>
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h3 mb-1">50+</div>
                                <small>时间序列数量</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h3 mb-1">8</div>
                                <small>分析模块</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h3 mb-1">6</div>
                                <small>分析维度</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h3 mb-1">100+</div>
                                <small>高级指标</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Start -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="card feature-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-play-circle text-primary me-2"></i>
                                快速开始增强仿真
                            </h5>
                            <p class="card-text text-muted mb-4">
                                配置参数并启动增强版仿真，获取多维度分析结果
                            </p>
                            
                            <!-- 配置表单 -->
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">仿真时间范围</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="startYear" value="1920" min="1900" max="1950">
                                            <small class="form-text">开始年</small>
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control" id="endYear" value="1940" min="1900" max="1950">
                                            <small class="form-text">结束年</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">智能体数量</label>
                                    <div class="row">
                                        <div class="col-4">
                                            <input type="number" class="form-control" id="numMigrants" value="300" min="50" max="1000">
                                            <small class="form-text">移民</small>
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control" id="numFamilies" value="300" min="50" max="1000">
                                            <small class="form-text">家庭</small>
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control" id="numInstitutions" value="10" min="5" max="50">
                                            <small class="form-text">机构</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 分析选项 -->
                            <div class="mt-3">
                                <label class="form-label">启用分析模块</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableMultiTimeseries" checked>
                                            <label class="form-check-label" for="enableMultiTimeseries">
                                                多时间序列分析
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableTrendAnalysis" checked>
                                            <label class="form-check-label" for="enableTrendAnalysis">
                                                趋势分析
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableClustering" checked>
                                            <label class="form-check-label" for="enableClustering">
                                                聚类分析
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableCorrelation" checked>
                                            <label class="form-check-label" for="enableCorrelation">
                                                相关性分析
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enablePrediction" checked>
                                            <label class="form-check-label" for="enablePrediction">
                                                预测分析
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableVisualization" checked>
                                            <label class="form-check-label" for="enableVisualization">
                                                可视化数据
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="btn btn-primary btn-lg mt-4" onclick="runEnhancedSimulation()">
                                <i class="fas fa-rocket me-2"></i>启动增强仿真
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card feature-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-info-circle text-info me-2"></i>
                                功能特点
                            </h5>
                            
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-chart-line text-primary me-2"></i>
                                    <strong>50+条时间序列</strong><br>
                                    <small class="text-muted">地区、货币、机构、事件等多维度</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-brain text-success me-2"></i>
                                    <strong>机器学习分析</strong><br>
                                    <small class="text-muted">聚类、预测、相关性分析</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-search text-warning me-2"></i>
                                    <strong>趋势检测</strong><br>
                                    <small class="text-muted">季节性、周期性、突变点识别</small>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-network-wired text-info me-2"></i>
                                    <strong>网络拓扑</strong><br>
                                    <small class="text-muted">中心性、社群、演化分析</small>
                                </li>
                            </ul>
                            
                            <div class="mt-4">
                                <button class="btn btn-outline-primary w-100 mb-2" onclick="showResults()">
                                    <i class="fas fa-eye me-2"></i>查看现有结果
                                </button>
                                <button class="btn btn-outline-success w-100" onclick="generateDemoResults()">
                                    <i class="fas fa-magic me-2"></i>生成演示数据
                                </button>
                                <small class="form-text text-muted mt-1">
                                    如果仿真无法运行，可以生成演示数据来测试界面功能
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 仿真状态和结果 -->
    <section class="py-5 bg-light">
        <div class="container">
            <!-- 运行状态 -->
            <div id="simulationStatus" class="row mb-4" style="display: none;">
                <div class="col-12">
                    <div class="alert alert-info d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-3" role="status">
                            <span class="visually-hidden">运行中...</span>
                        </div>
                        <div>
                            <strong>增强仿真正在运行...</strong>
                            <div id="statusMessage">正在初始化智能体和环境...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 结果展示 -->
            <div id="resultsSection" style="display: none;">
                <!-- 多时间序列选择器 -->
                <div class="timeseries-selector mb-4">
                    <h4><i class="fas fa-chart-line me-2"></i>多时间序列分析</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">选择维度</label>
                            <select class="form-select" id="dimensionSelect" onchange="loadTimeseries()">
                                <option value="overall">整体趋势</option>
                                <option value="by_region">按地区分组</option>
                                <option value="by_currency">按货币分组</option>
                                <option value="by_institution">按机构分组</option>
                                <option value="by_event">按事件分组</option>
                                <option value="by_corridor">按通道分组</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">子系列</label>
                            <select class="form-select" id="seriesSelect" onchange="updateChart()">
                                <option value="">请先选择维度</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">指标</label>
                            <select class="form-select" id="metricSelect" onchange="updateChart()">
                                <option value="flow_count">流量数量</option>
                                <option value="flow_amount">流量金额</option>
                                <option value="success_rate">成功率</option>
                                <option value="total_savings">总储蓄</option>
                                <option value="avg_income">平均收入</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 时间序列图表 -->
                <div class="chart-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 id="chartTitle">时间序列图表</h5>
                        <div class="chart-controls">
                            <label class="form-label me-2">数据点限制:</label>
                            <select class="form-select form-select-sm" id="dataPointLimit" onchange="updateChart()" style="width: auto; display: inline-block;">
                                <option value="50">最近50个点</option>
                                <option value="100" selected>最近100个点</option>
                                <option value="200">最近200个点</option>
                                <option value="0">全部数据</option>
                            </select>
                        </div>
                    </div>
                    <div style="position: relative; height: 350px;">
                        <canvas id="timeseriesChart"></canvas>
                        <div id="chartLoading" class="chart-loading" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载图表数据...</div>
                        </div>
                        <div id="chartError" class="chart-loading" style="display: none;">
                            <div class="text-danger">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <div>图表加载失败</div>
                                <button class="btn btn-sm btn-outline-primary mt-2" onclick="updateChart()">重试</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 分析结果网格 -->
                <div class="analysis-grid">
                    <!-- 趋势分析 -->
                    <div class="metric-card">
                        <h6><i class="fas fa-trending-up me-2"></i>趋势分析</h6>
                        <div id="trendResults">
                            <div class="metric-value">--</div>
                            <div class="metric-label">总体趋势方向</div>
                        </div>
                    </div>
                    
                    <!-- 聚类结果 -->
                    <div class="metric-card">
                        <h6><i class="fas fa-project-diagram me-2"></i>聚类分析</h6>
                        <div id="clusterResults">
                            <div class="metric-value">--</div>
                            <div class="metric-label">行为群体数量</div>
                        </div>
                    </div>
                    
                    <!-- 预测结果 -->
                    <div class="metric-card">
                        <h6><i class="fas fa-crystal-ball me-2"></i>预测分析</h6>
                        <div id="predictionResults">
                            <div class="metric-value">--</div>
                            <div class="metric-label">5年增长预测</div>
                        </div>
                    </div>
                    
                    <!-- 网络分析 -->
                    <div class="metric-card">
                        <h6><i class="fas fa-network-wired me-2"></i>网络分析</h6>
                        <div id="networkResults">
                            <div class="metric-value">--</div>
                            <div class="metric-label">网络密度</div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细分析表格 -->
                <div class="mt-4">
                    <div class="comparison-table">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th>分析模块</th>
                                        <th>状态</th>
                                        <th>关键指标</th>
                                        <th>结果摘要</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="analysisModulesTable">
                                    <!-- 动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Enhanced Simulation JavaScript -->
    <script>
        let currentTimeseriesData = {};
        let currentChart = null;
        let chartUpdateTimeout = null;

        // 性能监控
        const performanceMonitor = {
            maxDataPoints: 1000,
            maxMemoryUsage: 100 * 1024 * 1024, // 100MB

            checkDataSize: function(data) {
                if (Array.isArray(data) && data.length > this.maxDataPoints) {
                    console.warn(`数据点过多 (${data.length}), 建议限制在 ${this.maxDataPoints} 以内`);
                    return false;
                }
                return true;
            },

            checkMemoryUsage: function() {
                if (performance.memory) {
                    const used = performance.memory.usedJSHeapSize;
                    if (used > this.maxMemoryUsage) {
                        console.warn(`内存使用过高: ${(used / 1024 / 1024).toFixed(2)}MB`);
                        return false;
                    }
                }
                return true;
            }
        };
        
        // 运行增强仿真
        async function runEnhancedSimulation() {
            const config = {
                startYear: parseInt(document.getElementById('startYear').value),
                endYear: parseInt(document.getElementById('endYear').value),
                migrants: parseInt(document.getElementById('numMigrants').value),
                families: parseInt(document.getElementById('numFamilies').value),
                institutions: parseInt(document.getElementById('numInstitutions').value)
            };
            
            // 显示状态
            document.getElementById('simulationStatus').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            
            try {
                const response = await fetch('/api/enhanced/run', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ config })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 更新状态消息
                    let statusMsg = result.message;
                    if (result.is_real_ai_simulation) {
                        statusMsg += ' (正在运行真实AI智能体仿真)';
                    }
                    document.getElementById('statusMessage').textContent = statusMsg;
                    
                    // 定期检查结果
                    checkSimulationResults();
                } else {
                    throw new Error(result.error || '启动仿真失败');
                }
            } catch (error) {
                console.error('运行仿真失败:', error);
                alert('启动增强仿真失败: ' + error.message);
                document.getElementById('simulationStatus').style.display = 'none';
            }
        }
        
        // 检查仿真结果
        async function checkSimulationResults() {
            try {
                const response = await fetch('/api/enhanced/results');
                const data = await response.json();
                
                if (data.available && data.results) {
                    // 隐藏状态，显示结果
                    document.getElementById('simulationStatus').style.display = 'none';
                    document.getElementById('resultsSection').style.display = 'block';
                    
                    // 加载结果数据
                    await loadEnhancedResults(data.results);
                } else {
                    // 继续等待
                    setTimeout(checkSimulationResults, 5000);
                }
            } catch (error) {
                console.error('检查结果失败:', error);
                setTimeout(checkSimulationResults, 10000);  // 延长检查间隔
            }
        }
        
        // 加载增强结果
        async function loadEnhancedResults(results) {
            console.log('加载增强结果:', results);
            
            // 1. 加载多时间序列数据
            if (results.multi_timeseries || results.multi_timeseries_analysis) {
                currentTimeseriesData = results.multi_timeseries || results.multi_timeseries_analysis;
                // 初始化第一个维度
                document.getElementById('dimensionSelect').selectedIndex = 0;
                loadTimeseries();
            } else if (results.enhanced_analysis && results.enhanced_analysis.multi_timeseries_analysis) {
                currentTimeseriesData = results.enhanced_analysis.multi_timeseries_analysis;
                // 初始化第一个维度
                document.getElementById('dimensionSelect').selectedIndex = 0;
                loadTimeseries();
            }
            
            // 2. 更新分析结果指标
            if (results.enhanced_analysis) {
                updateAnalysisMetrics(results.enhanced_analysis);
            }
            
            // 3. 填充分析模块表格
            updateAnalysisModulesTable(results);
        }
        
        // 加载时间序列数据
        async function loadTimeseries() {
            const dimension = document.getElementById('dimensionSelect').value;
            const seriesSelect = document.getElementById('seriesSelect');
            
            // 清空子系列选择器
            seriesSelect.innerHTML = '<option value="">加载中...</option>';
            
            try {
                // 如果有本地数据，使用本地数据
                if (currentTimeseriesData && currentTimeseriesData[dimension]) {
                    updateSeriesSelect(currentTimeseriesData, dimension);
                    return;
                }
                
                // 否则从服务器获取
                const response = await fetch(`/api/enhanced/timeseries?dimension=${dimension}`);
                const data = await response.json();
                
                if (data) {
                    currentTimeseriesData = data;
                    updateSeriesSelect(data, dimension);
                } else {
                    throw new Error('No data received');
                }
                
            } catch (error) {
                console.error('加载时间序列失败:', error);
                seriesSelect.innerHTML = '<option value="">加载失败</option>';
            }
        }
        
        // 更新子系列选择器
        function updateSeriesSelect(data, dimension) {
            const seriesSelect = document.getElementById('seriesSelect');
            seriesSelect.innerHTML = '<option value="">选择子系列</option>';
            
            if (data[dimension]) {
                Object.keys(data[dimension]).forEach(seriesKey => {
                    const option = document.createElement('option');
                    option.value = seriesKey;
                    option.textContent = seriesKey;
                    seriesSelect.appendChild(option);
                });
                
                // 如果只有一个选项，自动选中
                if (Object.keys(data[dimension]).length === 1) {
                    seriesSelect.selectedIndex = 1;
                    updateChart();
                }
            }
        }
        
        // 更新图表 (带防抖)
        function updateChart() {
            // 清除之前的更新计时器
            if (chartUpdateTimeout) {
                clearTimeout(chartUpdateTimeout);
            }

            // 防抖：延迟300ms执行，避免频繁更新
            chartUpdateTimeout = setTimeout(function() {
                updateChartImmediate();
            }, 300);
        }

        // 立即更新图表
        function updateChartImmediate() {
            const dimension = document.getElementById('dimensionSelect').value;
            const seriesKey = document.getElementById('seriesSelect').value;
            const metric = document.getElementById('metricSelect').value;
            const dataPointLimit = parseInt(document.getElementById('dataPointLimit').value);

            console.log('更新图表:', { dimension, seriesKey, metric, dataPointLimit });

            if (!dimension || !seriesKey || !metric) {
                console.log('缺少必要参数');
                return;
            }

            // 性能检查
            if (!performanceMonitor.checkMemoryUsage()) {
                showChartError(true);
                return;
            }

            if (!currentTimeseriesData || !currentTimeseriesData[dimension] || !currentTimeseriesData[dimension][seriesKey]) {
                console.error('数据不存在:', { dimension, seriesKey, currentTimeseriesData });
                return;
            }

            let series = currentTimeseriesData[dimension][seriesKey];

            // 确保series是数组
            if (!Array.isArray(series)) {
                console.error('Series不是数组:', series);
                return;
            }

            // 限制数据点数量以防止性能问题
            if (dataPointLimit > 0 && series.length > dataPointLimit) {
                series = series.slice(-dataPointLimit); // 取最后N个数据点
                console.log(`数据点已限制为最近${dataPointLimit}个点`);
            }

            // 性能检查：验证数据大小
            if (!performanceMonitor.checkDataSize(series)) {
                console.warn('数据量过大，强制限制为最近500个点');
                series = series.slice(-500);
            }

            const labels = series.map(d => d.year || d.step || '');
            const values = series.map(d => d[metric] || 0);

            // 验证数据有效性
            if (labels.length === 0 || values.length === 0) {
                console.error('没有有效的图表数据');
                showChartError(true);
                return;
            }

            console.log('图表数据:', {
                labels: labels.length,
                values: values.length,
                limited: dataPointLimit > 0 && series.length === dataPointLimit,
                memoryOK: performanceMonitor.checkMemoryUsage()
            });
            
            // 更新图表标题
            const metricNames = {
                'flow_count': '流量数量',
                'flow_amount': '流量金额',
                'success_rate': '成功率',
                'total_savings': '总储蓄',
                'avg_income': '平均收入'
            };
            document.getElementById('chartTitle').textContent = `${seriesKey} - ${metricNames[metric] || metric}`;
            
            // 显示加载状态
            showChartLoading(true);
            hideChartError();

            // 销毁现有图表
            if (currentChart) {
                try {
                    currentChart.destroy();
                } catch (e) {
                    console.warn('销毁图表时出错:', e);
                }
                currentChart = null;
            }

            try {
                // 创建新图表
                const canvas = document.getElementById('timeseriesChart');
                if (!canvas) {
                    throw new Error('找不到图表画布元素');
                }
                const ctx = canvas.getContext('2d');
                
                currentChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: metricNames[metric] || metric,
                            data: values,
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointRadius: labels.length > 50 ? 1 : 3, // 数据点多时减小点的大小
                            pointHoverRadius: 5
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        aspectRatio: 2, // 固定宽高比
                        layout: {
                            padding: {
                                top: 10,
                                bottom: 10
                            }
                        },
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '年份'
                                },
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    maxTicksLimit: 20, // 限制X轴标签数量
                                    autoSkip: true
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: metricNames[metric] || metric
                                },
                                beginAtZero: metric === 'success_rate' ? false : true,
                                ticks: {
                                    maxTicksLimit: 10 // 限制Y轴标签数量
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 15
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                padding: 10,
                                cornerRadius: 5,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (metric === 'success_rate') {
                                            label += (context.parsed.y * 100).toFixed(2) + '%';
                                        } else {
                                            label += context.parsed.y.toFixed(2);
                                        }
                                        return label;
                                    }
                                }
                            }
                        },
                        // 性能优化配置
                        animation: {
                            duration: labels.length > 100 ? 0 : 1000 // 数据点多时禁用动画
                        },
                        elements: {
                            line: {
                                tension: labels.length > 100 ? 0 : 0.4 // 数据点多时使用直线
                            }
                        }
                    }
                });

                console.log('图表创建成功');
                showChartLoading(false);

            } catch (error) {
                console.error('创建图表失败:', error);
                showChartLoading(false);
                showChartError(true);
            }
        }

        // 显示/隐藏图表加载状态
        function showChartLoading(show) {
            const loadingEl = document.getElementById('chartLoading');
            if (loadingEl) {
                loadingEl.style.display = show ? 'block' : 'none';
            }
        }

        // 显示/隐藏图表错误状态
        function showChartError(show) {
            const errorEl = document.getElementById('chartError');
            if (errorEl) {
                errorEl.style.display = show ? 'block' : 'none';
            }
        }

        function hideChartError() {
            showChartError(false);
        }
        
        // 更新分析指标
        function updateAnalysisMetrics(enhancedAnalysis) {
            // 趋势分析结果
            const trendAnalysis = enhancedAnalysis.trend_analysis?.overall_trends;
            if (trendAnalysis) {
                let trendSummary = '';
                for (const [metric, trend] of Object.entries(trendAnalysis)) {
                    trendSummary += `${metric}: ${trend}<br>`;
                }
                document.getElementById('trendResults').innerHTML = `
                    <div class="metric-value">${Object.keys(trendAnalysis).length}</div>
                    <div class="metric-label">趋势指标</div>
                `;
            }
            
            // 聚类分析结果
            const clusterAnalysis = enhancedAnalysis.clustering_analysis?.migrant_behavior_clusters?.cluster_profiles;
            if (clusterAnalysis) {
                const clusterCount = Object.keys(clusterAnalysis).length;
                document.getElementById('clusterResults').innerHTML = `
                    <div class="metric-value">${clusterCount}</div>
                    <div class="metric-label">行为群体</div>
                `;
            }
            
            // 预测分析结果
            const predictionAnalysis = enhancedAnalysis.prediction_analysis?.['5_year_forecast'];
            if (predictionAnalysis) {
                const lastPrediction = predictionAnalysis.predicted_savings?.slice(-1)[0] || 0;
                const currentValue = predictionAnalysis.predicted_savings?.[0] || 0;
                const growthRate = currentValue > 0 ? ((lastPrediction - currentValue) / currentValue * 100) : 0;
                
                document.getElementById('predictionResults').innerHTML = `
                    <div class="metric-value">${growthRate.toFixed(1)}%</div>
                    <div class="metric-label">5年预期增长</div>
                `;
            }
            
            // 网络分析结果
            const networkAnalysis = enhancedAnalysis.network_topology_analysis;
            if (networkAnalysis && networkAnalysis.network_density !== undefined) {
                document.getElementById('networkResults').innerHTML = `
                    <div class="metric-value">${(networkAnalysis.network_density * 100).toFixed(2)}%</div>
                    <div class="metric-label">网络密度</div>
                `;
            }
        }
        
        // 更新分析模块表格
        function updateAnalysisModulesTable(results) {
            const tbody = document.getElementById('analysisModulesTable');
            tbody.innerHTML = '';
            
            const modules = [
                { name: '多时间序列分析', key: 'multi_timeseries_analysis', icon: 'chart-line' },
                { name: '趋势分析', key: 'trend_analysis', icon: 'trending-up' },
                { name: '相关性分析', key: 'correlation_analysis', icon: 'project-diagram' },
                { name: '聚类分析', key: 'clustering_analysis', icon: 'sitemap' },
                { name: '预测分析', key: 'prediction_analysis', icon: 'crystal-ball' },
                { name: '网络拓扑分析', key: 'network_topology_analysis', icon: 'network-wired' },
                { name: '事件影响分析', key: 'event_impact_analysis', icon: 'exclamation-triangle' }
            ];
            
            modules.forEach(module => {
                const hasData = results.enhanced_analysis && results.enhanced_analysis[module.key];
                const status = hasData ? 'completed' : 'error';
                const statusText = hasData ? '完成' : '无数据';
                
                let keyMetric = '--';
                let summary = '暂无数据';
                
                if (hasData) {
                    const data = results.enhanced_analysis[module.key];
                    switch (module.key) {
                        case 'multi_timeseries_analysis':
                            const dimensions = ['overall', 'by_region', 'by_currency', 'by_institution', 'by_event', 'by_corridor'];
                            let totalSeries = 0;
                            dimensions.forEach(dim => {
                                if (data[dim]) {
                                    totalSeries += Object.keys(data[dim]).length;
                                }
                            });
                            keyMetric = totalSeries + '条';
                            summary = `${dimensions.filter(d => data[d]).length}个维度的时间序列`;
                            break;
                        case 'clustering_analysis':
                            const clusters = data.migrant_behavior_clusters?.cluster_profiles || {};
                            keyMetric = Object.keys(clusters).length + '个';
                            summary = `识别出${Object.keys(clusters).length}个行为群体`;
                            break;
                        case 'prediction_analysis':
                            keyMetric = data['5_year_forecast'] ? '5年' : '--';
                            summary = data['5_year_forecast'] ? '包含5年趋势预测' : '无预测数据';
                            break;
                        default:
                            keyMetric = Object.keys(data).length + '项';
                            summary = `包含${Object.keys(data).length}个分析指标`;
                    }
                }
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <i class="fas fa-${module.icon} me-2"></i>
                        ${module.name}
                    </td>
                    <td>
                        <span class="status-indicator status-${status}"></span>
                        ${statusText}
                    </td>
                    <td>${keyMetric}</td>
                    <td>${summary}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${module.key}')" 
                                ${!hasData ? 'disabled' : ''}>
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 显示现有结果
        async function showResults() {
            try {
                const response = await fetch('/api/enhanced/results');
                const data = await response.json();
                
                if (data.available) {
                    document.getElementById('resultsSection').style.display = 'block';
                    await loadEnhancedResults(data.results);
                } else {
                    alert('暂无增强仿真结果。请先运行增强仿真或生成演示数据。');
                }
            } catch (error) {
                console.error('加载结果失败:', error);
                alert('加载结果失败: ' + error.message);
            }
        }
        
        // 生成演示结果
        async function generateDemoResults() {
            try {
                console.log('🎭 Generating demo results...');
                
                const response = await fetch('/api/enhanced/generate-demo', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('演示数据已生成！点击"查看现有结果"来查看演示数据。');
                    // 自动显示结果
                    setTimeout(showResults, 1000);
                } else {
                    throw new Error(result.error || '生成演示数据失败');
                }
            } catch (error) {
                console.error('生成演示数据失败:', error);
                alert('生成演示数据失败: ' + error.message);
            }
        }
        
        // 查看详细分析
        function viewDetails(moduleKey) {
            // 跳转到可视化页面的相应模块
            const moduleMap = {
                'multi_timeseries_analysis': 'timeseries',
                'trend_analysis': 'trend',
                'correlation_analysis': 'correlation',
                'clustering_analysis': 'clustering',
                'prediction_analysis': 'prediction',
                'network_topology_analysis': 'network',
                'event_impact_analysis': 'event'
            };
            
            const tab = moduleMap[moduleKey];
            if (tab) {
                window.location.href = `/analysis-visualizations.html#${tab}`;
            } else {
                // 如果没有对应的可视化，显示原始JSON数据
                window.open(`/api/enhanced/results`, '_blank');
            }
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动检查是否有现有结果
            showResults();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            // 清理图表
            if (currentChart) {
                try {
                    currentChart.destroy();
                } catch (e) {
                    console.warn('清理图表时出错:', e);
                }
                currentChart = null;
            }

            // 清理定时器
            if (chartUpdateTimeout) {
                clearTimeout(chartUpdateTimeout);
                chartUpdateTimeout = null;
            }

            // 清理数据
            currentTimeseriesData = {};
        });

        // 监听窗口大小变化，重新调整图表
        window.addEventListener('resize', function() {
            if (currentChart) {
                setTimeout(function() {
                    try {
                        currentChart.resize();
                    } catch (e) {
                        console.warn('调整图表大小时出错:', e);
                    }
                }, 100);
            }
        });
    </script>
</body>
</html>