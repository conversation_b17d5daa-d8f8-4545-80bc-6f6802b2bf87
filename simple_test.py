#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""简单测试主要模块的导入"""

import sys

print("Testing imports...")
errors = []

try:
    import agents
    print("✓ agents module imported")
except Exception as e:
    print(f"✗ agents module failed: {e}")
    errors.append(("agents", str(e)))

try:
    import environment
    print("✓ environment module imported")
except Exception as e:
    print(f"✗ environment module failed: {e}")
    errors.append(("environment", str(e)))

try:
    import simulation_engine
    print("✓ simulation_engine module imported")
except Exception as e:
    print(f"✗ simulation_engine module failed: {e}")
    errors.append(("simulation_engine", str(e)))

try:
    import qiaopi_interactions
    print("✓ qiaopi_interactions module imported")
except Exception as e:
    print(f"✗ qiaopi_interactions module failed: {e}")
    errors.append(("qiaopi_interactions", str(e)))

try:
    import data_integration
    print("✓ data_integration module imported")
except Exception as e:
    print(f"✗ data_integration module failed: {e}")
    errors.append(("data_integration", str(e)))

if errors:
    print(f"\nFound {len(errors)} errors:")
    for module, error in errors:
        print(f"  - {module}: {error}")
    sys.exit(1)
else:
    print("\nAll modules imported successfully!")
    
    # Try to run a basic test
    print("\nTesting basic functionality...")
    from simulation_engine import SimulationConfig, QiaopiSimulationEngine
    
    config = SimulationConfig(
        start_year=1900,
        end_year=1901,
        steps_per_year=12,
        num_migrants=5,
        num_families=5,
        num_institutions=2,
        output_directory="test_results"
    )
    
    try:
        sim = QiaopiSimulationEngine(config)
        print("✓ Simulation engine created successfully")
        
        # Run one step
        sim._run_single_step()
        print("✓ One simulation step completed")
        
        print("\n✅ All tests passed! The system is working.")
    except Exception as e:
        print(f"\n✗ Simulation test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)