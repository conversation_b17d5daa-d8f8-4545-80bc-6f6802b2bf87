# 增强版仿真修复指南
## Enhanced Simulation Fix Guide

### 问题描述 (Problem Description)

The enhanced simulation feature was encountering a 500 Internal Server Error when trying to run through the web interface. The main issues were:

1. **交互式脚本问题**: 原始的 `run_enhanced_demo.py` 需要用户交互输入
2. **路径解析问题**: 包含空格的目录路径导致命令解析失败
3. **依赖模块问题**: 某些Python模块可能缺失或未正确导入

### 解决方案 (Solutions Implemented)

#### 1. 创建API专用脚本 (Created API-specific Script)

**文件**: `run_enhanced_demo_api.py`
- 无需用户交互的命令行版本
- 支持通过参数配置仿真参数
- 自动生成所需的输出文件
- 包含错误处理和状态报告

#### 2. 简化服务器启动 (Simplified Server Startup)

**文件**: `start_enhanced_server.js`
- 独立的服务器启动脚本
- 简化的路径处理
- 改进的错误处理和日志记录
- 异步后台处理Python脚本

#### 3. 启动脚本 (Startup Scripts)

**Windows**: `start_enhanced_simulation.bat`
**Linux/Mac**: `start_enhanced_simulation.sh`
- 自动检查Node.js和Python依赖
- 自动安装npm依赖包
- 一键启动增强仿真服务器

#### 4. 测试工具 (Testing Tools)

**文件**: `test_enhanced_api.py`
- 测试增强API功能
- 验证模块导入
- 快速验证仿真功能

### 使用说明 (Usage Instructions)

#### 方法1: 使用新的启动脚本

**Windows用户**:
```bash
# 双击运行或在命令行执行
start_enhanced_simulation.bat
```

**Linux/Mac用户**:
```bash
# 在终端执行
./start_enhanced_simulation.sh
```

#### 方法2: 手动启动

1. **安装依赖**:
   ```bash
   npm install
   ```

2. **启动增强服务器**:
   ```bash
   node start_enhanced_server.js
   ```

3. **访问增强仿真页面**:
   ```
   http://localhost:3508/enhanced-simulation.html
   ```

#### 方法3: 测试API功能

```bash
# 测试增强API脚本
python test_enhanced_api.py

# 直接测试API脚本
python ../run_enhanced_demo_api.py --help
```

### 文件结构 (File Structure)

```
web-app/
├── start_enhanced_server.js          # 增强服务器启动脚本
├── start_enhanced_simulation.bat     # Windows启动脚本
├── start_enhanced_simulation.sh      # Linux/Mac启动脚本  
├── test_enhanced_api.py              # API测试脚本
├── server.js                         # 原始服务器（已更新）
└── public/
    └── enhanced-simulation.html      # 增强仿真前端页面

../
├── run_enhanced_demo_api.py          # 新的API专用脚本
├── run_enhanced_demo.py             # 原始演示脚本
├── enhanced_simulation_engine.py    # 增强仿真引擎
└── enhanced_results/                # 仿真结果输出目录
```

### API端点 (API Endpoints)

1. **运行增强仿真**:
   ```
   POST /api/enhanced/run
   Body: { "config": { "startYear": 1920, "endYear": 1940, ... } }
   ```

2. **获取仿真结果**:
   ```
   GET /api/enhanced/results
   ```

3. **获取时间序列数据**:
   ```
   GET /api/enhanced/timeseries?dimension=overall
   ```

4. **获取可视化数据**:
   ```
   GET /api/enhanced/visualization
   ```

### 故障排除 (Troubleshooting)

#### 1. Python模块导入错误
```bash
# 确保在根目录下运行，或检查Python路径
cd /path/to/qiaopi-project
python web-app/test_enhanced_api.py
```

#### 2. 端口占用
```bash
# 检查端口3508是否被占用
netstat -an | find "3508"

# 或修改端口
set PORT=3509
node start_enhanced_server.js
```

#### 3. 依赖包缺失
```bash
# 重新安装npm依赖
rm -rf node_modules
npm install

# 检查Python依赖
python -c "import numpy, pandas, matplotlib"
```

#### 4. 权限问题 (Linux/Mac)
```bash
# 给脚本执行权限
chmod +x start_enhanced_simulation.sh
chmod +x ../run_enhanced_demo_api.py
```

### 验证安装 (Installation Verification)

1. **检查服务器启动**:
   - 访问 http://localhost:3508/api/health
   - 应返回 `{"status": "healthy", ...}`

2. **检查增强仿真页面**:
   - 访问 http://localhost:3508/enhanced-simulation.html
   - 页面应正常加载，显示配置表单

3. **测试运行仿真**:
   - 在增强仿真页面点击"启动增强仿真"
   - 应显示"仿真已启动"的消息
   - 几分钟后应能获取到结果

### 功能特性 (Features)

增强版仿真包含以下分析模块:
- 🕐 **多时间序列分析**: 整体、地区、货币、机构等维度
- 📈 **趋势分析**: 趋势检测与季节性分析  
- 🔗 **相关性分析**: 变量间相关性矩阵
- 🎯 **聚类分析**: 智能体行为模式识别
- 🔮 **预测分析**: 5年期趋势预测
- 🌐 **网络拓扑分析**: 网络结构与中心性分析
- ⚡ **事件影响分析**: 历史事件冲击评估

### 支持和帮助 (Support)

如果遇到问题，请检查:
1. 控制台日志输出
2. 浏览器开发者工具中的网络请求
3. `enhanced_results/` 目录中的输出文件
4. Python脚本的错误输出

---

**更新日期**: 2025-08-29
**版本**: 1.0.0
**状态**: 已修复并测试