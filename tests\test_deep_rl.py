"""
深度强化学习模块测试
Test Deep Reinforcement Learning Module

测试AI智能体的核心功能：
- 智能体创建和初始化
- 状态编码和动作解码
- 决策制定和学习能力
- 多智能体交互
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import torch
import pytest
from typing import Dict, Any
import logging

# 导入AI模块
from ai_advanced_modules.deep_rl.rl_agents import (
    DeepRLMigrantAgent, 
    DeepRLFamilyAgent, 
    DeepRLInstitutionAgent,
    create_agent_ensemble
)
from ai_advanced_modules.deep_rl.environment import (
    QiaopiEnvironment,
    EnvironmentConfig
)
from ai_advanced_modules.deep_rl.utils import (
    AgentStateEncoder,
    ActionDecoder,
    StateEncoding
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestDeepRLAgents:
    """测试深度强化学习智能体"""
    
    def setup_method(self):
        """测试前的设置"""
        self.test_config = {
            'state_dim': 20,
            'action_dim': 10,
            'learning_algorithm': 'PPO'
        }
        
    def test_migrant_agent_creation(self):
        """测试移民智能体创建"""
        agent = DeepRLMigrantAgent("test_migrant", **self.test_config)
        
        assert agent.agent_id == "test_migrant"
        assert agent.state_dim == 20
        assert agent.action_dim == 10
        assert hasattr(agent, 'model')
        assert hasattr(agent, 'personal_traits')
        
        logger.info("✅ 移民智能体创建测试通过")
    
    def test_migrant_agent_decision_making(self):
        """测试移民智能体决策"""
        agent = DeepRLMigrantAgent("test_migrant", **self.test_config)
        
        # 创建测试状态
        test_state = {
            'income_level': 100.0,
            'savings': 500.0,
            'family_urgent_need': 0.3,
            'political_stability': 0.8,
            'social_capital': 0.6,
            'years_abroad': 5.0
        }
        
        # 测试决策
        action, value = agent.make_decision(test_state)
        
        assert isinstance(action, dict)
        assert 'type' in action
        assert isinstance(value, float)
        
        logger.info(f"✅ 智能体决策测试通过: {action['type']}")
    
    def test_family_agent_creation(self):
        """测试家庭智能体创建"""
        agent = DeepRLFamilyAgent("test_family")
        
        assert agent.agent_id == "test_family"
        assert hasattr(agent, 'family_traits')
        assert hasattr(agent, 'model')
        
        logger.info("✅ 家庭智能体创建测试通过")
    
    def test_family_agent_decision_making(self):
        """测试家庭智能体决策"""
        agent = DeepRLFamilyAgent("test_family")
        
        test_state = {
            'cash_reserves': 200.0,
            'family_size': 4,
            'urgent_need_level': 0.2,
            'monthly_expenses': 80.0,
            'education_needs': 50.0
        }
        
        action = agent.make_decision(test_state)
        
        assert isinstance(action, dict)
        assert 'type' in action
        
        logger.info(f"✅ 家庭决策测试通过: {action['type']}")
    
    def test_institution_agent_creation(self):
        """测试机构智能体创建"""
        agent = DeepRLInstitutionAgent("test_institution")
        
        assert agent.agent_id == "test_institution"
        assert hasattr(agent, 'institution_traits')
        assert hasattr(agent, 'model')
        
        logger.info("✅ 机构智能体创建测试通过")
    
    def test_institution_agent_decision_making(self):
        """测试机构智能体决策"""
        agent = DeepRLInstitutionAgent("test_institution")
        
        test_state = {
            'liquidity': 10000.0,
            'transaction_volume': 5000.0,
            'success_rate': 0.9,
            'market_share': 0.15,
            'operational_cost': 500.0
        }
        
        action = agent.make_decision(test_state)
        
        assert isinstance(action, dict)
        assert 'type' in action
        
        logger.info(f"✅ 机构决策测试通过: {action['type']}")
    
    def test_agent_ensemble_creation(self):
        """测试智能体集合创建"""
        agents = create_agent_ensemble(
            num_migrants=5,
            num_families=4,
            num_institutions=2
        )
        
        assert len(agents['migrants']) == 5
        assert len(agents['families']) == 4
        assert len(agents['institutions']) == 2
        
        logger.info("✅ 智能体集合创建测试通过")


class TestEnvironment:
    """测试环境类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.config = EnvironmentConfig(
            max_steps=100,
            num_migrants=10,
            num_families=8,
            num_institutions=3
        )
    
    def test_environment_creation(self):
        """测试环境创建"""
        env = QiaopiEnvironment(self.config)
        
        assert env.config.num_migrants == 10
        assert env.config.num_families == 8
        assert env.config.num_institutions == 3
        assert hasattr(env, 'global_state')
        assert hasattr(env, 'agent_states')
        
        logger.info("✅ 环境创建测试通过")
    
    def test_environment_reset(self):
        """测试环境重置"""
        env = QiaopiEnvironment(self.config)
        
        obs, info = env.reset()
        
        assert isinstance(obs, np.ndarray)
        assert isinstance(info, dict)
        assert env.current_step == 0
        
        logger.info("✅ 环境重置测试通过")
    
    def test_environment_step(self):
        """测试环境步进"""
        env = QiaopiEnvironment(self.config)
        obs, info = env.reset()
        
        # 创建随机动作
        actions = {
            'migrants': np.random.randint(0, 10, size=self.config.num_migrants),
            'families': np.random.randint(0, 8, size=self.config.num_families),
            'institutions': np.random.randint(0, 12, size=self.config.num_institutions)
        }
        
        obs, reward, terminated, truncated, info = env.step(actions)
        
        assert isinstance(obs, np.ndarray)
        assert isinstance(reward, float)
        assert isinstance(terminated, bool)
        assert isinstance(truncated, bool)
        assert isinstance(info, dict)
        
        logger.info("✅ 环境步进测试通过")


class TestUtilities:
    """测试工具类"""
    
    def test_state_encoder(self):
        """测试状态编码器"""
        config = StateEncoding(
            economic_features=['income_level', 'savings'],
            social_features=['social_capital'],
            environmental_features=['political_stability'],
            historical_features=['success_rate'],
            personal_features=['risk_tolerance']
        )
        
        encoder = AgentStateEncoder(config)
        
        test_state = {
            'income_level': 100.0,
            'savings': 500.0,
            'social_capital': 0.7,
            'political_stability': 0.8,
            'success_rate': 0.85,
            'risk_tolerance': 0.6
        }
        
        encoded = encoder.encode_state(test_state, "migrant")
        
        assert isinstance(encoded, np.ndarray)
        assert len(encoded) > 0
        
        logger.info("✅ 状态编码器测试通过")
    
    def test_action_decoder(self):
        """测试动作解码器"""
        decoder = ActionDecoder("migrant")
        
        # 测试所有可能的动作
        for action_id in range(decoder.get_action_space_size()):
            decoded = decoder.decode_action(action_id)
            
            assert isinstance(decoded, dict)
            assert 'type' in decoded
        
        logger.info("✅ 动作解码器测试通过")


def run_performance_test():
    """运行性能测试"""
    logger.info("🚀 开始性能测试...")
    
    # 创建大规模智能体集合
    start_time = time.time()
    
    agents = create_agent_ensemble(
        num_migrants=100,
        num_families=80,
        num_institutions=10
    )
    
    creation_time = time.time() - start_time
    logger.info(f"创建190个智能体耗时: {creation_time:.2f}秒")
    
    # 测试批量决策
    start_time = time.time()
    
    test_state = {
        'income_level': 100.0,
        'savings': 500.0,
        'family_urgent_need': 0.3
    }
    
    decisions = []
    for agent in agents['migrants'][:10]:  # 测试前10个
        action, _ = agent.make_decision(test_state)
        decisions.append(action)
    
    decision_time = time.time() - start_time
    logger.info(f"10个智能体决策耗时: {decision_time:.3f}秒")
    
    logger.info("✅ 性能测试完成")


def run_integration_test():
    """运行集成测试"""
    logger.info("🔗 开始集成测试...")
    
    # 创建环境和智能体
    config = EnvironmentConfig(
        max_steps=50,
        num_migrants=5,
        num_families=4,
        num_institutions=2
    )
    
    env = QiaopiEnvironment(config)
    agents = create_agent_ensemble(
        num_migrants=config.num_migrants,
        num_families=config.num_families,
        num_institutions=config.num_institutions
    )
    
    # 运行仿真步骤
    obs, info = env.reset()
    
    for step in range(10):
        # 模拟智能体决策（简化版）
        actions = {
            'migrants': np.random.randint(0, 10, size=config.num_migrants),
            'families': np.random.randint(0, 8, size=config.num_families),
            'institutions': np.random.randint(0, 12, size=config.num_institutions)
        }
        
        obs, reward, terminated, truncated, info = env.step(actions)
        
        if terminated or truncated:
            break
    
    logger.info(f"集成测试完成，运行了 {step+1} 步")
    logger.info("✅ 集成测试通过")


if __name__ == "__main__":
    """运行所有测试"""
    import time
    
    logger.info("🧪 开始深度强化学习模块测试")
    
    # 单元测试
    test_agents = TestDeepRLAgents()
    test_agents.setup_method()
    
    try:
        test_agents.test_migrant_agent_creation()
        test_agents.test_migrant_agent_decision_making()
        test_agents.test_family_agent_creation()
        test_agents.test_family_agent_decision_making()
        test_agents.test_institution_agent_creation()
        test_agents.test_institution_agent_decision_making()
        test_agents.test_agent_ensemble_creation()
        
        # 环境测试
        test_env = TestEnvironment()
        test_env.setup_method()
        test_env.test_environment_creation()
        test_env.test_environment_reset()
        test_env.test_environment_step()
        
        # 工具测试
        test_utils = TestUtilities()
        test_utils.test_state_encoder()
        test_utils.test_action_decoder()
        
        # 性能测试
        run_performance_test()
        
        # 集成测试
        run_integration_test()
        
        logger.info("🎉 所有测试通过！深度强化学习模块工作正常。")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
