#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 检查主要模块的错误
Test Script - Check for errors in main modules
"""

import sys
import traceback

def test_imports():
    """测试所有主要模块的导入"""
    modules_to_test = [
        ('agents', '智能体模块'),
        ('environment', '环境模块'),
        ('simulation_engine', '仿真引擎'),
        ('qiaopi_interactions', '侨批交互'),
        ('data_integration', '数据集成'),
        ('realistic_agents', '真实智能体'),
        ('realistic_scenarios', '真实场景'),
        ('realistic_simulation_engine', '真实仿真引擎'),
    ]
    
    print("=" * 60)
    print("检查模块导入...")
    print("=" * 60)
    
    errors = []
    for module_name, description in modules_to_test:
        try:
            exec(f"import {module_name}")
            print(f"✓ {description} ({module_name}) - 导入成功")
        except ImportError as e:
            print(f"✗ {description} ({module_name}) - 导入失败: {e}")
            errors.append((module_name, str(e)))
        except Exception as e:
            print(f"✗ {description} ({module_name}) - 错误: {e}")
            errors.append((module_name, str(e)))
    
    return errors

def test_basic_simulation():
    """测试基础仿真功能"""
    print("\n" + "=" * 60)
    print("测试基础仿真...")
    print("=" * 60)
    
    try:
        from simulation_engine import SimulationConfig, QiaopiSimulationEngine
        
        # 创建最小配置
        config = SimulationConfig(
            start_year=1900,
            end_year=1901,  # 只运行一年
            steps_per_year=12,
            num_migrants=10,
            num_families=10,
            num_institutions=2,
            output_directory="test_output"
        )
        
        # 创建仿真引擎
        simulation = QiaopiSimulationEngine(config)
        print("✓ 仿真引擎创建成功")
        
        # 运行一步
        simulation._run_single_step()
        print("✓ 单步运行成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 仿真测试失败: {e}")
        traceback.print_exc()
        return False

def test_agent_creation():
    """测试智能体创建"""
    print("\n" + "=" * 60)
    print("测试智能体创建...")
    print("=" * 60)
    
    try:
        from agents import MigrantAgent, FamilyAgent, InstitutionAgent
        from agents import Location, Occupation, QiaoxiangRegion, EconomicStatus
        
        # 创建移民智能体
        migrant = MigrantAgent(
            origin_qiaoxiang=QiaoxiangRegion.CHAOSHAN,
            location=Location.SINGAPORE,
            occupation=Occupation.MERCHANT
        )
        print(f"✓ 移民智能体创建成功 (ID: {migrant.agent_id[:8]}...)")
        
        # 创建家庭智能体
        family = FamilyAgent(
            location_qiaoxiang=QiaoxiangRegion.CHAOSHAN,
            economic_status=EconomicStatus.STABLE
        )
        print(f"✓ 家庭智能体创建成功 (ID: {family.agent_id[:8]}...)")
        
        # 创建机构智能体
        institution = InstitutionAgent(
            home_base=Location.SINGAPORE,
            network_reach=[Location.MALAYSIA]
        )
        print(f"✓ 机构智能体创建成功 (ID: {institution.agent_id[:8]}...)")
        
        return True
        
    except Exception as e:
        print(f"✗ 智能体创建失败: {e}")
        traceback.print_exc()
        return False

def test_environment():
    """测试环境模块"""
    print("\n" + "=" * 60)
    print("测试环境模块...")
    print("=" * 60)
    
    try:
        from environment import Environment
        from agents import Location
        
        # 创建环境
        env = Environment(start_year=1900, end_year=1950)
        print("✓ 环境创建成功")
        
        # 测试方法
        economic_factor = env.get_economic_factor(Location.SINGAPORE)
        print(f"✓ 经济因素计算成功: {economic_factor:.2f}")
        
        political_risk = env.get_political_risk(Location.SINGAPORE)
        print(f"✓ 政治风险计算成功: {political_risk:.2f}")
        
        # 更新环境
        env.update()
        print(f"✓ 环境更新成功 (当前年份: {env.get_current_year():.1f})")
        
        return True
        
    except Exception as e:
        print(f"✗ 环境测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("侨批仿真系统错误检查")
    print("=" * 60)
    
    # 测试导入
    import_errors = test_imports()
    
    # 测试智能体
    agent_success = test_agent_creation()
    
    # 测试环境
    env_success = test_environment()
    
    # 测试仿真
    sim_success = test_basic_simulation()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if import_errors:
        print(f"❌ 发现 {len(import_errors)} 个导入错误")
        for module, error in import_errors:
            print(f"   - {module}: {error}")
    else:
        print("✅ 所有模块导入成功")
    
    if agent_success:
        print("✅ 智能体创建测试通过")
    else:
        print("❌ 智能体创建测试失败")
    
    if env_success:
        print("✅ 环境模块测试通过")
    else:
        print("❌ 环境模块测试失败")
    
    if sim_success:
        print("✅ 仿真引擎测试通过")
    else:
        print("❌ 仿真引擎测试失败")
    
    total_success = not import_errors and agent_success and env_success and sim_success
    
    if total_success:
        print("\n🎉 所有测试通过！系统可以正常运行。")
    else:
        print("\n⚠️ 发现错误，请修复后重试。")
    
    return 0 if total_success else 1

if __name__ == "__main__":
    sys.exit(main())