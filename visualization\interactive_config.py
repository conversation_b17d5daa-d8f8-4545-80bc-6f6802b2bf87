#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interactive Configuration Tool for Qiaopi Simulation
侨批仿真交互式配置工具

User-friendly interface for setting up simulations
"""

import json
from pathlib import Path
from datetime import datetime
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class InteractiveConfigurator:
    """
    Interactive configuration wizard for simulation setup
    """
    
    def __init__(self):
        self.config = {
            'simulation': {},
            'visualization': {},
            'export': {}
        }
        self.presets = self._load_presets()
    
    def _load_presets(self):
        """Load preset configurations"""
        return {
            'quick_demo': {
                'name': 'Quick Demo (5 years, small scale)',
                'simulation': {
                    'start_year': 1920,
                    'end_year': 1925,
                    'steps_per_year': 12,
                    'num_migrants': 50,
                    'num_families': 50,
                    'num_institutions': 5
                },
                'visualization': {
                    'update_interval': 2000,
                    'show_all_charts': True,
                    'enable_3d': False
                }
            },
            'standard': {
                'name': 'Standard Simulation (20 years, medium scale)',
                'simulation': {
                    'start_year': 1920,
                    'end_year': 1940,
                    'steps_per_year': 12,
                    'num_migrants': 200,
                    'num_families': 200,
                    'num_institutions': 15
                },
                'visualization': {
                    'update_interval': 3000,
                    'show_all_charts': True,
                    'enable_3d': True
                }
            },
            'large_scale': {
                'name': 'Large Scale (50 years, full scale)',
                'simulation': {
                    'start_year': 1900,
                    'end_year': 1950,
                    'steps_per_year': 12,
                    'num_migrants': 1000,
                    'num_families': 800,
                    'num_institutions': 50
                },
                'visualization': {
                    'update_interval': 5000,
                    'show_all_charts': False,
                    'enable_3d': False
                }
            },
            'research': {
                'name': 'Research Mode (custom parameters)',
                'simulation': {
                    'start_year': 1920,
                    'end_year': 1930,
                    'steps_per_year': 24,
                    'num_migrants': 500,
                    'num_families': 400,
                    'num_institutions': 20
                },
                'visualization': {
                    'update_interval': 1000,
                    'show_all_charts': True,
                    'enable_3d': True,
                    'enable_export': True
                }
            }
        }
    
    def print_header(self):
        """Print welcome header"""
        print("\n" + "="*70)
        print("       QIAOPI NETWORK SIMULATION - CONFIGURATION WIZARD")
        print("       侨批网络仿真 - 配置向导")
        print("="*70 + "\n")
    
    def select_preset(self):
        """Let user select a preset configuration"""
        print("📋 Available Configuration Presets:\n")
        
        options = list(self.presets.keys())
        for i, key in enumerate(options, 1):
            preset = self.presets[key]
            print(f"  [{i}] {preset['name']}")
            sim = preset['simulation']
            print(f"      Period: {sim['start_year']}-{sim['end_year']}")
            print(f"      Agents: {sim['num_migrants']} migrants, {sim['num_families']} families")
            print()
        
        print("  [C] Custom Configuration")
        print("  [Q] Quit")
        print()
        
        while True:
            choice = input("Select an option: ").strip().upper()
            
            if choice == 'Q':
                print("\n👋 Goodbye!")
                sys.exit(0)
            elif choice == 'C':
                return None
            else:
                try:
                    idx = int(choice) - 1
                    if 0 <= idx < len(options):
                        selected = options[idx]
                        print(f"\n✅ Selected: {self.presets[selected]['name']}")
                        return self.presets[selected]
                except:
                    pass
            
            print("❌ Invalid choice. Please try again.")
    
    def custom_configuration(self):
        """Guide user through custom configuration"""
        print("\n🔧 Custom Configuration Setup\n")
        
        # Simulation parameters
        print("── Simulation Parameters ──")
        
        self.config['simulation']['start_year'] = self._get_integer(
            "Start year", default=1920, min_val=1850, max_val=1950
        )
        
        self.config['simulation']['end_year'] = self._get_integer(
            "End year", 
            default=self.config['simulation']['start_year'] + 10,
            min_val=self.config['simulation']['start_year'] + 1,
            max_val=2000
        )
        
        self.config['simulation']['steps_per_year'] = self._get_integer(
            "Steps per year (12=monthly, 52=weekly)", default=12, min_val=1, max_val=365
        )
        
        print("\n── Agent Configuration ──")
        
        self.config['simulation']['num_migrants'] = self._get_integer(
            "Number of migrants", default=100, min_val=10, max_val=5000
        )
        
        self.config['simulation']['num_families'] = self._get_integer(
            "Number of families", default=100, min_val=10, max_val=5000
        )
        
        self.config['simulation']['num_institutions'] = self._get_integer(
            "Number of institutions", default=10, min_val=1, max_val=100
        )
        
        # Visualization parameters
        print("\n── Visualization Settings ──")
        
        self.config['visualization']['update_interval'] = self._get_integer(
            "Update interval (milliseconds)", default=2000, min_val=500, max_val=10000
        )
        
        self.config['visualization']['show_all_charts'] = self._get_boolean(
            "Show all charts", default=True
        )
        
        self.config['visualization']['enable_3d'] = self._get_boolean(
            "Enable 3D visualizations", default=False
        )
        
        # Export settings
        print("\n── Export Settings ──")
        
        self.config['export']['auto_export'] = self._get_boolean(
            "Auto-export results", default=True
        )
        
        if self.config['export']['auto_export']:
            self.config['export']['formats'] = self._get_export_formats()
        
        return self.config
    
    def _get_integer(self, prompt, default=None, min_val=None, max_val=None):
        """Get integer input with validation"""
        if default is not None:
            prompt += f" [{default}]"
        prompt += ": "
        
        while True:
            value = input(prompt).strip()
            
            if not value and default is not None:
                return default
            
            try:
                num = int(value)
                if min_val is not None and num < min_val:
                    print(f"❌ Value must be at least {min_val}")
                    continue
                if max_val is not None and num > max_val:
                    print(f"❌ Value must be at most {max_val}")
                    continue
                return num
            except:
                print("❌ Please enter a valid number")
    
    def _get_boolean(self, prompt, default=True):
        """Get boolean input"""
        default_str = "Y" if default else "N"
        prompt += f" (Y/N) [{default_str}]: "
        
        while True:
            value = input(prompt).strip().upper()
            
            if not value:
                return default
            
            if value in ['Y', 'YES', '1', 'TRUE']:
                return True
            elif value in ['N', 'NO', '0', 'FALSE']:
                return False
            else:
                print("❌ Please enter Y or N")
    
    def _get_export_formats(self):
        """Get export format selection"""
        formats = {
            '1': ('Excel', 'excel'),
            '2': ('CSV', 'csv'),
            '3': ('JSON', 'json'),
            '4': ('PDF Report', 'pdf'),
            '5': ('All formats', 'all')
        }
        
        print("\nSelect export formats (comma-separated):")
        for key, (name, _) in formats.items():
            print(f"  [{key}] {name}")
        
        selected = input("Your choice [5]: ").strip() or '5'
        
        if '5' in selected or 'all' in selected.lower():
            return ['excel', 'csv', 'json', 'pdf']
        
        result = []
        for char in selected.split(','):
            char = char.strip()
            if char in formats:
                result.append(formats[char][1])
        
        return result or ['excel']
    
    def save_configuration(self, config):
        """Save configuration to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"config_{timestamp}.json"
        
        # Create configs directory
        Path("configs").mkdir(exist_ok=True)
        filepath = Path("configs") / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Configuration saved to: {filepath}")
        return filepath
    
    def generate_run_script(self, config):
        """Generate a run script with the configuration"""
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto-generated run script for Qiaopi Simulation
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulation_engine import QiaopiSimulationEngine, SimulationConfig
from visualization.simple_dashboard import SimpleDashboard

# Configuration
config = SimulationConfig(
    start_year={config['simulation']['start_year']},
    end_year={config['simulation']['end_year']},
    steps_per_year={config['simulation']['steps_per_year']},
    num_migrants={config['simulation']['num_migrants']},
    num_families={config['simulation']['num_families']},
    num_institutions={config['simulation']['num_institutions']},
    output_directory="output_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
)

# Create simulation
print("Initializing simulation...")
simulation = QiaopiSimulationEngine(config)

# Create dashboard
print("Starting dashboard...")
dashboard = SimpleDashboard()
dashboard.simulation = simulation
dashboard.demo_mode = False

# Run
dashboard.run(debug=False, port=8050)
'''
        
        filename = f"run_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"🚀 Run script generated: {filename}")
        return filename
    
    def run(self):
        """Main configuration workflow"""
        self.print_header()
        
        # Select or create configuration
        preset = self.select_preset()
        
        if preset:
            config = preset
        else:
            config = self.custom_configuration()
        
        # Review configuration
        print("\n" + "─"*50)
        print("📊 Configuration Summary:")
        print("─"*50)
        
        sim = config['simulation']
        print(f"\nSimulation:")
        print(f"  Period: {sim['start_year']} - {sim['end_year']}")
        print(f"  Duration: {sim['end_year'] - sim['start_year']} years")
        print(f"  Total steps: {(sim['end_year'] - sim['start_year']) * sim['steps_per_year']}")
        print(f"  Agents: {sim['num_migrants'] + sim['num_families'] + sim['num_institutions']} total")
        
        viz = config.get('visualization', {})
        if viz:
            print(f"\nVisualization:")
            print(f"  Update interval: {viz.get('update_interval', 2000)}ms")
            print(f"  All charts: {viz.get('show_all_charts', True)}")
            print(f"  3D enabled: {viz.get('enable_3d', False)}")
        
        print("\n" + "─"*50)
        
        # Confirm
        if self._get_boolean("\nProceed with this configuration?", default=True):
            # Save configuration
            config_file = self.save_configuration(config)
            
            # Generate run script
            run_script = self.generate_run_script(config)
            
            print("\n✅ Configuration complete!")
            print("\nTo run the simulation:")
            print(f"  python {run_script}")
            print("\nOr use the saved configuration:")
            print(f"  python run_with_config.py {config_file}")
            
            # Ask if user wants to run now
            if self._get_boolean("\nRun simulation now?", default=True):
                print("\n🚀 Starting simulation...")
                os.system(f"python {run_script}")
        else:
            print("\n❌ Configuration cancelled")


def main():
    """Main entry point"""
    configurator = InteractiveConfigurator()
    configurator.run()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Configuration cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)