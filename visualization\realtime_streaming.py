#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real-time Data Streaming Infrastructure for Qiaopi Visualization
实时数据流基础设施

Enterprise-grade streaming with WebSocket, Kafka patterns
"""

import asyncio
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from collections import deque
import logging

# WebSocket and async support
import socketio
from aiohttp import web
import aiohttp_cors

# Performance monitoring
import psutil
import gc

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StreamEventType(Enum):
    """Types of streaming events"""
    SIMULATION_UPDATE = "simulation_update"
    AGENT_ACTION = "agent_action"
    REMITTANCE = "remittance"
    NETWORK_CHANGE = "network_change"
    SYSTEM_METRIC = "system_metric"
    ALERT = "alert"


@dataclass
class StreamEvent:
    """Streaming event data structure"""
    event_type: StreamEventType
    timestamp: datetime
    data: Dict[str, Any]
    priority: int = 5  # 1-10, 1 is highest priority
    
    def to_dict(self):
        return {
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'priority': self.priority
        }


class DataStreamProcessor:
    """
    High-performance data stream processor
    高性能数据流处理器
    """
    
    def __init__(self, buffer_size: int = 10000):
        self.buffer_size = buffer_size
        self.event_buffer = deque(maxlen=buffer_size)
        self.subscribers: Dict[str, List[Callable]] = {}
        self.metrics = {
            'events_processed': 0,
            'events_dropped': 0,
            'processing_time_ms': deque(maxlen=1000),
            'buffer_utilization': 0
        }
        self.is_running = False
        self.processing_thread = None
        
        # Performance optimization
        self.batch_size = 100
        self.flush_interval = 0.1  # seconds
        
    def start(self):
        """Start the stream processor"""
        self.is_running = True
        self.processing_thread = threading.Thread(target=self._process_events, daemon=True)
        self.processing_thread.start()
        logger.info("Stream processor started")
    
    def stop(self):
        """Stop the stream processor"""
        self.is_running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
        logger.info("Stream processor stopped")
    
    def publish(self, event: StreamEvent):
        """Publish event to stream"""
        if len(self.event_buffer) >= self.buffer_size:
            self.metrics['events_dropped'] += 1
            return False
        
        self.event_buffer.append(event)
        self.metrics['buffer_utilization'] = len(self.event_buffer) / self.buffer_size
        return True
    
    def subscribe(self, event_type: StreamEventType, callback: Callable):
        """Subscribe to specific event type"""
        key = event_type.value
        if key not in self.subscribers:
            self.subscribers[key] = []
        self.subscribers[key].append(callback)
    
    def _process_events(self):
        """Process events from buffer"""
        batch = []
        last_flush = time.time()
        
        while self.is_running:
            try:
                # Collect batch
                while len(batch) < self.batch_size and self.event_buffer:
                    batch.append(self.event_buffer.popleft())
                
                # Process batch or flush on timeout
                current_time = time.time()
                if batch and (len(batch) >= self.batch_size or 
                             current_time - last_flush > self.flush_interval):
                    
                    start_time = time.perf_counter()
                    self._process_batch(batch)
                    processing_time = (time.perf_counter() - start_time) * 1000
                    
                    self.metrics['processing_time_ms'].append(processing_time)
                    self.metrics['events_processed'] += len(batch)
                    
                    batch.clear()
                    last_flush = current_time
                
                # Small sleep to prevent CPU spinning
                if not self.event_buffer:
                    time.sleep(0.01)
                    
            except Exception as e:
                logger.error(f"Error processing events: {e}")
    
    def _process_batch(self, events: List[StreamEvent]):
        """Process a batch of events"""
        # Group by event type for efficient processing
        grouped = {}
        for event in events:
            key = event.event_type.value
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(event)
        
        # Notify subscribers
        for event_type, event_list in grouped.items():
            if event_type in self.subscribers:
                for callback in self.subscribers[event_type]:
                    try:
                        callback(event_list)
                    except Exception as e:
                        logger.error(f"Subscriber callback error: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get processor metrics"""
        avg_processing_time = np.mean(self.metrics['processing_time_ms']) if self.metrics['processing_time_ms'] else 0
        return {
            'events_processed': self.metrics['events_processed'],
            'events_dropped': self.metrics['events_dropped'],
            'avg_processing_time_ms': avg_processing_time,
            'buffer_utilization': self.metrics['buffer_utilization'],
            'throughput_eps': self.metrics['events_processed'] / max(1, time.time())
        }


class WebSocketServer:
    """
    WebSocket server for real-time dashboard updates
    实时仪表板更新的WebSocket服务器
    """
    
    def __init__(self, stream_processor: DataStreamProcessor):
        self.stream_processor = stream_processor
        self.sio = socketio.AsyncServer(
            async_mode='aiohttp',
            cors_allowed_origins='*',
            logger=False,
            engineio_logger=False
        )
        self.app = web.Application()
        self.sio.attach(self.app)
        
        # Setup CORS
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*"
            )
        })
        
        # Client tracking
        self.clients = {}
        self.client_subscriptions = {}
        
        # Register handlers
        self._register_handlers()
        
        # Subscribe to stream events
        self._setup_stream_subscriptions()
    
    def _register_handlers(self):
        """Register WebSocket event handlers"""
        
        @self.sio.event
        async def connect(sid, environ):
            """Handle client connection"""
            logger.info(f"Client connected: {sid}")
            self.clients[sid] = {
                'connected_at': datetime.now(),
                'last_ping': datetime.now()
            }
            await self.sio.emit('connection_established', {'sid': sid}, room=sid)
        
        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection"""
            logger.info(f"Client disconnected: {sid}")
            if sid in self.clients:
                del self.clients[sid]
            if sid in self.client_subscriptions:
                del self.client_subscriptions[sid]
        
        @self.sio.event
        async def subscribe(sid, data):
            """Handle subscription request"""
            event_types = data.get('event_types', [])
            self.client_subscriptions[sid] = event_types
            logger.info(f"Client {sid} subscribed to: {event_types}")
            await self.sio.emit('subscription_confirmed', {'events': event_types}, room=sid)
        
        @self.sio.event
        async def ping(sid, data):
            """Handle ping for keepalive"""
            if sid in self.clients:
                self.clients[sid]['last_ping'] = datetime.now()
            await self.sio.emit('pong', {'timestamp': datetime.now().isoformat()}, room=sid)
    
    def _setup_stream_subscriptions(self):
        """Setup subscriptions to stream processor"""
        
        # Subscribe to all event types
        for event_type in StreamEventType:
            self.stream_processor.subscribe(
                event_type,
                lambda events, et=event_type: asyncio.create_task(
                    self._broadcast_events(events, et)
                )
            )
    
    async def _broadcast_events(self, events: List[StreamEvent], event_type: StreamEventType):
        """Broadcast events to subscribed clients"""
        for sid, subscriptions in self.client_subscriptions.items():
            if event_type.value in subscriptions or '*' in subscriptions:
                event_data = [e.to_dict() for e in events]
                await self.sio.emit(event_type.value, event_data, room=sid)
    
    async def run(self, host='0.0.0.0', port=8081):
        """Run the WebSocket server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()
        logger.info(f"WebSocket server running on {host}:{port}")


class SimulationDataStreamer:
    """
    Stream simulation data in real-time
    实时流式传输仿真数据
    """
    
    def __init__(self, simulation_engine, stream_processor: DataStreamProcessor):
        self.simulation = simulation_engine
        self.stream_processor = stream_processor
        self.streaming_thread = None
        self.is_streaming = False
        
        # Sampling rates (events per second)
        self.sampling_rates = {
            'agent_update': 10,
            'remittance': 5,
            'network': 1,
            'metrics': 2
        }
        
        self.last_sample_time = {}
    
    def start_streaming(self):
        """Start streaming simulation data"""
        self.is_streaming = True
        self.streaming_thread = threading.Thread(target=self._stream_loop, daemon=True)
        self.streaming_thread.start()
        logger.info("Simulation streaming started")
    
    def stop_streaming(self):
        """Stop streaming"""
        self.is_streaming = False
        if self.streaming_thread:
            self.streaming_thread.join(timeout=5)
        logger.info("Simulation streaming stopped")
    
    def _stream_loop(self):
        """Main streaming loop"""
        while self.is_streaming:
            try:
                current_time = time.time()
                
                # Stream agent updates
                if self._should_sample('agent_update', current_time):
                    self._stream_agent_updates()
                
                # Stream remittance events
                if self._should_sample('remittance', current_time):
                    self._stream_remittance_events()
                
                # Stream network changes
                if self._should_sample('network', current_time):
                    self._stream_network_updates()
                
                # Stream system metrics
                if self._should_sample('metrics', current_time):
                    self._stream_system_metrics()
                
                time.sleep(0.05)  # 50ms loop interval
                
            except Exception as e:
                logger.error(f"Streaming error: {e}")
    
    def _should_sample(self, sample_type: str, current_time: float) -> bool:
        """Check if we should sample this data type"""
        last_time = self.last_sample_time.get(sample_type, 0)
        interval = 1.0 / self.sampling_rates.get(sample_type, 1)
        
        if current_time - last_time >= interval:
            self.last_sample_time[sample_type] = current_time
            return True
        return False
    
    def _stream_agent_updates(self):
        """Stream agent state updates"""
        if not self.simulation:
            return
        
        # Sample a few agents for updates
        sample_size = min(10, len(self.simulation.migrants))
        sampled_migrants = list(self.simulation.migrants.values())[:sample_size]
        
        for migrant in sampled_migrants:
            event = StreamEvent(
                event_type=StreamEventType.AGENT_ACTION,
                timestamp=datetime.now(),
                data={
                    'agent_id': migrant.agent_id[:8],
                    'type': 'migrant',
                    'location': migrant.location.value,
                    'savings': float(migrant.savings),
                    'income': float(migrant.income_level),
                    'health': float(migrant.health_status)
                },
                priority=7
            )
            self.stream_processor.publish(event)
    
    def _stream_remittance_events(self):
        """Stream remittance transactions"""
        if not self.simulation or not self.simulation.qiaopi_protocol:
            return
        
        # Get recent remittances
        recent_qiaopi = self.simulation.qiaopi_protocol.active_qiaopi[:5]
        
        for qiaopi in recent_qiaopi:
            event = StreamEvent(
                event_type=StreamEventType.REMITTANCE,
                timestamp=datetime.now(),
                data={
                    'message_id': qiaopi.message_id[:8],
                    'amount': float(qiaopi.amount),
                    'status': qiaopi.status.value,
                    'origin': qiaopi.origin.value,
                    'destination': qiaopi.destination.value
                },
                priority=5
            )
            self.stream_processor.publish(event)
    
    def _stream_network_updates(self):
        """Stream network topology changes"""
        if not self.simulation:
            return
        
        network_stats = {
            'total_networks': len(self.simulation.kinship_networks),
            'avg_network_size': np.mean([len(agents) for agents in self.simulation.kinship_networks.values()]),
            'total_connections': sum(len(agents) for agents in self.simulation.kinship_networks.values())
        }
        
        event = StreamEvent(
            event_type=StreamEventType.NETWORK_CHANGE,
            timestamp=datetime.now(),
            data=network_stats,
            priority=8
        )
        self.stream_processor.publish(event)
    
    def _stream_system_metrics(self):
        """Stream system performance metrics"""
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=0)
        memory = psutil.virtual_memory()
        
        # Simulation metrics
        sim_metrics = {}
        if self.simulation:
            sim_metrics = {
                'simulation_step': self.simulation.current_step,
                'simulation_year': self.simulation.environment.get_current_year() if hasattr(self.simulation, 'environment') else 0,
                'total_agents': len(self.simulation.migrants) + len(self.simulation.families) if self.simulation else 0
            }
        
        event = StreamEvent(
            event_type=StreamEventType.SYSTEM_METRIC,
            timestamp=datetime.now(),
            data={
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / (1024**3),
                **sim_metrics,
                'stream_metrics': self.stream_processor.get_metrics()
            },
            priority=9
        )
        self.stream_processor.publish(event)


async def main():
    """Main function to run the streaming infrastructure"""
    
    # Create stream processor
    processor = DataStreamProcessor(buffer_size=10000)
    processor.start()
    
    # Create WebSocket server
    ws_server = WebSocketServer(processor)
    
    # Create simulation streamer (simulation would be passed here)
    # streamer = SimulationDataStreamer(simulation, processor)
    # streamer.start_streaming()
    
    # Run WebSocket server
    await ws_server.run(host='0.0.0.0', port=8081)
    
    # Keep running
    while True:
        await asyncio.sleep(1)


if __name__ == "__main__":
    asyncio.run(main())