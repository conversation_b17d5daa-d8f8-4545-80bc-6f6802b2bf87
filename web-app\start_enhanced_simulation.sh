#!/bin/bash

echo "🚀 Starting Enhanced Qiaopi Simulation Server..."
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if Python is installed
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "❌ Python is not installed or not in PATH"
    echo "Please install Python from https://python.org/"
    exit 1
fi

echo "✅ Node.js and Python found"
echo

# Install npm dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing npm dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install npm dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed"
    echo
fi

# Start the enhanced server
echo "🌐 Starting server..."
echo "You can access the enhanced simulation at: http://localhost:3508/enhanced-simulation.html"
echo

node start_enhanced_server.js