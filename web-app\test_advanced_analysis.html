<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级分析测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 注意：已移除有问题的Chart.js插件，改用原生图表类型 -->
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-chart-bar"></i> 高级分析功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> API测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p><i class="fas fa-spinner fa-spin"></i> 正在测试...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4" id="chartsContainer" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> 高级分析图表</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <h6>月度季节性</h6>
                                    <canvas id="monthlyChart" style="height: 300px;"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <h6>机构成功率</h6>
                                    <canvas id="institutionChart" style="height: 300px;"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <h6>通道流量</h6>
                                    <canvas id="corridorChart" style="height: 300px;"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <h6>货币使用占比</h6>
                                    <canvas id="currencyChart" style="height: 300px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的API类
        class SimpleAPI {
            async get(url) {
                const response = await fetch(url);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                return await response.json();
            }
        }

        // 简化的图表管理器
        class SimpleChartManager {
            createBarChart(canvasId, data, options = {}) {
                const canvas = document.getElementById(canvasId);
                if (!canvas) return null;
                
                return new Chart(canvas, {
                    type: 'bar',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        ...options
                    }
                });
            }

            createDoughnutChart(canvasId, data, options = {}) {
                const canvas = document.getElementById(canvasId);
                if (!canvas) return null;
                
                return new Chart(canvas, {
                    type: 'doughnut',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        ...options
                    }
                });
            }

            getColors(count) {
                const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'];
                return colors.slice(0, count);
            }
        }

        async function testAdvancedAnalysis() {
            const api = new SimpleAPI();
            const chartManager = new SimpleChartManager();
            const resultsDiv = document.getElementById('testResults');
            const chartsContainer = document.getElementById('chartsContainer');

            try {
                // 测试API端点
                resultsDiv.innerHTML = '<p><i class="fas fa-spinner fa-spin"></i> 正在测试API...</p>';
                
                // 测试健康检查
                await api.get('/api/health');
                resultsDiv.innerHTML += '<p><i class="fas fa-check text-success"></i> API健康检查: 通过</p>';
                
                // 测试高级分析端点
                const reports = await api.get('/api/advanced/list');
                resultsDiv.innerHTML += `<p><i class="fas fa-check text-success"></i> 找到 ${reports.length} 个报告</p>`;
                
                // 获取最新报告
                const latestReport = await api.get('/api/advanced/latest');
                if (latestReport && latestReport.advanced_analysis) {
                    resultsDiv.innerHTML += '<p><i class="fas fa-check text-success"></i> 高级分析数据: 已加载</p>';
                    
                    // 显示图表
                    chartsContainer.style.display = 'block';
                    renderAdvancedAnalysisCharts(latestReport.advanced_analysis, chartManager);
                    
                } else {
                    resultsDiv.innerHTML += '<p><i class="fas fa-exclamation-triangle text-warning"></i> 高级分析数据: 未找到</p>';
                }
                
            } catch (error) {
                console.error('测试失败:', error);
                resultsDiv.innerHTML += `<p><i class="fas fa-times text-danger"></i> 错误: ${error.message}</p>`;
            }
        }

        function renderAdvancedAnalysisCharts(advancedAnalysis, chartManager) {
            // 月度季节性
            if (advancedAnalysis.remittance_dynamics?.monthly_counts) {
                const labels = Object.keys(advancedAnalysis.remittance_dynamics.monthly_counts).map(m => `月${m}`);
                const data = Object.values(advancedAnalysis.remittance_dynamics.monthly_counts);
                
                chartManager.createBarChart('monthlyChart', {
                    labels,
                    datasets: [{
                        label: '汇款笔数',
                        data,
                        backgroundColor: '#3498db80',
                        borderColor: '#3498db'
                    }]
                });
            }

            // 机构成功率
            if (advancedAnalysis.institution_performance?.institutions) {
                const entries = Object.entries(advancedAnalysis.institution_performance.institutions);
                const labels = entries.map(([id, v]) => id);
                const data = entries.map(([_, v]) => Math.round((v.success_rate || 0) * 100));
                
                chartManager.createBarChart('institutionChart', {
                    labels,
                    datasets: [{
                        label: '成功率(%)',
                        data,
                        backgroundColor: '#2ecc7180',
                        borderColor: '#2ecc71'
                    }]
                });
            }

            // 通道流量
            if (advancedAnalysis.corridor_flows?.top_by_count) {
                const top = advancedAnalysis.corridor_flows.top_by_count;
                const labels = top.map(x => `${x.origin}→${x.destination}`);
                const data = top.map(x => x.count);
                
                chartManager.createBarChart('corridorChart', {
                    labels,
                    datasets: [{
                        label: '笔数',
                        data,
                        backgroundColor: '#f39c1280',
                        borderColor: '#f39c12'
                    }]
                });
            }

            // 货币使用占比
            if (advancedAnalysis.currency_usage) {
                const labels = Object.keys(advancedAnalysis.currency_usage);
                const data = labels.map(k => advancedAnalysis.currency_usage[k].share * 100);
                
                chartManager.createDoughnutChart('currencyChart', {
                    labels,
                    datasets: [{
                        label: '占比(%)',
                        data,
                        backgroundColor: chartManager.getColors(labels.length)
                    }]
                });
            }
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', testAdvancedAnalysis);
    </script>
</body>
</html>