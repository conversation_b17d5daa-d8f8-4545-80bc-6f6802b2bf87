#!/usr/bin/env node
/**
 * 验证高级分析功能完整性
 * Verify Advanced Analysis Functionality Completeness
 */

const fs = require('fs-extra');
const path = require('path');

console.log('🔍 验证高级分析功能完整性...\n');

const checks = [
    {
        name: '1. 服务器文件',
        files: ['server.js', 'start_server_simple.js'],
        required: true
    },
    {
        name: '2. 前端核心文件',
        files: ['public/index.html', 'public/js/api.js', 'public/js/charts.js', 'public/js/ui.js'],
        required: true
    },
    {
        name: '3. 高级分析数据',
        files: ['simulation_results/final_report.json'],
        required: true
    },
    {
        name: '4. 测试文件',
        files: ['test_advanced_analysis.html', 'verify_advanced_analysis.js'],
        required: false
    },
    {
        name: '5. 文档',
        files: ['START_ADVANCED_ANALYSIS_DEMO.md'],
        required: false
    }
];

let allPassed = true;

for (const check of checks) {
    console.log(`📋 ${check.name}:`);
    
    for (const file of check.files) {
        const exists = fs.existsSync(file);
        const status = exists ? '✅' : (check.required ? '❌' : '⚠️');
        console.log(`   ${status} ${file}`);
        
        if (!exists && check.required) {
            allPassed = false;
        }
    }
    console.log();
}

// 验证JSON数据结构
console.log('📊 验证高级分析数据结构:');
try {
    const reportPath = 'simulation_results/final_report.json';
    if (fs.existsSync(reportPath)) {
        const report = fs.readJsonSync(reportPath);
        
        const requiredSections = [
            'simulation_summary',
            'final_statistics', 
            'advanced_analysis'
        ];
        
        for (const section of requiredSections) {
            const exists = section in report;
            console.log(`   ${exists ? '✅' : '❌'} ${section}`);
            if (!exists && section === 'advanced_analysis') allPassed = false;
        }
        
        // 验证高级分析子部分
        if (report.advanced_analysis) {
            const advancedSections = [
                'inequality_metrics',
                'remittance_dynamics',
                'event_impact',
                'corridor_flows',
                'institution_performance',
                'family_mobility',
                'currency_usage',
                'network_correlations'
            ];
            
            console.log('\n   📈 高级分析子部分:');
            for (const section of advancedSections) {
                const exists = section in report.advanced_analysis;
                console.log(`      ${exists ? '✅' : '⚠️'} ${section}`);
            }
        }
    } else {
        console.log('   ❌ 报告文件不存在');
        allPassed = false;
    }
} catch (err) {
    console.log(`   ❌ JSON解析错误: ${err.message}`);
    allPassed = false;
}

console.log('\n' + '='.repeat(60));

if (allPassed) {
    console.log('🎉 所有检查通过！高级分析功能应该完全可用。\n');
    console.log('🚀 启动步骤:');
    console.log('   1. 运行: node start_server_simple.js');
    console.log('   2. 访问: http://localhost:3508');
    console.log('   3. 点击"真实数据"菜单');
    console.log('   4. 查看"高级分析"卡片区域\n');
} else {
    console.log('⚠️ 存在问题，请检查上述错误。\n');
}

console.log('🔗 有用的链接:');
console.log('   📖 完整指南: START_ADVANCED_ANALYSIS_DEMO.md');
console.log('   🧪 测试页面: http://localhost:3508/test');
console.log('   🔧 API测试: http://localhost:3508/api/advanced/latest');