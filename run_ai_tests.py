"""
侨批网络AI模块测试运行器
Qiaopi Network AI Module Test Runner

这个脚本用于快速验证所有新AI模块的功能。
运行此脚本将测试：
1. 深度强化学习模块
2. 图神经网络模块
3. 预测系统模块
4. 系统集成演示

使用方法：
python run_ai_tests.py [--quick] [--demo] [--module MODULE_NAME]
"""

import sys
import os
import argparse
import time
import logging
from typing import List, Dict, Any
import subprocess

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AITestRunner:
    """AI模块测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        
    def check_dependencies(self) -> bool:
        """检查依赖库"""
        logger.info("🔍 检查依赖库...")
        
        required_packages = [
            'torch',
            'numpy', 
            'pandas',
            'matplotlib',
            'seaborn',
            'networkx',
            'scikit-learn'
        ]
        
        optional_packages = [
            'torch_geometric',
            'stable_baselines3',
            'transformers'
        ]
        
        missing_required = []
        missing_optional = []
        
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"  ✅ {package}")
            except ImportError:
                missing_required.append(package)
                logger.error(f"  ❌ {package} (必需)")
        
        for package in optional_packages:
            try:
                __import__(package)
                logger.info(f"  ✅ {package}")
            except ImportError:
                missing_optional.append(package)
                logger.warning(f"  ⚠️ {package} (可选，某些功能可能不可用)")
        
        if missing_required:
            logger.error(f"缺少必需依赖: {missing_required}")
            logger.error("请运行: pip install " + " ".join(missing_required))
            return False
        
        if missing_optional:
            logger.warning(f"缺少可选依赖: {missing_optional}")
            logger.warning("某些高级功能可能不可用")
        
        logger.info("✅ 依赖检查完成")
        return True
    
    def run_module_test(self, module_name: str) -> Dict[str, Any]:
        """运行特定模块测试"""
        logger.info(f"🧪 测试模块: {module_name}")
        
        test_files = {
            'deep_rl': 'tests/test_deep_rl.py',
            'graph_networks': 'tests/test_graph_networks.py',
            'prediction': 'tests/test_prediction.py'  # 还未创建，将创建简单版本
        }
        
        if module_name not in test_files:
            logger.error(f"未知模块: {module_name}")
            return {'success': False, 'error': 'Unknown module'}
        
        test_file = test_files[module_name]
        
        if not os.path.exists(test_file):
            if module_name == 'prediction':
                # 创建简单的预测测试
                self._create_simple_prediction_test()
            else:
                logger.error(f"测试文件不存在: {test_file}")
                return {'success': False, 'error': 'Test file not found'}
        
        # 运行测试
        start_time = time.time()
        
        try:
            result = subprocess.run(
                [sys.executable, test_file],
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            execution_time = time.time() - start_time
            
            success = result.returncode == 0
            
            test_result = {
                'success': success,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode
            }
            
            if success:
                logger.info(f"✅ {module_name} 测试通过 ({execution_time:.2f}秒)")
            else:
                logger.error(f"❌ {module_name} 测试失败")
                if result.stderr:
                    logger.error(f"错误信息: {result.stderr[:500]}...")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {module_name} 测试超时")
            return {'success': False, 'error': 'Timeout'}
        except Exception as e:
            logger.error(f"❌ {module_name} 测试异常: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _create_simple_prediction_test(self):
        """创建简单的预测模块测试"""
        test_content = '''
"""
简单预测模块测试
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import torch
import logging

try:
    from ai_advanced_modules.prediction.time_series_models import MultiScalePredictor, PredictionConfig
    PREDICTION_MODULE_AVAILABLE = True
except ImportError as e:
    PREDICTION_MODULE_AVAILABLE = False
    print(f"预测模块不可用: {e}")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_prediction_module():
    """测试预测模块"""
    if not PREDICTION_MODULE_AVAILABLE:
        logger.warning("⚠️ 预测模块不可用，跳过测试")
        return True
    
    try:
        # 创建配置
        config = PredictionConfig(
            hidden_dim=64,
            sequence_length=30,
            prediction_horizon=6,
            feature_dim=5
        )
        
        # 创建模型
        predictor = MultiScalePredictor(config)
        logger.info("✅ 预测器创建成功")
        
        # 创建测试数据
        batch_size = 4
        x = torch.randn(batch_size, config.sequence_length, config.feature_dim)
        
        # 前向传播
        with torch.no_grad():
            predictions = predictor(x, prediction_horizon=6)
        
        if isinstance(predictions, dict):
            logger.info(f"✅ 多尺度预测成功，输出: {list(predictions.keys())}")
        else:
            logger.info(f"✅ 预测成功，输出形状: {predictions.shape}")
        
        logger.info("✅ 预测模块测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 预测模块测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_prediction_module()
    if not success:
        sys.exit(1)
'''
        
        os.makedirs('tests', exist_ok=True)
        with open('tests/test_prediction.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        logger.info("✅ 创建了简单预测测试文件")
    
    def run_integration_demo(self) -> Dict[str, Any]:
        """运行集成演示"""
        logger.info("🎬 运行集成演示...")
        
        demo_file = 'integration_examples/complete_ai_demo.py'
        
        if not os.path.exists(demo_file):
            logger.error(f"演示文件不存在: {demo_file}")
            return {'success': False, 'error': 'Demo file not found'}
        
        start_time = time.time()
        
        try:
            # 设置非交互模式环境变量
            env = os.environ.copy()
            env['MPLBACKEND'] = 'Agg'  # 使用非交互式后端
            
            result = subprocess.run(
                [sys.executable, demo_file],
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时
                env=env
            )
            
            execution_time = time.time() - start_time
            
            success = result.returncode == 0
            
            demo_result = {
                'success': success,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode
            }
            
            if success:
                logger.info(f"✅ 集成演示完成 ({execution_time:.2f}秒)")
                
                # 检查生成的文件
                generated_files = []
                for file_pattern in ['qiaopi_ai_analysis_results.png', 'qiaopi_ai_demo_results_*.json', 'qiaopi_ai_demo_report_*.md']:
                    if '*' in file_pattern:
                        import glob
                        files = glob.glob(file_pattern)
                        generated_files.extend(files)
                    elif os.path.exists(file_pattern):
                        generated_files.append(file_pattern)
                
                if generated_files:
                    logger.info(f"📁 生成文件: {generated_files}")
                
            else:
                logger.error(f"❌ 集成演示失败")
                if result.stderr:
                    logger.error(f"错误信息: {result.stderr[:500]}...")
            
            return demo_result
            
        except subprocess.TimeoutExpired:
            logger.error("❌ 集成演示超时")
            return {'success': False, 'error': 'Timeout'}
        except Exception as e:
            logger.error(f"❌ 集成演示异常: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def run_quick_tests(self) -> Dict[str, Any]:
        """运行快速测试（仅基础功能）"""
        logger.info("⚡ 运行快速测试...")
        
        quick_results = {}
        
        # 测试模块导入
        modules_to_test = [
            ('ai_advanced_modules.deep_rl.rl_agents', 'DeepRLMigrantAgent'),
            ('ai_advanced_modules.graph_networks.network_analyzer', 'NetworkAnalyzer'),
            ('ai_advanced_modules.prediction.time_series_models', 'MultiScalePredictor')
        ]
        
        for module_path, class_name in modules_to_test:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                logger.info(f"✅ {module_path}.{class_name}")
                quick_results[f"{module_path}.{class_name}"] = True
            except Exception as e:
                logger.error(f"❌ {module_path}.{class_name}: {str(e)}")
                quick_results[f"{module_path}.{class_name}"] = False
        
        # 简单功能测试
        try:
            # 测试网络分析器
            import networkx as nx
            from ai_advanced_modules.graph_networks.network_analyzer import NetworkAnalyzer
            
            analyzer = NetworkAnalyzer()
            test_graph = nx.erdos_renyi_graph(20, 0.1)
            metrics = analyzer.analyze_network(test_graph, compute_expensive=False)
            
            logger.info("✅ 网络分析器快速测试通过")
            quick_results['network_analyzer_basic'] = True
            
        except Exception as e:
            logger.error(f"❌ 网络分析器快速测试失败: {str(e)}")
            quick_results['network_analyzer_basic'] = False
        
        success_count = sum(1 for v in quick_results.values() if v)
        total_count = len(quick_results)
        
        logger.info(f"⚡ 快速测试完成: {success_count}/{total_count} 通过")
        
        return {
            'success': success_count == total_count,
            'results': quick_results,
            'success_rate': success_count / total_count if total_count > 0 else 0
        }
    
    def run_all_tests(self, modules: List[str] = None, include_demo: bool = True) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始完整测试流程")
        
        if modules is None:
            modules = ['deep_rl', 'graph_networks', 'prediction']
        
        # 检查依赖
        if not self.check_dependencies():
            return {'success': False, 'error': 'Missing dependencies'}
        
        # 运行模块测试
        for module in modules:
            self.test_results[module] = self.run_module_test(module)
        
        # 运行集成演示
        if include_demo:
            self.test_results['integration_demo'] = self.run_integration_demo()
        
        # 生成摘要报告
        self.generate_test_report()
        
        return self.test_results
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📊 生成测试报告...")
        
        total_time = time.time() - self.start_time
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        # 生成报告
        report = f"""
# 侨批网络AI模块测试报告
生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
总耗时: {total_time:.2f} 秒

## 测试摘要
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 失败测试: {total_tests - passed_tests}
- 成功率: {passed_tests/total_tests*100:.1f}%

## 详细结果
"""
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result.get('success', False) else "❌ 失败"
            exec_time = result.get('execution_time', 0)
            
            report += f"""
### {test_name}
- 状态: {status}
- 执行时间: {exec_time:.2f} 秒
"""
            
            if not result.get('success', False) and 'error' in result:
                report += f"- 错误: {result['error']}\n"
        
        report += """
## 结论
"""
        
        if passed_tests == total_tests:
            report += "🎉 所有测试通过！AI模块工作正常，可以进入下一阶段开发。"
        elif passed_tests > total_tests * 0.7:
            report += "⚠️ 大部分测试通过，需要修复少数问题后继续。"
        else:
            report += "❌ 多个测试失败，需要重点检查和修复。"
        
        # 保存报告
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = f"ai_test_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"📝 测试报告已保存到: {report_file}")
        
        # 输出控制台摘要
        logger.info("=" * 60)
        logger.info(f"🧪 测试摘要: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
        logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
        
        for test_name, result in self.test_results.items():
            status_emoji = "✅" if result.get('success', False) else "❌"
            logger.info(f"  {status_emoji} {test_name}")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='侨批网络AI模块测试运行器')
    parser.add_argument('--quick', action='store_true', help='仅运行快速测试')
    parser.add_argument('--demo', action='store_true', help='仅运行集成演示')
    parser.add_argument('--module', type=str, help='仅测试特定模块 (deep_rl, graph_networks, prediction)')
    parser.add_argument('--no-demo', action='store_true', help='跳过集成演示')
    
    args = parser.parse_args()
    
    runner = AITestRunner()
    
    try:
        if args.quick:
            # 快速测试
            result = runner.run_quick_tests()
            
        elif args.demo:
            # 仅运行演示
            if not runner.check_dependencies():
                return 1
            result = runner.run_integration_demo()
            
        elif args.module:
            # 测试特定模块
            if not runner.check_dependencies():
                return 1
            result = runner.run_module_test(args.module)
            runner.test_results[args.module] = result
            runner.generate_test_report()
            
        else:
            # 完整测试
            modules = ['deep_rl', 'graph_networks', 'prediction']
            include_demo = not args.no_demo
            result = runner.run_all_tests(modules, include_demo)
        
        # 返回适当的退出码
        if isinstance(result, dict):
            return 0 if result.get('success', False) else 1
        else:
            return 0
            
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断测试")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
