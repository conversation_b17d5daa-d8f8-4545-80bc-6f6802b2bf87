#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance Monitoring Dashboard
性能监控仪表板

Real-time system performance monitoring for the simulation
"""

import psutil
import time
import threading
from collections import deque
from datetime import datetime
import json
import os
import sys

try:
    import dash
    from dash import dcc, html, Input, Output
    import plotly.graph_objs as go
    from plotly.subplots import make_subplots
except ImportError:
    print("Error: Required packages not installed")
    print("Install with: pip install dash plotly psutil")
    sys.exit(1)

class PerformanceMonitor:
    """
    System performance monitoring dashboard
    """
    
    def __init__(self, history_size=300):  # 5 minutes of data at 1 second intervals
        self.app = dash.Dash(__name__)
        self.history_size = history_size
        
        # Data storage
        self.metrics = {
            'timestamps': deque(maxlen=history_size),
            'cpu_percent': deque(maxlen=history_size),
            'memory_percent': deque(maxlen=history_size),
            'memory_mb': deque(maxlen=history_size),
            'disk_io_read': deque(maxlen=history_size),
            'disk_io_write': deque(maxlen=history_size),
            'network_sent': deque(maxlen=history_size),
            'network_recv': deque(maxlen=history_size),
            'process_cpu': deque(maxlen=history_size),
            'process_memory': deque(maxlen=history_size),
            'thread_count': deque(maxlen=history_size),
            'simulation_fps': deque(maxlen=history_size)
        }
        
        # System info
        self.system_info = self._get_system_info()
        
        # Monitoring thread
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        
        # Performance statistics
        self.stats = {
            'cpu_max': 0,
            'memory_max': 0,
            'warnings': deque(maxlen=50),
            'start_time': datetime.now()
        }
        
        # Build dashboard
        self._build_layout()
        self._register_callbacks()
        
        # Start monitoring
        self.monitor_thread.start()
    
    def _get_system_info(self):
        """Get static system information"""
        cpu_info = {
            'cores_physical': psutil.cpu_count(logical=False),
            'cores_logical': psutil.cpu_count(logical=True),
            'cpu_freq': psutil.cpu_freq().max if psutil.cpu_freq() else 0
        }
        
        memory_info = {
            'total_gb': psutil.virtual_memory().total / (1024**3),
            'available_gb': psutil.virtual_memory().available / (1024**3)
        }
        
        try:
            # Use appropriate path for Windows or Unix
            disk_path = 'C:\\' if os.name == 'nt' else '/'
            disk_usage = psutil.disk_usage(disk_path)
            disk_info = {
                'total_gb': disk_usage.total / (1024**3),
                'free_gb': disk_usage.free / (1024**3)
            }
        except:
            disk_info = {
                'total_gb': 0,
                'free_gb': 0
            }
        
        return {
            'cpu': cpu_info,
            'memory': memory_info,
            'disk': disk_info,
            'platform': {
                'system': psutil.os.name,
                'python_version': sys.version.split()[0]
            }
        }
    
    def _build_layout(self):
        """Build the monitoring dashboard layout"""
        
        self.app.layout = html.Div([
            # Header
            html.Div([
                html.H2("🖥️ Performance Monitor", style={'display': 'inline-block'}),
                html.H3("System Performance Monitoring", 
                       style={'display': 'inline-block', 'marginLeft': '20px', 'color': 'gray'}),
                html.Div(id='live-time', style={'float': 'right', 'marginTop': '20px'})
            ], style={'marginBottom': '20px'}),
            
            # System Info Cards
            html.Div([
                html.Div([
                    html.H5("CPU"),
                    html.P(f"{self.system_info['cpu']['cores_physical']} cores ({self.system_info['cpu']['cores_logical']} threads)"),
                    html.P(f"{self.system_info['cpu']['cpu_freq']:.0f} MHz max")
                ], className='info-card', style={'display': 'inline-block', 'margin': '10px', 
                                                'padding': '15px', 'backgroundColor': '#f0f0f0',
                                                'borderRadius': '5px', 'width': '200px'}),
                
                html.Div([
                    html.H5("Memory"),
                    html.P(f"{self.system_info['memory']['total_gb']:.1f} GB total"),
                    html.P(id='memory-available')
                ], className='info-card', style={'display': 'inline-block', 'margin': '10px',
                                                'padding': '15px', 'backgroundColor': '#f0f0f0',
                                                'borderRadius': '5px', 'width': '200px'}),
                
                html.Div([
                    html.H5("Disk"),
                    html.P(f"{self.system_info['disk']['total_gb']:.1f} GB total"),
                    html.P(f"{self.system_info['disk']['free_gb']:.1f} GB free")
                ], className='info-card', style={'display': 'inline-block', 'margin': '10px',
                                                'padding': '15px', 'backgroundColor': '#f0f0f0',
                                                'borderRadius': '5px', 'width': '200px'}),
                
                html.Div([
                    html.H5("Status"),
                    html.P(id='status-text', children='Running'),
                    html.P(id='uptime-text')
                ], className='info-card', style={'display': 'inline-block', 'margin': '10px',
                                                'padding': '15px', 'backgroundColor': '#f0f0f0',
                                                'borderRadius': '5px', 'width': '200px'}),
            ]),
            
            # Alert Box
            html.Div(id='alert-box', style={'margin': '20px 0'}),
            
            # Main Charts
            html.Div([
                # CPU and Memory Chart
                dcc.Graph(id='cpu-memory-chart', style={'height': '350px'}),
                
                # IO Charts
                html.Div([
                    dcc.Graph(id='disk-io-chart', style={'display': 'inline-block', 
                                                        'width': '50%', 'height': '300px'}),
                    dcc.Graph(id='network-io-chart', style={'display': 'inline-block', 
                                                           'width': '50%', 'height': '300px'}),
                ]),
                
                # Process Details Chart
                dcc.Graph(id='process-chart', style={'height': '300px'}),
                
                # Performance Statistics
                html.Div([
                    html.H4("Performance Statistics"),
                    html.Div(id='stats-summary', style={'backgroundColor': '#f9f9f9', 
                                                       'padding': '15px', 'borderRadius': '5px'})
                ], style={'marginTop': '20px'}),
            ]),
            
            # Update interval
            dcc.Interval(id='update-interval', interval=1000, n_intervals=0),  # 1 second updates
            
            # Hidden div for storing data
            html.Div(id='metrics-store', style={'display': 'none'})
        ])
    
    def _register_callbacks(self):
        """Register dashboard callbacks"""
        
        @self.app.callback(
            [Output('live-time', 'children'),
             Output('memory-available', 'children'),
             Output('uptime-text', 'children')],
            [Input('update-interval', 'n_intervals')]
        )
        def update_info(n):
            """Update basic information"""
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            memory_available = f"{psutil.virtual_memory().available / (1024**3):.1f} GB available"
            
            uptime = datetime.now() - self.stats['start_time']
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            uptime_str = f"Uptime: {hours}h {minutes}m {seconds}s"
            
            return current_time, memory_available, uptime_str
        
        @self.app.callback(
            Output('alert-box', 'children'),
            [Input('update-interval', 'n_intervals')]
        )
        def update_alerts(n):
            """Check for performance alerts"""
            alerts = []
            
            # Check CPU
            if self.metrics['cpu_percent'] and self.metrics['cpu_percent'][-1] > 90:
                alerts.append(html.Div("⚠️ High CPU usage detected!", 
                                     style={'color': 'red', 'fontWeight': 'bold'}))
            
            # Check Memory
            if self.metrics['memory_percent'] and self.metrics['memory_percent'][-1] > 85:
                alerts.append(html.Div("⚠️ High memory usage detected!", 
                                     style={'color': 'orange', 'fontWeight': 'bold'}))
            
            return html.Div(alerts)
        
        @self.app.callback(
            Output('cpu-memory-chart', 'figure'),
            [Input('update-interval', 'n_intervals')]
        )
        def update_cpu_memory_chart(n):
            """Update CPU and memory chart"""
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=('CPU Usage (%)', 'Memory Usage (%)'),
                vertical_spacing=0.15
            )
            
            if self.metrics['timestamps']:
                # CPU trace
                fig.add_trace(
                    go.Scatter(
                        x=list(self.metrics['timestamps']),
                        y=list(self.metrics['cpu_percent']),
                        mode='lines',
                        name='CPU',
                        line=dict(color='blue', width=2),
                        fill='tozeroy'
                    ),
                    row=1, col=1
                )
                
                # Memory trace
                fig.add_trace(
                    go.Scatter(
                        x=list(self.metrics['timestamps']),
                        y=list(self.metrics['memory_percent']),
                        mode='lines',
                        name='Memory',
                        line=dict(color='green', width=2),
                        fill='tozeroy'
                    ),
                    row=2, col=1
                )
            
            fig.update_yaxes(range=[0, 100], row=1, col=1)
            fig.update_yaxes(range=[0, 100], row=2, col=1)
            fig.update_layout(height=350, showlegend=False)
            
            return fig
        
        @self.app.callback(
            Output('disk-io-chart', 'figure'),
            [Input('update-interval', 'n_intervals')]
        )
        def update_disk_io_chart(n):
            """Update disk I/O chart"""
            fig = go.Figure()
            
            if self.metrics['timestamps']:
                fig.add_trace(go.Scatter(
                    x=list(self.metrics['timestamps']),
                    y=list(self.metrics['disk_io_read']),
                    mode='lines',
                    name='Read',
                    line=dict(color='blue')
                ))
                
                fig.add_trace(go.Scatter(
                    x=list(self.metrics['timestamps']),
                    y=list(self.metrics['disk_io_write']),
                    mode='lines',
                    name='Write',
                    line=dict(color='red')
                ))
            
            fig.update_layout(
                title='Disk I/O (MB/s)',
                height=300,
                yaxis_title='MB/s'
            )
            
            return fig
        
        @self.app.callback(
            Output('network-io-chart', 'figure'),
            [Input('update-interval', 'n_intervals')]
        )
        def update_network_io_chart(n):
            """Update network I/O chart"""
            fig = go.Figure()
            
            if self.metrics['timestamps']:
                fig.add_trace(go.Scatter(
                    x=list(self.metrics['timestamps']),
                    y=list(self.metrics['network_sent']),
                    mode='lines',
                    name='Sent',
                    line=dict(color='green')
                ))
                
                fig.add_trace(go.Scatter(
                    x=list(self.metrics['timestamps']),
                    y=list(self.metrics['network_recv']),
                    mode='lines',
                    name='Received',
                    line=dict(color='purple')
                ))
            
            fig.update_layout(
                title='Network I/O (KB/s)',
                height=300,
                yaxis_title='KB/s'
            )
            
            return fig
        
        @self.app.callback(
            Output('process-chart', 'figure'),
            [Input('update-interval', 'n_intervals')]
        )
        def update_process_chart(n):
            """Update process-specific metrics"""
            fig = make_subplots(
                rows=1, cols=2,
                subplot_titles=('Process CPU & Memory', 'Thread Count'),
                specs=[[{"secondary_y": True}, {}]]
            )
            
            if self.metrics['timestamps']:
                # Process CPU
                fig.add_trace(
                    go.Scatter(
                        x=list(self.metrics['timestamps']),
                        y=list(self.metrics['process_cpu']),
                        mode='lines',
                        name='Process CPU %',
                        line=dict(color='orange')
                    ),
                    row=1, col=1,
                    secondary_y=False
                )
                
                # Process Memory
                fig.add_trace(
                    go.Scatter(
                        x=list(self.metrics['timestamps']),
                        y=list(self.metrics['process_memory']),
                        mode='lines',
                        name='Process Memory MB',
                        line=dict(color='red')
                    ),
                    row=1, col=1,
                    secondary_y=True
                )
                
                # Thread count
                fig.add_trace(
                    go.Scatter(
                        x=list(self.metrics['timestamps']),
                        y=list(self.metrics['thread_count']),
                        mode='lines+markers',
                        name='Threads',
                        line=dict(color='blue')
                    ),
                    row=1, col=2
                )
            
            fig.update_yaxes(title_text="CPU %", secondary_y=False, row=1, col=1)
            fig.update_yaxes(title_text="Memory MB", secondary_y=True, row=1, col=1)
            fig.update_layout(height=300)
            
            return fig
        
        @self.app.callback(
            Output('stats-summary', 'children'),
            [Input('update-interval', 'n_intervals')]
        )
        def update_stats_summary(n):
            """Update performance statistics summary"""
            if not self.metrics['cpu_percent']:
                return "Collecting data..."
            
            # Calculate statistics
            cpu_data = list(self.metrics['cpu_percent'])
            memory_data = list(self.metrics['memory_percent'])
            
            stats_text = [
                html.Div([
                    html.Strong("CPU: "),
                    f"Current: {cpu_data[-1]:.1f}% | ",
                    f"Average: {sum(cpu_data)/len(cpu_data):.1f}% | ",
                    f"Max: {max(cpu_data):.1f}%"
                ]),
                html.Div([
                    html.Strong("Memory: "),
                    f"Current: {memory_data[-1]:.1f}% | ",
                    f"Average: {sum(memory_data)/len(memory_data):.1f}% | ",
                    f"Max: {max(memory_data):.1f}%"
                ]),
                html.Div([
                    html.Strong("Process: "),
                    f"Threads: {self.metrics['thread_count'][-1] if self.metrics['thread_count'] else 0} | ",
                    f"Memory: {self.metrics['process_memory'][-1] if self.metrics['process_memory'] else 0:.1f} MB"
                ])
            ]
            
            return stats_text
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        # Get initial I/O counters
        disk_io_start = psutil.disk_io_counters()
        net_io_start = psutil.net_io_counters()
        
        # Current process
        current_process = psutil.Process()
        
        while self.monitoring:
            try:
                # Timestamp
                self.metrics['timestamps'].append(datetime.now())
                
                # System metrics
                self.metrics['cpu_percent'].append(psutil.cpu_percent(interval=0.1))
                self.metrics['memory_percent'].append(psutil.virtual_memory().percent)
                self.metrics['memory_mb'].append(psutil.virtual_memory().used / (1024**2))
                
                # Disk I/O
                disk_io = psutil.disk_io_counters()
                if disk_io_start:
                    read_speed = (disk_io.read_bytes - disk_io_start.read_bytes) / (1024**2)  # MB/s
                    write_speed = (disk_io.write_bytes - disk_io_start.write_bytes) / (1024**2)
                    self.metrics['disk_io_read'].append(max(0, read_speed))
                    self.metrics['disk_io_write'].append(max(0, write_speed))
                else:
                    self.metrics['disk_io_read'].append(0)
                    self.metrics['disk_io_write'].append(0)
                disk_io_start = disk_io
                
                # Network I/O
                net_io = psutil.net_io_counters()
                if net_io_start:
                    sent_speed = (net_io.bytes_sent - net_io_start.bytes_sent) / 1024  # KB/s
                    recv_speed = (net_io.bytes_recv - net_io_start.bytes_recv) / 1024
                    self.metrics['network_sent'].append(max(0, sent_speed))
                    self.metrics['network_recv'].append(max(0, recv_speed))
                else:
                    self.metrics['network_sent'].append(0)
                    self.metrics['network_recv'].append(0)
                net_io_start = net_io
                
                # Process-specific metrics
                try:
                    self.metrics['process_cpu'].append(current_process.cpu_percent())
                    self.metrics['process_memory'].append(current_process.memory_info().rss / (1024**2))
                    self.metrics['thread_count'].append(current_process.num_threads())
                except:
                    self.metrics['process_cpu'].append(0)
                    self.metrics['process_memory'].append(0)
                    self.metrics['thread_count'].append(0)
                
                # Placeholder for simulation FPS (would need actual simulation reference)
                self.metrics['simulation_fps'].append(0)
                
                # Update statistics
                if self.metrics['cpu_percent']:
                    self.stats['cpu_max'] = max(self.stats['cpu_max'], self.metrics['cpu_percent'][-1])
                if self.metrics['memory_percent']:
                    self.stats['memory_max'] = max(self.stats['memory_max'], self.metrics['memory_percent'][-1])
                
                # Sleep
                time.sleep(1)
                
            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(1)
    
    def run(self, port=8051):
        """Run the performance monitor"""
        print("="*60)
        print("Performance Monitor Starting...")
        print(f"Dashboard: http://localhost:{port}")
        print("Press Ctrl+C to stop")
        print("="*60)
        
        self.app.run(debug=False, port=port, host='0.0.0.0')
    
    def stop(self):
        """Stop monitoring"""
        self.monitoring = False


def main():
    """Main entry point"""
    monitor = PerformanceMonitor()
    
    try:
        monitor.run(port=8051)
    except KeyboardInterrupt:
        print("\nStopping performance monitor...")
        monitor.stop()


if __name__ == "__main__":
    main()