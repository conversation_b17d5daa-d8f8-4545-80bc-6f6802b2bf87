"""
图神经网络模块测试
Test Graph Neural Networks Module

测试图分析和网络建模功能：
- 图神经网络模型创建和训练
- 网络结构分析
- 社区检测和演化
- 影响力分析
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import torch
import networkx as nx
import pytest
from typing import Dict, Any
import logging

# 导入图网络模块
from ai_advanced_modules.graph_networks.graph_models import (
    QiaopiNetworkGNN,
    TemporalGraphNetwork,
    AttentionGraphNetwork,
    GraphConfig,
    create_qiaopi_gnn
)
from ai_advanced_modules.graph_networks.network_analyzer import (
    NetworkAnalyzer,
    NetworkMetrics
)

# PyTorch Geometric导入
try:
    from torch_geometric.data import Data
    from torch_geometric.utils import from_networkx, to_networkx
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False
    logging.warning("PyTorch Geometric不可用，将跳过相关测试")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestGraphModels:
    """测试图神经网络模型"""
    
    def setup_method(self):
        """测试前的设置"""
        self.config = GraphConfig(
            hidden_dim=64,
            num_layers=2,
            num_heads=4,
            node_feature_dim=10,
            edge_feature_dim=5,
            output_dim=32
        )
        
        # 创建测试图数据
        self.test_graph_data = self._create_test_graph()
    
    def _create_test_graph(self):
        """创建测试图数据"""
        if not TORCH_GEOMETRIC_AVAILABLE:
            return None
            
        # 创建简单的测试图
        num_nodes = 20
        x = torch.randn(num_nodes, self.config.node_feature_dim)
        
        # 创建边：环形连接 + 一些随机边
        edge_list = []
        for i in range(num_nodes):
            edge_list.append([i, (i + 1) % num_nodes])  # 环形连接
        
        # 添加一些随机边
        for _ in range(10):
            src = np.random.randint(0, num_nodes)
            dst = np.random.randint(0, num_nodes)
            if src != dst:
                edge_list.append([src, dst])
        
        edge_index = torch.tensor(edge_list, dtype=torch.long).t().contiguous()
        edge_attr = torch.randn(edge_index.size(1), self.config.edge_feature_dim)
        
        return Data(x=x, edge_index=edge_index, edge_attr=edge_attr)
    
    @pytest.mark.skipif(not TORCH_GEOMETRIC_AVAILABLE, reason="PyTorch Geometric不可用")
    def test_qiaopi_gnn_creation(self):
        """测试侨批网络GNN创建"""
        model = QiaopiNetworkGNN(self.config)
        
        assert hasattr(model, 'node_encoder')
        assert hasattr(model, 'edge_encoder')
        assert hasattr(model, 'gnn_layers')
        assert hasattr(model, 'task_heads')
        
        logger.info("✅ QiaopiNetworkGNN创建测试通过")
    
    @pytest.mark.skipif(not TORCH_GEOMETRIC_AVAILABLE, reason="PyTorch Geometric不可用")
    def test_qiaopi_gnn_forward_pass(self):
        """测试QiaopiNetworkGNN前向传播"""
        model = QiaopiNetworkGNN(self.config)
        
        # 测试不同任务
        tasks = ['node_classification', 'graph_classification', 'link_prediction']
        
        for task in tasks:
            try:
                output = model(self.test_graph_data, task=task)
                assert isinstance(output, torch.Tensor)
                logger.info(f"✅ {task}任务前向传播测试通过，输出形状: {output.shape}")
            except Exception as e:
                logger.warning(f"⚠️ {task}任务测试跳过: {str(e)}")
    
    @pytest.mark.skipif(not TORCH_GEOMETRIC_AVAILABLE, reason="PyTorch Geometric不可用") 
    def test_node_embeddings(self):
        """测试节点嵌入提取"""
        model = QiaopiNetworkGNN(self.config)
        
        embeddings = model.get_node_embeddings(self.test_graph_data)
        
        assert embeddings.shape[0] == self.test_graph_data.x.shape[0]  # 节点数量匹配
        assert embeddings.shape[1] == self.config.hidden_dim  # 嵌入维度匹配
        
        logger.info(f"✅ 节点嵌入测试通过，形状: {embeddings.shape}")
    
    @pytest.mark.skipif(not TORCH_GEOMETRIC_AVAILABLE, reason="PyTorch Geometric不可用")
    def test_temporal_graph_network(self):
        """测试时序图神经网络"""
        temporal_model = TemporalGraphNetwork(self.config, sequence_length=5)
        
        # 创建图序列
        graph_sequence = [self.test_graph_data for _ in range(5)]
        
        try:
            prediction, attention_weights = temporal_model(graph_sequence)
            
            assert isinstance(prediction, torch.Tensor)
            assert isinstance(attention_weights, torch.Tensor)
            
            logger.info("✅ 时序图网络测试通过")
        except Exception as e:
            logger.warning(f"⚠️ 时序图网络测试跳过: {str(e)}")
    
    @pytest.mark.skipif(not TORCH_GEOMETRIC_AVAILABLE, reason="PyTorch Geometric不可用")
    def test_attention_graph_network(self):
        """测试注意力图神经网络"""
        attention_model = AttentionGraphNetwork(self.config)
        
        try:
            output = attention_model(self.test_graph_data, return_attention=True)
            
            if isinstance(output, tuple):
                predictions, attention_weights = output
                assert isinstance(predictions, torch.Tensor)
                assert isinstance(attention_weights, list)
                logger.info("✅ 注意力图网络（含权重）测试通过")
            else:
                assert isinstance(output, torch.Tensor)
                logger.info("✅ 注意力图网络测试通过")
        except Exception as e:
            logger.warning(f"⚠️ 注意力图网络测试跳过: {str(e)}")
    
    def test_model_factory(self):
        """测试模型工厂函数"""
        model_types = ['basic', 'temporal', 'hierarchical', 'attention']
        
        for model_type in model_types:
            try:
                model = create_qiaopi_gnn(model_type, **self.config.__dict__)
                assert hasattr(model, 'forward')
                logger.info(f"✅ {model_type}模型创建测试通过")
            except Exception as e:
                logger.warning(f"⚠️ {model_type}模型测试跳过: {str(e)}")


class TestNetworkAnalyzer:
    """测试网络分析器"""
    
    def setup_method(self):
        """测试前的设置"""
        self.analyzer = NetworkAnalyzer()
        
        # 创建测试网络
        self.test_graphs = {
            'small_world': nx.watts_strogatz_graph(30, 4, 0.3),
            'scale_free': nx.barabasi_albert_graph(30, 2),
            'random': nx.erdos_renyi_graph(30, 0.1),
            'complete': nx.complete_graph(10)
        }
    
    def test_basic_network_analysis(self):
        """测试基础网络分析"""
        for graph_name, graph in self.test_graphs.items():
            metrics = self.analyzer.analyze_network(graph, compute_expensive=False)
            
            assert isinstance(metrics, NetworkMetrics)
            assert metrics.num_nodes > 0
            assert metrics.num_edges >= 0
            assert 0 <= metrics.density <= 1
            
            logger.info(f"✅ {graph_name}网络基础分析测试通过")
    
    def test_node_importance_analysis(self):
        """测试节点重要性分析"""
        graph = self.test_graphs['scale_free']
        
        importance = self.analyzer.analyze_node_importance(graph, top_k=5)
        
        assert 'degree' in importance
        assert 'pagerank' in importance
        assert len(importance['degree']) <= 5
        
        logger.info("✅ 节点重要性分析测试通过")
    
    def test_degree_distribution_analysis(self):
        """测试度分布分析"""
        graph = self.test_graphs['scale_free']
        
        degree_dist = self.analyzer.analyze_degree_distribution(graph)
        
        assert 'degree_sequence' in degree_dist
        assert 'degree_counts' in degree_dist
        assert 'statistics' in degree_dist
        assert 'power_law_fit' in degree_dist
        
        logger.info("✅ 度分布分析测试通过")
    
    def test_network_robustness(self):
        """测试网络鲁棒性分析"""
        graph = self.test_graphs['small_world']
        
        robustness = self.analyzer.analyze_network_robustness(
            graph, 
            attack_strategies=['random', 'degree']
        )
        
        assert 'random' in robustness
        assert 'degree' in robustness
        
        for strategy, result in robustness.items():
            assert 'connectivity_loss' in result
            assert 'nodes_to_fragment' in result
        
        logger.info("✅ 网络鲁棒性分析测试通过")
    
    def test_network_comparison(self):
        """测试网络比较"""
        graphs = [self.test_graphs['small_world'], self.test_graphs['scale_free']]
        labels = ['Small World', 'Scale Free']
        
        comparison_df = self.analyzer.compare_networks(graphs, labels)
        
        assert len(comparison_df) == 2
        assert 'network_label' in comparison_df.columns
        assert 'num_nodes' in comparison_df.columns
        
        logger.info("✅ 网络比较分析测试通过")
    
    def test_temporal_evolution_analysis(self):
        """测试时序演化分析"""
        # 创建网络演化序列
        graph_sequence = []
        for i in range(5):
            # 逐渐增加节点数模拟网络增长
            G = nx.barabasi_albert_graph(10 + i*5, 2)
            graph_sequence.append(G)
        
        evolution_results = self.analyzer.analyze_temporal_evolution(graph_sequence)
        
        assert 'evolution_metrics' in evolution_results
        assert 'trends' in evolution_results
        assert 'summary' in evolution_results
        
        logger.info("✅ 时序演化分析测试通过")
    
    def test_anomaly_detection(self):
        """测试异常检测"""
        # 创建正常网络
        normal_graph = self.test_graphs['small_world']
        
        # 创建参考网络集合
        reference_graphs = [
            nx.watts_strogatz_graph(30, 4, 0.3) for _ in range(5)
        ]
        
        anomalies = self.analyzer.detect_anomalies(normal_graph, reference_graphs)
        
        assert 'structural_anomalies' in anomalies
        assert 'statistical_anomalies' in anomalies
        assert 'node_anomalies' in anomalies
        
        logger.info("✅ 异常检测测试通过")
    
    def test_network_report_generation(self):
        """测试网络分析报告生成"""
        graph = self.test_graphs['scale_free']
        
        report = self.analyzer.generate_network_report(graph)
        
        assert 'basic_metrics' in report
        assert 'node_importance' in report
        assert 'degree_distribution' in report
        assert 'robustness_analysis' in report
        assert 'summary' in report
        
        logger.info("✅ 网络分析报告生成测试通过")


def run_performance_benchmark():
    """运行性能基准测试"""
    logger.info("🚀 开始图网络模块性能测试...")
    
    import time
    
    analyzer = NetworkAnalyzer()
    
    # 测试不同规模的网络
    sizes = [100, 500, 1000]
    
    for size in sizes:
        logger.info(f"测试规模: {size}个节点")
        
        # 创建测试网络
        graph = nx.barabasi_albert_graph(size, 3)
        
        # 基础分析性能
        start_time = time.time()
        metrics = analyzer.analyze_network(graph, compute_expensive=False)
        analysis_time = time.time() - start_time
        
        logger.info(f"  基础分析耗时: {analysis_time:.3f}秒")
        
        # 度分布分析性能
        start_time = time.time()
        degree_dist = analyzer.analyze_degree_distribution(graph)
        degree_time = time.time() - start_time
        
        logger.info(f"  度分布分析耗时: {degree_time:.3f}秒")
    
    logger.info("✅ 性能基准测试完成")


def run_real_world_simulation():
    """运行真实世界仿真测试"""
    logger.info("🌍 开始真实世界网络仿真测试...")
    
    # 模拟侨批网络特征
    # 创建一个具有小世界特性的网络，模拟侨批网络
    
    # 节点：移民、家庭、机构
    num_migrants = 100
    num_families = 80
    num_institutions = 10
    
    total_nodes = num_migrants + num_families + num_institutions
    
    # 创建网络
    G = nx.Graph()
    
    # 添加节点并设置类型
    for i in range(num_migrants):
        G.add_node(i, type='migrant', region='southeast_asia')
    
    for i in range(num_migrants, num_migrants + num_families):
        G.add_node(i, type='family', region='guangdong')
    
    for i in range(num_migrants + num_families, total_nodes):
        G.add_node(i, type='institution', region='hub')
    
    # 添加边：模拟汇款关系
    np.random.seed(42)
    
    # 移民-家庭连接（汇款关系）
    for migrant_id in range(num_migrants):
        # 每个移民连接1-3个家庭
        num_connections = np.random.randint(1, 4)
        family_ids = np.random.choice(
            range(num_migrants, num_migrants + num_families),
            size=min(num_connections, num_families),
            replace=False
        )
        
        for family_id in family_ids:
            G.add_edge(migrant_id, family_id, relation='remittance')
    
    # 机构连接（中介关系）
    for migrant_id in range(num_migrants):
        # 每个移民选择1-2个机构
        num_institutions_used = np.random.randint(1, 3)
        institution_ids = np.random.choice(
            range(num_migrants + num_families, total_nodes),
            size=min(num_institutions_used, num_institutions),
            replace=False
        )
        
        for inst_id in institution_ids:
            G.add_edge(migrant_id, inst_id, relation='service')
    
    # 分析模拟网络
    analyzer = NetworkAnalyzer()
    
    logger.info(f"模拟侨批网络: {G.number_of_nodes()}个节点, {G.number_of_edges()}条边")
    
    # 生成完整分析报告
    report = analyzer.generate_network_report(G)
    
    # 输出关键指标
    metrics = report['basic_metrics']
    logger.info(f"网络密度: {metrics['density']:.4f}")
    logger.info(f"平均聚类系数: {metrics['avg_clustering_coefficient']:.4f}")
    logger.info(f"连通性: {'连通' if metrics['is_connected'] else '不连通'}")
    
    if metrics['small_world_coefficient']:
        logger.info(f"小世界系数: {metrics['small_world_coefficient']:.4f}")
    
    # 分析节点重要性
    importance = report['node_importance']
    if 'degree' in importance and importance['degree']:
        top_node = importance['degree'][0]
        node_type = G.nodes[top_node[0]].get('type', 'unknown')
        logger.info(f"最重要节点: {top_node[0]} (类型: {node_type}, 度中心性: {top_node[1]:.4f})")
    
    logger.info("✅ 真实世界仿真测试完成")


if __name__ == "__main__":
    """运行所有测试"""
    logger.info("🧪 开始图神经网络模块测试")
    
    if not TORCH_GEOMETRIC_AVAILABLE:
        logger.warning("⚠️ PyTorch Geometric不可用，将跳过GNN模型测试")
    
    try:
        # 图模型测试
        if TORCH_GEOMETRIC_AVAILABLE:
            test_models = TestGraphModels()
            test_models.setup_method()
            test_models.test_qiaopi_gnn_creation()
            test_models.test_qiaopi_gnn_forward_pass()
            test_models.test_node_embeddings()
            test_models.test_temporal_graph_network()
            test_models.test_attention_graph_network()
            test_models.test_model_factory()
        
        # 网络分析测试
        test_analyzer = TestNetworkAnalyzer()
        test_analyzer.setup_method()
        test_analyzer.test_basic_network_analysis()
        test_analyzer.test_node_importance_analysis()
        test_analyzer.test_degree_distribution_analysis()
        test_analyzer.test_network_robustness()
        test_analyzer.test_network_comparison()
        test_analyzer.test_temporal_evolution_analysis()
        test_analyzer.test_anomaly_detection()
        test_analyzer.test_network_report_generation()
        
        # 性能测试
        run_performance_benchmark()
        
        # 真实世界仿真
        run_real_world_simulation()
        
        logger.info("🎉 所有测试通过！图神经网络模块工作正常。")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
