#!/bin/bash

# Qiaopi Network Visualization System Setup and Run Script
# 侨批网络可视化系统安装运行脚本

echo "=========================================="
echo "Qiaopi Network Visualization System Setup"
echo "侨批网络可视化系统安装"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check Python version
echo -e "${YELLOW}Checking Python version...${NC}"
python_version=$(python3 --version 2>&1 | grep -Po '(?<=Python )\d+\.\d+')
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then 
    echo -e "${GREEN}✓ Python $python_version is installed${NC}"
else
    echo -e "${RED}✗ Python $required_version or higher is required${NC}"
    exit 1
fi

# Create virtual environment
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}Creating virtual environment...${NC}"
    python3 -m venv venv
    echo -e "${GREEN}✓ Virtual environment created${NC}"
else
    echo -e "${GREEN}✓ Virtual environment exists${NC}"
fi

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source venv/bin/activate

# Upgrade pip
echo -e "${YELLOW}Upgrading pip...${NC}"
pip install --upgrade pip > /dev/null 2>&1

# Install requirements
echo -e "${YELLOW}Installing requirements...${NC}"
echo "This may take a few minutes..."

# Core requirements
pip install plotly dash dash-bootstrap-components > /dev/null 2>&1
echo -e "  ${GREEN}✓ Dashboard libraries installed${NC}"

pip install pandas numpy scipy scikit-learn > /dev/null 2>&1
echo -e "  ${GREEN}✓ Data science libraries installed${NC}"

pip install flask flask-socketio python-socketio > /dev/null 2>&1
echo -e "  ${GREEN}✓ Real-time streaming libraries installed${NC}"

pip install networkx statsmodels > /dev/null 2>&1
echo -e "  ${GREEN}✓ Analytics libraries installed${NC}"

# Optional but recommended
pip install redis celery psutil structlog > /dev/null 2>&1 || true
echo -e "  ${GREEN}✓ Optional libraries installed${NC}"

# Create necessary directories
echo -e "${YELLOW}Creating directories...${NC}"
mkdir -p visualization_output
mkdir -p logs
mkdir -p data
echo -e "${GREEN}✓ Directories created${NC}"

# Check if Redis is running (optional)
if command -v redis-cli &> /dev/null; then
    if redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Redis server is running (optional caching enabled)${NC}"
    else
        echo -e "${YELLOW}⚠ Redis server not running (will use in-memory storage)${NC}"
    fi
else
    echo -e "${YELLOW}⚠ Redis not installed (will use in-memory storage)${NC}"
fi

echo ""
echo "=========================================="
echo -e "${GREEN}Setup complete!${NC}"
echo "=========================================="
echo ""
echo "Starting Qiaopi Visualization System..."
echo ""
echo "Dashboard will open at: http://localhost:8050"
echo "WebSocket server at: ws://localhost:8081"
echo ""
echo "Press Ctrl+C to stop"
echo ""

# Run the visualization system
python3 run_visualization.py