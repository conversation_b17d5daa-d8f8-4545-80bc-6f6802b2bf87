#!/usr/bin/env python3
"""
直接测试增强仿真脚本
"""

import os
import sys
import subprocess
import json

def test_simple_api():
    """测试简单API脚本"""
    print("🧪 Testing simple API script...")
    
    # 切换到父目录
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        # 运行简单测试脚本
        cmd = [
            sys.executable, 'test_api_simple.py',
            '--start-year', '1920',
            '--end-year', '1925', 
            '--migrants', '50',
            '--families', '50',
            '--institutions', '3',
            '--output-dir', 'enhanced_results'
        ]
        
        print(f"💻 Running command: {' '.join(cmd)}")
        print(f"📁 Working directory: {os.getcwd()}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        print(f"📊 Exit code: {result.returncode}")
        print(f"📄 STDOUT:\n{result.stdout}")
        
        if result.stderr:
            print(f"⚠️ STDERR:\n{result.stderr}")
            
        # 检查生成的文件
        results_dir = 'enhanced_results'
        if os.path.exists(results_dir):
            files = os.listdir(results_dir)
            print(f"📂 Generated files: {files}")
            
            for file in files:
                file_path = os.path.join(results_dir, file)
                size = os.path.getsize(file_path)
                print(f"   📄 {file}: {size} bytes")
        else:
            print("❌ Enhanced results directory not created")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ Script timeout (60s)")
        return False
    except Exception as e:
        print(f"❌ Error running script: {e}")
        return False
    finally:
        os.chdir(original_dir)

def test_full_api():
    """测试完整API脚本"""
    print("\n🔬 Testing full API script...")
    
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        if not os.path.exists('run_enhanced_demo_api.py'):
            print("⚠️ Full API script not found")
            return False
            
        cmd = [
            sys.executable, 'run_enhanced_demo_api.py',
            '--start-year', '1920',
            '--end-year', '1925',
            '--migrants', '50', 
            '--families', '50',
            '--institutions', '3',
            '--output-dir', 'enhanced_results_full'
        ]
        
        print(f"💻 Running command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        print(f"📊 Exit code: {result.returncode}")
        print(f"📄 STDOUT:\n{result.stdout}")
        
        if result.stderr:
            print(f"⚠️ STDERR:\n{result.stderr}")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ Script timeout (120s)")
        return False
    except Exception as e:
        print(f"❌ Error running full script: {e}")
        return False
    finally:
        os.chdir(original_dir)

def main():
    print("🎯 Testing Enhanced Simulation Scripts Directly")
    print("=" * 60)
    
    # 测试简单版本
    simple_ok = test_simple_api()
    
    # 测试完整版本
    full_ok = test_full_api()
    
    print("\n📊 Test Results:")
    print(f"   Simple API: {'✅ PASS' if simple_ok else '❌ FAIL'}")
    print(f"   Full API: {'✅ PASS' if full_ok else '❌ FAIL'}")
    
    if simple_ok or full_ok:
        print("\n✅ At least one script works - server should be able to generate results")
    else:
        print("\n❌ Both scripts failed - need to investigate Python environment")
        
    return simple_ok or full_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)