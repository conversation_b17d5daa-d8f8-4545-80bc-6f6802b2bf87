"""
深度强化学习智能体模块
Deep Reinforcement Learning Agents Module

这个模块实现了基于深度强化学习的智能体系统，用于替代现有的基于规则的决策系统。
智能体具备学习和适应能力，能够在复杂的侨批网络环境中做出最优决策。

主要组件：
- DeepRLMigrantAgent: 基于深度强化学习的移民智能体
- DeepRLFamilyAgent: 基于深度强化学习的家庭智能体  
- DeepRLInstitutionAgent: 基于深度强化学习的机构智能体
- QiaopiEnvironment: 侨批网络强化学习环境
- TrainingManager: 训练管理器
"""

from .rl_agents import (
    DeepRLMigrantAgent,
    DeepRLFamilyAgent, 
    DeepRLInstitutionAgent
)

from .environment import QiaopiEnvironment
# from .training import TrainingManager  # 需要wandb依赖
from .utils import AgentStateEncoder, ActionDecoder

__version__ = "1.0.0"
__author__ = "Qiaopi Research Team"

__all__ = [
    "DeepRLMigrantAgent",
    "DeepRLFamilyAgent", 
    "DeepRLInstitutionAgent",
    "QiaopiEnvironment",
    "TrainingManager",
    "AgentStateEncoder",
    "ActionDecoder"
]
