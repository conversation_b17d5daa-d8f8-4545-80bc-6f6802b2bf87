// 图表管理器
class ChartManager {
    constructor() {
        this.charts = new Map();
        this.colors = [
            '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
            '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#d35400'
        ];
    }

    // 获取默认配置
    getDefaultConfig(type = 'line') {
        const baseConfig = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 15
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#fff',
                    borderWidth: 1,
                    cornerRadius: 4,
                    displayColors: true
                }
            }
        };

        switch (type) {
            case 'line':
                return {
                    ...baseConfig,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '数值'
                            }
                        }
                    }
                };
            case 'bar':
                return {
                    ...baseConfig,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '类别'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '数值'
                            },
                            beginAtZero: true
                        }
                    }
                };
            case 'pie':
                return {
                    ...baseConfig,
                    cutout: '50%'
                };
            case 'doughnut':
                return {
                    ...baseConfig,
                    cutout: '70%'
                };
            default:
                return baseConfig;
        }
    }

    // 创建折线图
    createLineChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const config = {
            type: 'line',
            data: data,
            options: {
                ...this.getDefaultConfig('line'),
                ...options
            }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 创建柱状图
    createBarChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const config = {
            type: 'bar',
            data: data,
            options: {
                ...this.getDefaultConfig('bar'),
                ...options
            }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 创建饼图
    createPieChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const config = {
            type: 'pie',
            data: data,
            options: {
                ...this.getDefaultConfig('pie'),
                ...options
            }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 创建环形图
    createDoughnutChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const config = {
            type: 'doughnut',
            data: data,
            options: {
                ...this.getDefaultConfig('doughnut'),
                ...options
            }
        };

        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 创建延迟分布图（使用条形图替代箱线图）
    createBoxPlotChart(canvasId, stats, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁已存在的图表
        this.destroyChart(canvasId);

        // 使用条形图显示统计信息
        const labels = ['最小值', '25%', '中位数', '75%', '最大值', '90%', '99%'];
        const data = [
            stats.min ?? 0,
            stats.q1 ?? 0, 
            stats.median ?? 0,
            stats.q3 ?? 0,
            stats.max ?? 0,
            stats.p90 ?? 0,
            stats.p99 ?? 0
        ];

        const config = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '延迟统计(步数)',
                    data: data,
                    backgroundColor: ['#e74c3c', '#f39c12', '#2ecc71', '#3498db', '#9b59b6', '#1abc9c', '#34495e'],
                    borderColor: '#2c3e50',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '延迟时间(步)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.y} 步`;
                            }
                        }
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 创建桑基图（使用条形图替代）
    createSankeyChart(canvasId, links, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁已存在的图表
        this.destroyChart(canvasId);
        
        // 将桑基数据转换为条形图数据
        const labels = links.map(link => `${link.from}→${link.to}`);
        const data = links.map(link => link.flow);
        
        const config = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '流量',
                    data: data,
                    backgroundColor: this.colors.slice(0, data.length),
                    borderColor: '#2c3e50',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y', // 水平条形图
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '流量'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.x} 笔`;
                            }
                        }
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 销毁图表
    destroyChart(canvasId) {
        const existingChart = this.charts.get(canvasId);
        if (existingChart) {
            try {
                existingChart.destroy();
                this.charts.delete(canvasId);
                console.log(`图表 ${canvasId} 已销毁`);
            } catch (error) {
                console.error(`销毁图表 ${canvasId} 时出错:`, error);
            }
        }
    }

    // 销毁所有图表
    destroyAllCharts() {
        this.charts.forEach((chart, canvasId) => {
            try {
                chart.destroy();
            } catch (error) {
                console.error(`销毁图表 ${canvasId} 时出错:`, error);
            }
        });
        this.charts.clear();
    }

    // 获取图表颜色
    getColors(count) {
        return this.colors.slice(0, count);
    }

    // 提取时间序列数据的备用方法
    extractTimeSeries(simulationData) {
        if (!simulationData || !simulationData.step_by_step) {
            return [];
        }
        
        return simulationData.step_by_step.map(step => ({
            x: step.year,
            y: step.total_remittances || 0,
            success_rate: step.success_rate || 0,
            amount: step.total_amount || 0
        }));
    }

    // 创建仿真分布图表
    createSimulationDistributionChart(canvasId, simulations) {
        const statusCount = simulations.reduce((acc, sim) => {
            acc[sim.status] = (acc[sim.status] || 0) + 1;
            return acc;
        }, {});

        const labels = Object.keys(statusCount);
        const data = Object.values(statusCount);

        const chartData = {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: this.getColors(labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        this.createDoughnutChart(canvasId, chartData);
    }

    // 创建场景类型图表
    createScenarioTypeChart(canvasId, scenarios) {
        const typeCount = scenarios.reduce((acc, scenario) => {
            const type = this.getScenarioType(scenario);
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {});

        const labels = Object.keys(typeCount);
        const data = Object.values(typeCount);

        const chartData = {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: this.getColors(labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        this.createPieChart(canvasId, chartData);
    }

    // 创建仿真时间序列图表
    createSimulationTimeSeriesChart(canvasId, simulation) {
        if (!simulation.statistics || !simulation.statistics.timeSeries) {
            console.warn('No time series data available');
            return;
        }

        const timeSeries = simulation.statistics.timeSeries;
        
        const chartData = {
            labels: timeSeries.map(point => point.year),
            datasets: [
                {
                    label: '汇款总数',
                    data: timeSeries.map(point => point.total_remittances),
                    borderColor: this.colors[0],
                    backgroundColor: this.colors[0] + '20',
                    yAxisID: 'y'
                },
                {
                    label: '成功率',
                    data: timeSeries.map(point => point.success_rate * 100),
                    borderColor: this.colors[1],
                    backgroundColor: this.colors[1] + '20',
                    yAxisID: 'y1'
                }
            ]
        };

        const options = {
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '汇款数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '成功率 (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        };

        this.createLineChart(canvasId, chartData, options);
    }

    // 创建对比图表
    createComparisonChart(canvasId, simulations) {
        if (!simulations || simulations.length < 2) {
            console.warn('Need at least 2 simulations for comparison');
            return;
        }

        const datasets = simulations.map((sim, index) => ({
            label: sim.name,
            data: this.extractTimeSeries(sim),
            borderColor: this.colors[index],
            backgroundColor: this.colors[index] + '20'
        }));

        const chartData = {
            datasets: datasets
        };

        this.createLineChart(canvasId, chartData);
    }

    // 创建汇款金额分布图表
    createAmountDistributionChart(canvasId, amounts) {
        if (!amounts || amounts.length === 0) return;

        // 创建直方图数据
        const bins = this.createHistogramBins(amounts, 10);
        
        const chartData = {
            labels: bins.map(bin => `${bin.min.toFixed(0)}-${bin.max.toFixed(0)}`),
            datasets: [{
                label: '频次',
                data: bins.map(bin => bin.count),
                backgroundColor: this.colors[0] + '60',
                borderColor: this.colors[0],
                borderWidth: 1
            }]
        };

        this.createBarChart(canvasId, chartData, {
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '汇款金额区间'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '频次'
                    }
                }
            }
        });
    }

    // 创建地理分布图表
    createGeographicChart(canvasId, geoData) {
        if (!geoData || geoData.length === 0) return;

        const labels = geoData.map(item => item.location);
        const data = geoData.map(item => item.count);

        const chartData = {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: this.getColors(labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        this.createDoughnutChart(canvasId, chartData);
    }

    // 延迟分布图表（替代箱线图）
    createBoxPlotChart(canvasId, stats, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁已存在的图表
        this.destroyChart(canvasId);

        // 使用条形图显示统计信息
        const labels = ['最小值', '25%', '中位数', '75%', '最大值', '90%', '99%'];
        const data = [
            stats.min ?? 0,
            stats.q1 ?? 0, 
            stats.median ?? 0,
            stats.q3 ?? 0,
            stats.max ?? 0,
            stats.p90 ?? 0,
            stats.p99 ?? 0
        ];

        const config = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '延迟统计(步数)',
                    data: data,
                    backgroundColor: ['#e74c3c', '#f39c12', '#2ecc71', '#3498db', '#9b59b6', '#1abc9c', '#34495e'],
                    borderColor: '#2c3e50',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '延迟时间(步)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.y} 步`;
                            }
                        }
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 创建桑基图（使用水平条形图替代）
    createSankeyChart(canvasId, links, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁已存在的图表
        this.destroyChart(canvasId);
        
        // 将桑基数据转换为条形图数据
        const labels = links.map(link => `${link.from}→${link.to}`);
        const data = links.map(link => link.flow);
        
        const config = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '流量',
                    data: data,
                    backgroundColor: this.colors.slice(0, data.length),
                    borderColor: '#2c3e50',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y', // 水平条形图
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '流量'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.x} 笔`;
                            }
                        }
                    }
                },
                ...options
            }
        };
        
        const chart = new Chart(canvas, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    // 工具方法：创建直方图区间
    createHistogramBins(data, numBins) {
        const min = Math.min(...data);
        const max = Math.max(...data);
        const binWidth = (max - min) / numBins;
        
        const bins = [];
        for (let i = 0; i < numBins; i++) {
            const binMin = min + i * binWidth;
            const binMax = min + (i + 1) * binWidth;
            const count = data.filter(value => value >= binMin && value < binMax).length;
            
            bins.push({
                min: binMin,
                max: binMax,
                count: count
            });
        }
        
        return bins;
    }

    // 获取场景类型
    getScenarioType(scenario) {
        if (scenario.name) {
            if (scenario.name.includes('网络韧性')) return '网络韧性';
            if (scenario.name.includes('景观演化')) return '景观演化';
            if (scenario.name.includes('链式移民')) return '链式移民';
            if (scenario.name.includes('信任机制')) return '信任机制';
            if (scenario.name.includes('代际财富')) return '代际财富';
            if (scenario.name.includes('通信技术')) return '通信技术';
            if (scenario.name.includes('政策干预')) return '政策干预';
        }
        return '其他';
    }

    // 创建仿真统计图表
    static createSimulationStatisticsCharts(statistics) {
        // 这里可以根据统计数据创建各种图表
        console.log('Creating simulation statistics charts...', statistics);
    }
}

// Chart Factory
class ChartFactory {
    static chartManager = new ChartManager();

    // 创建仿真分布图表
    static createSimulationDistributionChart(canvasId, simulations) {
        if (simulations && simulations.length > 0) {
            this.chartManager.createSimulationDistributionChart(canvasId, simulations);
        }
    }

    // 创建场景类型图表
    static createScenarioTypeChart(canvasId, scenarioTypes) {
        this.chartManager.createScenarioTypeChart(canvasId, scenarioTypes);
    }

    // 创建仿真时间序列图表
    static createSimulationTimeSeriesChart(canvasId, simulation) {
        if (simulation && simulation.statistics) {
            this.chartManager.createSimulationTimeSeriesChart(canvasId, simulation);
        }
    }

    // 创建对比图表
    static createComparisonChart(canvasId, simulations) {
        if (simulations && simulations.length >= 2) {
            this.chartManager.createComparisonChart(canvasId, simulations);
        }
    }

    // 创建真实数据图表
    static createRealDataCharts(realData) {
        if (realData && realData.length > 0) {
            // 汇款金额分布
            const amounts = realData.map(record => record.amount).filter(a => a > 0);
            if (amounts.length > 0) {
                this.chartManager.createAmountDistributionChart('realAmountDistributionChart', amounts);
            }

            // 地理分布
            const geoCount = {};
            realData.forEach(record => {
                const location = record.sender_location || '未知';
                geoCount[location] = (geoCount[location] || 0) + 1;
            });
            const geoData = Object.entries(geoCount).map(([location, count]) => ({
                location, count
            }));
            if (geoData.length > 0) {
                this.chartManager.createGeographicChart('realGeographicDistributionChart', geoData);
            }
        }
    }
}

// 导出供其他模块使用
window.ChartManager = ChartManager;
window.ChartFactory = ChartFactory;