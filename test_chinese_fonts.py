"""
测试matplotlib中文字体显示
Test Chinese Font Display in Matplotlib

这个脚本用来测试和验证matplotlib是否能正确显示中文字符
"""

import matplotlib.pyplot as plt
import matplotlib
import numpy as np

def test_chinese_fonts():
    """测试中文字体显示"""
    print("🔍 测试matplotlib中文字体显示...")
    
    # 配置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 获取当前字体信息
    current_font = matplotlib.rcParams['font.sans-serif']
    print(f"当前字体配置: {current_font}")
    
    # 创建简单的中文图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 左图：柱状图
    categories = ['侨批数量', '平均金额', '成功率', '网络密度']
    values = [100, 50, 0.9, 0.3]
    
    bars = ax1.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax1.set_title('侨批网络关键指标', fontsize=14, fontweight='bold')
    ax1.set_ylabel('指标值')
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                f'{value}', ha='center', va='bottom')
    
    # 右图：折线图
    months = ['1月', '2月', '3月', '4月', '5月', '6月']
    qiaopi_count = [80, 95, 110, 85, 120, 105]
    
    ax2.plot(months, qiaopi_count, 'o-', linewidth=2, markersize=6, color='#FF6B6B')
    ax2.set_title('月度侨批数量趋势', fontsize=14, fontweight='bold')
    ax2.set_xlabel('月份')
    ax2.set_ylabel('侨批数量')
    ax2.grid(True, alpha=0.3)
    
    # 添加说明文本
    fig.suptitle('侨批网络AI分析系统 - 中文字体测试', fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('chinese_font_test.png', dpi=150, bbox_inches='tight')
    print("✅ 中文字体测试图表已保存为 chinese_font_test.png")
    
    # 显示图表（如果在交互环境中）
    try:
        plt.show()
        print("✅ 图表显示成功")
    except Exception as e:
        print(f"⚠️ 图表显示跳过（非交互环境）: {e}")
    
    plt.close()


def check_available_fonts():
    """检查系统可用的中文字体"""
    print("\n🔍 检查系统可用字体...")
    
    font_manager = matplotlib.font_manager
    
    # 获取所有字体
    all_fonts = [f.name for f in font_manager.fontManager.ttflist]
    
    # 查找可能的中文字体
    chinese_fonts = []
    chinese_keywords = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 
                       'Microsoft JhengHei', 'PMingLiU', 'MingLiU', 'DFKai-SB',
                       'Arial Unicode MS', 'Noto Sans CJK', 'Source Han Sans']
    
    for font in set(all_fonts):
        for keyword in chinese_keywords:
            if keyword.lower() in font.lower():
                chinese_fonts.append(font)
                break
    
    if chinese_fonts:
        print("✅ 找到以下可能支持中文的字体:")
        for font in sorted(set(chinese_fonts)):
            print(f"  • {font}")
    else:
        print("⚠️ 未找到明确的中文字体，将使用默认字体配置")
    
    # 显示当前配置
    print(f"\n当前matplotlib字体配置:")
    print(f"  font.family: {matplotlib.rcParams['font.family']}")
    print(f"  font.sans-serif: {matplotlib.rcParams['font.sans-serif']}")
    
    return chinese_fonts


def main():
    """主函数"""
    print("🚀 启动matplotlib中文字体测试")
    print("=" * 50)
    
    # 检查可用字体
    available_fonts = check_available_fonts()
    
    print("\n" + "=" * 50)
    
    # 测试中文显示
    test_chinese_fonts()
    
    print("\n" + "=" * 50)
    print("🎉 中文字体测试完成！")
    
    if available_fonts:
        print("✅ 建议使用的中文字体:", available_fonts[0] if available_fonts else "Microsoft YaHei")
    
    print("\n📁 生成文件:")
    print("  • chinese_font_test.png - 中文字体测试图表")
    
    return available_fonts


if __name__ == "__main__":
    fonts = main()
