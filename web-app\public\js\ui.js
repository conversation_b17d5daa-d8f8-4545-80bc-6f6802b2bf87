// 主要UI逻辑管理器
class UIManager {
    constructor() {
        this.currentSection = 'overview';
        this.simulations = [];
        this.scenarios = [];
        this.realData = null;
        this.selectedSimulations = [];
        this.sampleRealData = this.generateSampleRealData(); // 固定数据源
        
        this.initializeEventListeners();
        this.loadInitialData();
    }

    // 初始化事件监听器
    initializeEventListeners() {
        // 导航链接
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const onclick = link.getAttribute('onclick');
                if (onclick) {
                    const match = onclick.match(/showSection\('([^']+)'\)/);
                    if (match) {
                        this.showSection(match[1]);
                    }
                }
            });
        });

        // 操作按钮事件
        this.setupButtonEvents();
    }

    setupButtonEvents() {
        // 使用事件委托来处理动态生成的按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('[onclick*="loadSimulations"]')) {
                e.preventDefault();
                this.loadSimulationsData();
            } else if (e.target.matches('[onclick*="runNewSimulation"]')) {
                e.preventDefault();
                this.runNewSimulation();
            } else if (e.target.matches('[onclick*="viewRealData"]')) {
                e.preventDefault();
                this.viewRealData();
            } else if (e.target.matches('[onclick*="exportResults"]')) {
                e.preventDefault();
                this.exportResults();
            } else if (e.target.matches('[onclick*="compareSimulations"]')) {
                e.preventDefault();
                this.compareSimulations();
            }
        });
    }

    // 加载初始数据
    async loadInitialData() {
        try {
            // 使用丰富的演示数据
            this.simulations = [
                {
                    id: 'demo_results',
                    name: '侨批网络基础仿真',
                    description: '基于真实数据的侨批网络基础功能演示',
                    createdAt: '2025-08-27T10:30:00.000Z',
                    status: 'completed',
                    agentCount: 560,
                    timeSpan: { startYear: 1920, endYear: 1940 },
                    statistics: {
                        total_remittances: 1850,
                        success_rate: 0.87,
                        total_amount: 245000
                    }
                },
                {
                    id: 'scenario_1_results', 
                    name: '网络韧性仿真',
                    description: '测试侨批网络在经济危机下的韧性表现',
                    createdAt: '2025-08-26T14:20:00.000Z',
                    status: 'completed',
                    agentCount: 320,
                    timeSpan: { startYear: 1925, endYear: 1945 },
                    statistics: {
                        total_remittances: 1240,
                        success_rate: 0.73,
                        total_amount: 180000
                    }
                },
                {
                    id: 'scenario_2_results',
                    name: '链式移民仿真', 
                    description: '分析移民链式迁移对汇款网络的影响',
                    createdAt: '2025-08-25T09:15:00.000Z',
                    status: 'completed',
                    agentCount: 420,
                    timeSpan: { startYear: 1880, endYear: 1930 },
                    statistics: {
                        total_remittances: 2100,
                        success_rate: 0.91,
                        total_amount: 320000
                    }
                }
            ];
            
            this.scenarios = [
                { 
                    id: 'scenario_1', 
                    name: '网络韧性分析', 
                    description: '分析侨批网络在面对外部冲击时的韧性表现',
                    parameters: {
                        'initial_migrants': 150,
                        'shock_frequency': 0.1,
                        'recovery_rate': 0.8,
                        'simulation_years': 20
                    },
                    complexity: 'medium'
                },
                { 
                    id: 'scenario_2', 
                    name: '景观演化分析', 
                    description: '研究侨批网络景观的长期演化模式',
                    parameters: {
                        'initial_migrants': 200,
                        'evolution_rate': 0.05,
                        'adaptation_threshold': 0.6,
                        'simulation_years': 50
                    },
                    complexity: 'high'
                },
                { 
                    id: 'scenario_3', 
                    name: '链式移民分析', 
                    description: '分析移民网络中的链式迁移模式',
                    parameters: {
                        'initial_migrants': 120,
                        'chain_strength': 0.8,
                        'migration_rate': 0.15,
                        'simulation_years': 30
                    },
                    complexity: 'medium'
                },
                {
                    id: 'scenario_4',
                    name: '信任机制研究',
                    description: '研究信任机制对侨批网络效率的影响',
                    parameters: {
                        'initial_trust': 0.7,
                        'trust_decay': 0.05,
                        'reputation_weight': 0.3,
                        'simulation_years': 25
                    },
                    complexity: 'high'
                },
                {
                    id: 'scenario_5',
                    name: '政策影响分析',
                    description: '分析政府政策对侨批网络的调节作用',
                    parameters: {
                        'policy_strength': 0.5,
                        'compliance_rate': 0.8,
                        'adaptation_time': 5,
                        'simulation_years': 40
                    },
                    complexity: 'very_high'
                }
            ];
            
            this.realData = {
                total_records: 13403,
                summary: '真实侨批数据',
                data_quality: 'high',
                coverage_period: '1920-1950',
                geographic_coverage: '20+国家地区'
            };

            this.updateUI();
            
            // 确保总览图表在数据加载后渲染
            setTimeout(() => {
                if (this.currentSection === 'overview') {
                    this.renderOverviewCharts();
                }
            }, 500);
            
        } catch (error) {
            console.error('Error in loadInitialData:', error);
        }
    }

    // 显示指定部分
    showSection(sectionName) {
        console.log('🎯 Showing section:', sectionName);
        
        // 隐藏所有部分
        document.querySelectorAll('.content-section').forEach(section => {
            section.style.display = 'none';
            console.log('🙈 Hidden section:', section.id);
        });

        // 显示指定部分
        const targetSection = document.getElementById(`${sectionName}-section`);
        console.log('🎯 Target section element:', targetSection);
        
        if (targetSection) {
            targetSection.style.display = 'block';
            targetSection.style.visibility = 'visible';
            targetSection.style.opacity = '1';
            console.log('👁️ Showing section:', `${sectionName}-section`);
            
            // 确保父容器也是可见的
            let parent = targetSection.parentElement;
            while (parent && parent !== document.body) {
                if (parent.style.display === 'none') {
                    parent.style.display = 'block';
                    console.log('👁️ Made parent visible:', parent.tagName, parent.className);
                }
                parent = parent.parentElement;
            }
        } else {
            console.error('❌ Target section not found:', `${sectionName}-section`);
            // 显示错误提示
            this.showNotification(`页面 "${sectionName}" 不存在，请检查配置`, 'error');
        }

        // 更新导航状态
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // 找到并激活当前链接
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            const onclick = link.getAttribute('onclick');
            if (onclick && onclick.includes(`'${sectionName}'`)) {
                link.classList.add('active');
                console.log('🎯 Activated nav link for:', sectionName);
            }
        });

        this.currentSection = sectionName;
        this.loadSectionData(sectionName);
        
        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // 加载部分数据
    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'overview':
                this.loadOverviewData();
                break;
            case 'simulations':
                this.loadSimulationsData();
                break;
            case 'agents':
                this.loadAgentsData();
                break;
            case 'scenarios':
                this.loadScenariosData();
                break;
            case 'comparison':
                this.loadComparisonData();
                break;
            case 'real-data':
                this.loadRealDataSection();
                break;
        }
    }

    // 加载总览数据
    loadOverviewData() {
        console.log('Loading overview data');
        this.updateRecentSimulations();
        this.renderOverviewStats();
        this.renderOverviewCharts();
    }

    // 加载仿真数据
    loadSimulationsData() {
        console.log('Loading simulations data');
        this.renderSimulationsGrid(this.simulations);
    }

    // 加载智能体数据
    loadAgentsData() {
        console.log('Loading agents data');
        this.renderMyAgents();
    }

    // 加载场景数据
    loadScenariosData() {
        console.log('Loading scenarios data');
        this.renderScenariosGrid(this.scenarios);
    }

    // 加载对比数据
    loadComparisonData() {
        console.log('Loading comparison data');
        this.updateSimulationSelectors();
        this.renderComparisonInterface();
    }

    // 加载真实数据部分
    loadRealDataSection() {
        setTimeout(async () => {
            // 确保realDataStats容器存在
            let statsContainer = document.getElementById('realDataStats');
            if (!statsContainer) {
                console.warn('创建缺失的统计容器...');
                const realDataSection = document.getElementById('real-data-section');
                if (realDataSection) {
                    // 创建缺失的统计容器
                    const statsRow = document.createElement('div');
                    statsRow.className = 'row mt-4';
                    statsRow.innerHTML = `
                        <div class="col-12">
                            <div class="real-data-stats">
                                <h5>数据统计</h5>
                                <div id="realDataStats" class="row">
                                    <!-- 动态加载统计数据 -->
                                </div>
                            </div>
                        </div>
                    `;
                    realDataSection.appendChild(statsRow);
                    statsContainer = document.getElementById('realDataStats');
                }
            }
            
            if (!statsContainer) {
                console.error('无法创建统计容器');
                return;
            }

            // 优先尝试从后端获取最新高级报告以驱动可视化
            try {
                const latestReport = await window.QiaopiAPI.getLatestAdvancedReport();
                if (latestReport && latestReport.advanced_analysis) {
                    this.renderAdvancedAnalysis(latestReport);
                }
            } catch (err) {
                console.warn('加载高级分析失败，回退到本地示例数据:', err);
            }
            
            // 使用样本数据或API数据
            const data = this.sampleRealData || [];
            this.updateRealDataStats(data);
            this.renderRealDataCharts();
        }, 100);
    }

    // 渲染总览统计
    renderOverviewStats() {
        console.log('📊 更新总览统计数字...');
        
        // 更新Hero区域的统计数字
        const stats = document.querySelectorAll('.stat-number');
        console.log('📈 找到', stats.length, '个统计数字容器');
        
        if (stats.length >= 3) {
            stats[0].textContent = '13,403'; // 真实侨批记录
            stats[1].textContent = this.scenarios.length || '7'; // 仿真场景
            stats[2].textContent = this.simulations.length || '50+'; // 仿真结果
            console.log('✅ 统计数字已更新:', stats[0].textContent, stats[1].textContent, stats[2].textContent);
        } else {
            console.warn('⚠️ 统计数字容器不足，期望3个，实际', stats.length, '个');
        }
    }

    // 渲染总览图表
    renderOverviewCharts() {
        console.log('📊 渲染总览图表...');
        
        // 确保ChartManager可用
        let cm;
        if (window.ChartFactory && window.ChartFactory.chartManager) {
            cm = window.ChartFactory.chartManager;
        } else if (window.ChartManager) {
            cm = new window.ChartManager();
        } else {
            console.error('ChartManager not available for overview charts');
            return;
        }

        // 1. 仿真分布图表
        this.renderSimulationDistributionChart(cm);
        
        // 2. 场景类型图表  
        this.renderScenarioTypeChart(cm);
    }

    // 渲染仿真分布图表
    renderSimulationDistributionChart(chartManager) {
        try {
            console.log('📈 创建仿真分布图表...');
            
            // 按状态统计仿真
            const statusCount = this.simulations.reduce((acc, sim) => {
                const status = sim.status || 'unknown';
                acc[status] = (acc[status] || 0) + 1;
                return acc;
            }, {});

            // 如果没有数据，创建示例数据
            if (Object.keys(statusCount).length === 0) {
                statusCount['completed'] = this.simulations.length || 3;
                statusCount['running'] = 1;
                statusCount['pending'] = 1;
            }

            const labels = Object.keys(statusCount).map(status => {
                switch(status) {
                    case 'completed': return '已完成';
                    case 'running': return '运行中';
                    case 'pending': return '等待中';
                    case 'failed': return '失败';
                    default: return status;
                }
            });
            const data = Object.values(statusCount);
            const colors = ['#2ecc71', '#3498db', '#f39c12', '#e74c3c'];

            chartManager.createDoughnutChart('simulationDistributionChart', {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            });
            
            console.log('✅ 仿真分布图表创建成功');
        } catch (error) {
            console.error('❌ 仿真分布图表创建失败:', error);
        }
    }

    // 渲染场景类型图表
    renderScenarioTypeChart(chartManager) {
        try {
            console.log('📈 创建场景类型图表...');
            
            // 按类型统计场景
            const typeCount = this.scenarios.reduce((acc, scenario) => {
                const type = this.getScenarioType(scenario);
                acc[type] = (acc[type] || 0) + 1;
                return acc;
            }, {});

            // 如果没有数据，创建示例数据
            if (Object.keys(typeCount).length === 0) {
                typeCount['网络韧性'] = 1;
                typeCount['链式移民'] = 1;
                typeCount['景观演化'] = 1;
                typeCount['信任机制'] = 1;
                typeCount['政策影响'] = 1;
            }

            const labels = Object.keys(typeCount);
            const data = Object.values(typeCount);
            const colors = chartManager.getColors(labels.length);

            chartManager.createPieChart('scenarioTypeChart', {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            });
            
            console.log('✅ 场景类型图表创建成功');
        } catch (error) {
            console.error('❌ 场景类型图表创建失败:', error);
        }
    }

    // 获取场景类型
    getScenarioType(scenario) {
        if (scenario.name) {
            if (scenario.name.includes('网络韧性')) return '网络韧性';
            if (scenario.name.includes('景观演化')) return '景观演化';
            if (scenario.name.includes('链式移民')) return '链式移民';
            if (scenario.name.includes('信任机制')) return '信任机制';
            if (scenario.name.includes('代际财富')) return '代际财富';
            if (scenario.name.includes('通信技术')) return '通信技术';
            if (scenario.name.includes('政策')) return '政策影响';
        }
        return '其他';
    }

    // 渲染对比界面
    renderComparisonInterface() {
        const comparisonResults = document.getElementById('comparisonResults');
        if (comparisonResults && this.simulations.length === 0) {
            comparisonResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>对比分析</h6>
                    <p>当前没有仿真结果可供对比。请先运行一些仿真实验。</p>
                    <button class="btn btn-primary" onclick="showSection('simulations')">
                        <i class="fas fa-play me-2"></i>运行仿真
                    </button>
                </div>
            `;
        }
    }

    // 渲染真实数据内容
    renderRealDataContent() {
        console.log('Rendering real data content:', this.realData);
        
        // 渲染统计数据
        this.renderRealDataStats();
        
        // 渲染图表
        this.renderRealDataCharts();
        
        // 渲染详细数据表格
        this.renderRealDataTable();
        
        // 渲染数据洞察
        this.renderDataInsights();
    }

    // 渲染高级分析到页面（添加几个图表容器并绘制）
    renderAdvancedAnalysis(report) {
        console.log('🎯 开始渲染高级分析...', report);
        const adv = report.advanced_analysis || {};
        console.log('📊 高级分析数据:', adv);
        
        const containerSection = document.getElementById('real-data-section');
        if (!containerSection) {
            console.error('❌ real-data-section 容器未找到');
            return;
        }

        // 动态插入一个“高级分析”卡片区域（避免重复）
        if (!document.getElementById('advancedAnalysisBlock')) {
            const block = document.createElement('div');
            block.id = 'advancedAnalysisBlock';
            block.className = 'row mt-4';
            block.innerHTML = `
                <div class="col-12">
                  <div class="card">
                    <div class="card-header"><h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>高级分析</h5></div>
                    <div class="card-body">
                      <div class="row g-3">
                        <div class="col-md-6"><div class="chart-container"><h5>月度季节性</h5><canvas id="advMonthlyCounts"></canvas></div></div>
                        <div class="col-md-6"><div class="chart-container"><h5>机构成功率</h5><canvas id="advInstitutionSuccess"></canvas></div></div>
                        <div class="col-md-6"><div class="chart-container"><h5>通道TOP(笔数)</h5><canvas id="advCorridorCount"></canvas></div></div>
                        <div class="col-md-6"><div class="chart-container"><h5>货币使用占比</h5><canvas id="advCurrencyUsage"></canvas></div></div>
                        <div class="col-md-6"><div class="chart-container"><h5>递送延迟分布</h5><canvas id="advDelayBox"></canvas></div></div>
                        <div class="col-md-6"><div class="chart-container"><h5>通道桑基图</h5><canvas id="advCorridorSankey"></canvas></div></div>
                      </div>
                    </div>
                  </div>
                </div>`;
            containerSection.querySelector('.section-header')?.insertAdjacentElement('afterend', block);
        }

        // 确保ChartManager可用
        let cm;
        if (window.ChartFactory && window.ChartFactory.chartManager) {
            cm = window.ChartFactory.chartManager;
        } else if (window.ChartManager) {
            cm = new window.ChartManager();
        } else {
            console.error('ChartManager not available');
            return;
        }

        // 清理可能存在的旧图表
        console.log('🗑️ 清理旧图表...');
        const chartIds = ['advMonthlyCounts', 'advInstitutionSuccess', 'advCorridorCount', 'advCurrencyUsage', 'advDelayBox', 'advCorridorSankey'];
        chartIds.forEach(id => {
            try {
                cm.destroyChart(id);
            } catch (err) {
                // 忽略销毁错误
            }
        });

        // 月度季节性
        if (adv.remittance_dynamics?.monthly_counts) {
            console.log('📈 创建月度季节性图表...');
            const labels = Object.keys(adv.remittance_dynamics.monthly_counts).map(m => `月${m}`);
            const data = Object.values(adv.remittance_dynamics.monthly_counts);
            try {
                cm.createBarChart('advMonthlyCounts', {
                    labels,
                    datasets: [{ label: '汇款笔数', data, backgroundColor: '#3498db80', borderColor: '#3498db' }]
                });
                console.log('✅ 月度季节性图表创建成功');
            } catch (err) {
                console.error('❌ 月度季节性图表创建失败:', err);
            }
        } else {
            console.warn('⚠️ 月度数据不可用');
        }

        // 机构成功率
        if (adv.institution_performance?.institutions) {
            const entries = Object.entries(adv.institution_performance.institutions);
            const labels = entries.map(([id, v]) => id.slice(0, 6));
            const data = entries.map(([_, v]) => Math.round((v.success_rate || 0) * 100));
            cm.createBarChart('advInstitutionSuccess', {
                labels,
                datasets: [{ label: '成功率(%)', data, backgroundColor: '#2ecc7180', borderColor: '#2ecc71' }]
            }, { scales: { y: { beginAtZero: true, max: 100 } } });
        }

        // 通道TOP（按笔数）
        if (adv.corridor_flows?.top_by_count) {
            const top = adv.corridor_flows.top_by_count;
            const labels = top.map(x => `${x.origin}→${x.destination}`);
            const data = top.map(x => x.count);
            cm.createBarChart('advCorridorCount', {
                labels,
                datasets: [{ label: '笔数', data, backgroundColor: '#f39c1280', borderColor: '#f39c12' }]
            });
        }

        // 货币使用占比
        if (adv.currency_usage) {
            const labels = Object.keys(adv.currency_usage);
            const data = labels.map(k => adv.currency_usage[k].share * 100);
            cm.createDoughnutChart('advCurrencyUsage', {
                labels,
                datasets: [{ label: '占比(%)', data, backgroundColor: cm.getColors(labels.length) }]
            });
        }

        // 延迟分布箱线图
        if (adv.remittance_dynamics?.delivery_delay_stats) {
            cm.createBoxPlotChart('advDelayBox', adv.remittance_dynamics.delivery_delay_stats);
        }

        // 通道桑基图（前10条）
        if (adv.corridor_flows?.top_by_count) {
            const top = adv.corridor_flows.top_by_count.slice(0, 10);
            const links = top.map(x => ({ from: x.origin, to: x.destination, flow: x.count }));
            cm.createSankeyChart('advCorridorSankey', links);
        }
    }

    // 渲染真实数据统计
    renderRealDataStats() {
        const realDataStats = document.getElementById('realDataStats');
        if (!realDataStats) return;

        const stats = this.calculateRealDataStats();
        
        realDataStats.innerHTML = `
            <div class="col-md-2">
                <div class="data-stat-item">
                    <div class="data-stat-label">总记录数</div>
                    <div class="data-stat-value">${stats.total_records.toLocaleString()}</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="data-stat-item">
                    <div class="data-stat-label">总金额</div>
                    <div class="data-stat-value">¥${stats.total_amount.toLocaleString()}</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="data-stat-item">
                    <div class="data-stat-label">平均金额</div>
                    <div class="data-stat-value">¥${stats.average_amount.toLocaleString()}</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="data-stat-item">
                    <div class="data-stat-label">覆盖地区</div>
                    <div class="data-stat-value">${stats.regions}个</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="data-stat-item">
                    <div class="data-stat-label">时间跨度</div>
                    <div class="data-stat-value">${stats.time_span}</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="data-stat-item">
                    <div class="data-stat-label">数据完整性</div>
                    <div class="data-stat-value">${stats.completeness}%</div>
                </div>
            </div>
        `;
    }

    // 计算真实数据统计
    calculateRealDataStats() {
        if (!this.realData) {
            // 生成模拟统计数据
            return {
                total_records: 15420,
                total_amount: 2847500,
                average_amount: 185,
                regions: 23,
                time_span: '1920-1950',
                completeness: 87
            };
        }

        const numeric = this.realData.numeric_analysis || {};
        const categorical = this.realData.categorical_analysis || {};
        
        const amounts = numeric.moneyAmount?.raw_values || [];
        const total_amount = amounts.reduce((sum, val) => sum + (val || 0), 0);
        const average_amount = amounts.length > 0 ? Math.round(total_amount / amounts.length) : 0;
        
        const regions = categorical.recvLocation?.length || 0;
        
        return {
            total_records: this.realData.total_records || amounts.length,
            total_amount: total_amount,
            average_amount: average_amount,
            regions: regions,
            time_span: '1920-1950',
            completeness: Math.round((amounts.filter(val => val != null).length / amounts.length) * 100) || 87
        };
    }

    // 渲染真实数据图表
    renderRealDataCharts() {
        // 使用requestAnimationFrame确保DOM已准备好
        requestAnimationFrame(() => {
            this.createRealDataCharts();
        });
    }

    // 创建真实数据图表
    createRealDataCharts() {
        // 金额分布图表
        this.createAmountDistributionChart();
        
        // 地理分布图表
        this.createGeographicDistributionChart();
        
        // 时间趋势图表
        this.createTimeTrendChart();
        
        // 汇款类型分布图表
        this.createRemittanceTypeChart();
    }

    // 创建金额分布图表
    createAmountDistributionChart() {
        const canvas = document.getElementById('realAmountDistributionChart');
        if (!canvas) return;

        // 生成模拟金额分布数据
        const amounts = this.generateAmountDistribution();
        
        const data = {
            labels: ['< 50元', '50-100元', '100-200元', '200-500元', '500-1000元', '> 1000元'],
            datasets: [{
                label: '汇款笔数',
                data: amounts,
                backgroundColor: [
                    '#3498db',
                    '#2ecc71',
                    '#f39c12',
                    '#e74c3c',
                    '#9b59b6',
                    '#1abc9c'
                ],
                borderWidth: 1
            }]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: '汇款金额分布统计'
                }
            }
        };

        // 销毁已存在的图表
        if (window.Chart && window.Chart.getChart(canvas)) {
            window.Chart.getChart(canvas).destroy();
        }

        // 创建新图表
        if (window.Chart) {
            new Chart(canvas, {
                type: 'doughnut',
                data: data,
                options: options
            });
        }
    }

    // 创建地理分布图表
    createGeographicDistributionChart() {
        const canvas = document.getElementById('realGeographicDistributionChart');
        if (!canvas) return;

        // 生成模拟地理分布数据
        const geoData = this.generateGeographicDistribution();
        
        const data = {
            labels: geoData.map(item => item.region),
            datasets: [{
                label: '汇款笔数',
                data: geoData.map(item => item.count),
                backgroundColor: [
                    '#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6',
                    '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
                ],
                borderWidth: 1
            }]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: '接收地区分布'
                }
            }
        };

        // 销毁已存在的图表
        if (window.Chart && window.Chart.getChart(canvas)) {
            window.Chart.getChart(canvas).destroy();
        }

        // 创建新图表
        if (window.Chart) {
            new Chart(canvas, {
                type: 'bar',
                data: data,
                options: options
            });
        }
    }

    // 创建时间趋势图表
    createTimeTrendChart() {
        // 创建时间趋势图表容器
        const container = document.querySelector('#real-data-section .row.mt-4');
        if (!container) return;

        // 检查是否已存在时间趋势图表
        if (document.getElementById('realTimeTrendChart')) return;

        const timeTrendHTML = `
            <div class="col-12 mt-4">
                <div class="chart-container">
                    <h5>时间趋势分析</h5>
                    <canvas id="realTimeTrendChart" height="100"></canvas>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', timeTrendHTML);

        const canvas = document.getElementById('realTimeTrendChart');
        if (!canvas) return;

        // 生成时间趋势数据
        const timeData = this.generateTimeTrendData();
        
        const data = {
            labels: timeData.map(item => item.year),
            datasets: [
                {
                    label: '汇款笔数',
                    data: timeData.map(item => item.count),
                    borderColor: '#3498db',
                    backgroundColor: '#3498db20',
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: '总金额 (千元)',
                    data: timeData.map(item => item.amount / 1000),
                    borderColor: '#e74c3c',
                    backgroundColor: '#e74c3c20',
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '年份'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '汇款笔数'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '金额 (千元)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    }
                }
            }
        };

        // 创建新图表
        if (window.Chart) {
            new Chart(canvas, {
                type: 'line',
                data: data,
                options: options
            });
        }
    }

    // 创建汇款类型分布图表
    createRemittanceTypeChart() {
        // 创建汇款类型图表容器
        const container = document.querySelector('#real-data-section .row.mt-4');
        if (!container) return;

        // 检查是否已存在汇款类型图表
        if (document.getElementById('realRemittanceTypeChart')) return;

        const remittanceTypeHTML = `
            <div class="col-md-6 mt-4">
                <div class="chart-container">
                    <h5>汇款类型分布</h5>
                    <canvas id="realRemittanceTypeChart" height="100"></canvas>
                </div>
            </div>
            <div class="col-md-6 mt-4">
                <div class="chart-container">
                    <h5>汇款渠道分布</h5>
                    <canvas id="realChannelChart" height="100"></canvas>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', remittanceTypeHTML);

        // 汇款类型图表
        const typeCanvas = document.getElementById('realRemittanceTypeChart');
        if (typeCanvas) {
            const typeData = this.generateRemittanceTypeData();
            
            const data = {
                labels: typeData.map(item => item.type),
                datasets: [{
                    label: '笔数',
                    data: typeData.map(item => item.count),
                    backgroundColor: ['#3498db', '#2ecc71', '#f39c12', '#e74c3c']
                }]
            };

            if (window.Chart) {
                new Chart(typeCanvas, {
                    type: 'pie',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // 汇款渠道图表
        const channelCanvas = document.getElementById('realChannelChart');
        if (channelCanvas) {
            const channelData = this.generateChannelData();
            
            const data = {
                labels: channelData.map(item => item.channel),
                datasets: [{
                    label: '笔数',
                    data: channelData.map(item => item.count),
                    backgroundColor: ['#9b59b6', '#1abc9c', '#34495e', '#e67e22']
                }]
            };

            if (window.Chart) {
                new Chart(channelCanvas, {
                    type: 'doughnut',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }
    }

    // 生成金额分布数据
    generateAmountDistribution() {
        return [1200, 2800, 4500, 3200, 1800, 920];
    }

    // 生成地理分布数据
    generateGeographicDistribution() {
        return [
            { region: '福建', count: 3200 },
            { region: '广东', count: 2800 },
            { region: '浙江', count: 1800 },
            { region: '江苏', count: 1200 },
            { region: '山东', count: 900 },
            { region: '河北', count: 800 },
            { region: '河南', count: 700 },
            { region: '湖北', count: 600 },
            { region: '湖南', count: 500 },
            { region: '其他', count: 1320 }
        ];
    }

    // 生成时间趋势数据
    generateTimeTrendData() {
        const years = [];
        for (let year = 1920; year <= 1950; year += 2) {
            years.push({
                year: year,
                count: Math.floor(Math.random() * 200) + 100,
                amount: Math.floor(Math.random() * 50000) + 20000
            });
        }
        return years;
    }

    // 生成汇款类型数据
    generateRemittanceTypeData() {
        return [
            { type: '家庭汇款', count: 8500 },
            { type: '教育费用', count: 3200 },
            { type: '医疗费用', count: 1800 },
            { type: '其他', count: 1920 }
        ];
    }

    // 生成汇款渠道数据
    generateChannelData() {
        return [
            { channel: '侨批局', count: 6800 },
            { channel: '银行', count: 4200 },
            { channel: '邮局', count: 2800 },
            { channel: '私人', count: 1620 }
        ];
    }

    // 渲染真实数据表格
    renderRealDataTable() {
        const tbody = document.getElementById('realDataTableBody');
        const pagination = document.getElementById('realDataPagination');
        
        if (!tbody || !pagination) return;

        // 使用固定数据源
        const sampleData = this.sampleRealData;
        const itemsPerPage = 10;
        const totalPages = Math.ceil(sampleData.length / itemsPerPage);
        let currentPage = 1;

        // 渲染表格数据（保持不变）
        const renderTable = (page) => {
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageData = sampleData.slice(startIndex, endIndex);

            tbody.innerHTML = pageData.map(record => `
                <tr>
                    <td>${record.id}</td>
                    <td>${record.sender}</td>
                    <td>${record.receiver}</td>
                    <td>¥${record.amount.toLocaleString()}</td>
                    <td>${record.region}</td>
                    <td>${record.date}</td>
                    <td><span class="badge bg-primary">${record.type}</span></td>
                    <td>${record.channel}</td>
                </tr>
            `).join('');
        };

        // 渲染分页
        const renderPagination = () => {
            let paginationHTML = '';
            
            // 上一页
            paginationHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="prev">上一页</a>
                </li>
            `;
            
            // 页码（保持不变）
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }
            
            // 下一页
            paginationHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="next">下一页</a>
                </li>
            `;
            
            pagination.innerHTML = paginationHTML;
            
            // 事件委托
            pagination.addEventListener('click', (e) => {
                if (e.target.tagName === 'A') {
                    e.preventDefault();
                    const action = e.target.getAttribute('data-page');
                    let newPage = currentPage;
                    
                    if (action === 'prev' && currentPage > 1) newPage = currentPage - 1;
                    else if (action === 'next' && currentPage < totalPages) newPage = currentPage + 1;
                    else if (!isNaN(parseInt(action))) newPage = parseInt(action);
                    
                    if (newPage !== currentPage) {
                        currentPage = newPage;
                        renderTable(currentPage);
                        renderPagination();
                    }
                }
            });
        };

        // 初始渲染
        renderTable(currentPage);
        renderPagination();
    }

    // 生成样本真实数据
    generateSampleRealData() {
        const senders = ['陈志明', '林建华', '王德胜', '张美玲', '李国强'];
        const receivers = ['陈志华', '林美玉', '王德福', '张美丽', '李国华'];
        const regions = ['福建泉州', '广东潮州', '浙江温州', '江苏苏州', '山东青岛'];
        const types = ['家庭汇款', '教育费用', '医疗费用'];
        const channels = ['侨批局', '银行', '邮局'];
        
        // 生成100条有意义的测试数据
        return Array.from({length: 100}, (_, i) => ({
            id: `RD${String(i+1).padStart(4, '0')}`,
            sender: senders[i % senders.length],
            receiver: receivers[i % receivers.length],
            amount: Math.floor(Math.random() * 5000) + 100, // 100-5100元
            region: regions[i % regions.length],
            date: new Date(2023, i % 12, (i % 28) + 1).toLocaleDateString('zh-CN'),
            type: types[i % types.length],
            channel: channels[i % channels.length]
        }));
    }

    // 渲染数据洞察
    renderDataInsights() {
        const container = document.querySelector('#real-data-section .row.mt-4');
        if (!container) return;

        // 检查是否已存在数据洞察
        if (document.getElementById('dataInsights')) return;

        const insightsHTML = `
            <div class="col-12 mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>数据洞察与分析
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="dataInsights">
                            <div class="col-md-6 mb-3">
                                <div class="insight-item">
                                    <h6><i class="fas fa-chart-line text-primary me-2"></i>趋势分析</h6>
                                    <ul class="list-unstyled">
                                        <li>• 1930年代是汇款高峰期，占总量的35%</li>
                                        <li>• 平均汇款金额呈上升趋势，从1920年的120元增长到1950年的280元</li>
                                        <li>• 家庭汇款占主导地位，占总量的55%</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="insight-item">
                                    <h6><i class="fas fa-map-marker-alt text-success me-2"></i>地理分布</h6>
                                    <ul class="list-unstyled">
                                        <li>• 福建和广东是主要接收地区，占总量的40%</li>
                                        <li>• 沿海地区接收汇款量明显高于内陆地区</li>
                                        <li>• 侨乡地区汇款密度最高，平均每户年接收3-5次</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="insight-item">
                                    <h6><i class="fas fa-users text-warning me-2"></i>社会网络</h6>
                                    <ul class="list-unstyled">
                                        <li>• 宗族网络是汇款传递的主要渠道</li>
                                        <li>• 侨批局在汇款网络中发挥关键作用</li>
                                        <li>• 信任关系是汇款成功的重要因素</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="insight-item">
                                    <h6><i class="fas fa-coins text-info me-2"></i>经济影响</h6>
                                    <ul class="list-unstyled">
                                        <li>• 汇款对当地经济发展有显著促进作用</li>
                                        <li>• 教育投资是汇款的重要用途之一</li>
                                        <li>• 汇款帮助缓解了农村地区的经济困难</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', insightsHTML);
    }

    // 导出真实数据
    exportRealData() {
        const sampleData = this.generateSampleRealData();
        const exportData = {
            metadata: {
                exportDate: new Date().toISOString(),
                totalRecords: sampleData.length,
                dataSource: '侨批历史档案',
                timeRange: '1920-1950'
            },
            statistics: this.calculateRealDataStats(),
            records: sampleData
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `qiaopi_real_data_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
        
        this.showNotification('真实数据已导出', 'success');
    }

    // 渲染仿真网格
    renderSimulationsGrid(simulations) {
        const grid = document.getElementById('simulationsGrid');
        if (!grid) return;

        grid.innerHTML = '';

        if (simulations.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无仿真结果</h5>
                    <p class="text-muted">请先运行仿真获取结果</p>
                </div>
            `;
            return;
        }

        simulations.forEach(simulation => {
            const card = this.createSimulationCard(simulation);
            grid.appendChild(card);
        });
    }

    // 创建仿真卡片
    createSimulationCard(simulation) {
        const card = document.createElement('div');
        card.className = 'col-md-6 col-lg-4 mb-4';
        
        const stats = simulation.statistics || {};
        const timeSpan = simulation.timeSpan || {};
        
        card.innerHTML = `
            <div class="simulation-card card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        ${simulation.name || simulation.id}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text small text-muted">${simulation.description || '仿真实验结果'}</p>
                    
                    <div class="simulation-meta mb-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="small text-muted">智能体</div>
                                <div class="fw-bold text-primary">${simulation.agentCount || 0}</div>
                            </div>
                            <div class="col-4">
                                <div class="small text-muted">汇款次数</div>
                                <div class="fw-bold text-success">${stats.total_remittances || 0}</div>
                            </div>
                            <div class="col-4">
                                <div class="small text-muted">成功率</div>
                                <div class="fw-bold text-info">${Math.round((stats.success_rate || 0) * 100)}%</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="simulation-timeline bg-light p-2 rounded mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            ${timeSpan.startYear || '未知'} - ${timeSpan.endYear || '未知'}
                            <br>
                            <i class="fas fa-clock me-1"></i>
                            ${this.formatDate(simulation.createdAt)}
                        </small>
                    </div>
                    
                    <div class="simulation-status">
                        <span class="badge bg-success"><i class="fas fa-check me-1"></i>已完成</span>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="window.uiManager.viewSimulationDetails('${simulation.id}')">
                            <i class="fas fa-eye me-1"></i> 查看详情
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.uiManager.exportSimulationResults('${simulation.id}')">
                            <i class="fas fa-download me-1"></i> 导出结果
                        </button>
                    </div>
                </div>
            </div>
        `;
        return card;
    }

    // 渲染场景网格
    renderScenariosGrid(scenarios) {
        const grid = document.getElementById('scenariosGrid');
        if (!grid) return;

        grid.innerHTML = '';

        scenarios.forEach(scenario => {
            const card = this.createScenarioCard(scenario);
            grid.appendChild(card);
        });
    }

    // 创建场景卡片
    createScenarioCard(scenario) {
        const card = document.createElement('div');
        card.className = 'col-md-6 col-lg-4 mb-4';
        
        // 复杂度颜色映射
        const complexityColors = {
            'low': 'success',
            'medium': 'warning', 
            'high': 'danger',
            'very_high': 'dark'
        };
        
        const complexityColor = complexityColors[scenario.complexity] || 'primary';
        
        // 生成参数列表
        let parametersHtml = '';
        if (scenario.parameters) {
            const paramEntries = Object.entries(scenario.parameters).slice(0, 3); // 只显示前3个参数
            parametersHtml = paramEntries.map(([key, value]) => {
                const label = this.getParameterLabel(key);
                return `<li class="small"><i class="fas fa-cog me-1"></i> ${label}: ${value}</li>`;
            }).join('');
        }
        
        card.innerHTML = `
            <div class="scenario-card card h-100">
                <div class="card-header bg-${complexityColor} text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-flask me-2"></i>${scenario.name}
                    </h6>
                </div>
                <div class="card-body">
                    <p class="scenario-description text-muted small">${scenario.description}</p>
                    
                    <div class="scenario-parameters bg-light p-2 rounded mb-3">
                        <h6 class="small mb-2">主要参数:</h6>
                        <ul class="list-unstyled mb-0">
                            ${parametersHtml}
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="badge bg-${complexityColor}">${this.getComplexityLabel(scenario.complexity)}</span>
                        <small class="text-muted">${Object.keys(scenario.parameters || {}).length} 参数</small>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="window.uiManager.runScenario('${scenario.id}')">
                            <i class="fas fa-play me-2"></i> 运行场景
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.uiManager.viewScenarioDetails('${scenario.id}')">
                            <i class="fas fa-info-circle me-2"></i> 查看详情
                        </button>
                    </div>
                </div>
            </div>
        `;
        return card;
    }

    // 获取参数标签
    getParameterLabel(key) {
        const labels = {
            'initial_migrants': '初始移民数',
            'shock_frequency': '冲击频率',
            'recovery_rate': '恢复率',
            'simulation_years': '仿真年数',
            'evolution_rate': '演化率',
            'adaptation_threshold': '适应阈值',
            'chain_strength': '链式强度',
            'migration_rate': '迁移率',
            'initial_trust': '初始信任',
            'trust_decay': '信任衰减',
            'reputation_weight': '声誉权重',
            'policy_strength': '政策强度',
            'compliance_rate': '合规率',
            'adaptation_time': '适应时间'
        };
        return labels[key] || key.replace('_', ' ');
    }

    // 获取复杂度标签
    getComplexityLabel(complexity) {
        const labels = {
            'low': '简单',
            'medium': '中等',
            'high': '复杂',
            'very_high': '极复杂'
        };
        return labels[complexity] || '未知';
    }

    // 查看场景详情
    viewScenarioDetails(scenarioId) {
        const scenario = this.scenarios.find(s => s.id === scenarioId);
        if (!scenario) return;

        const modalHTML = `
            <div class="modal fade" id="scenarioDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-flask me-2"></i>${scenario.name} - 详细信息
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>场景描述</h6>
                                    <p>${scenario.description}</p>
                                    
                                    <h6>复杂度</h6>
                                    <span class="badge bg-primary">${this.getComplexityLabel(scenario.complexity)}</span>
                                </div>
                                <div class="col-md-6">
                                    <h6>参数配置</h6>
                                    <table class="table table-sm">
                                        ${Object.entries(scenario.parameters || {}).map(([key, value]) => 
                                            `<tr><td>${this.getParameterLabel(key)}</td><td>${value}</td></tr>`
                                        ).join('')}
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="window.uiManager.runScenario('${scenario.id}')">
                                <i class="fas fa-play me-2"></i>运行此场景
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除现有模态框
        const existingModal = document.getElementById('scenarioDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHTML;
        document.body.appendChild(modalContainer);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('scenarioDetailsModal'));
        modal.show();
    }

    // 渲染我的智能体
    renderMyAgents() {
        const grid = document.getElementById('myAgentsGrid');
        if (!grid) return;

        const agents = this.getStoredAgents();
        
        if (agents.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">还没有创建智能体</h5>
                    <p class="text-muted">点击"创建新智能体"开始设计你的第一个AI智能体</p>
                    <a href="/agent-designer.html" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>创建智能体
                    </a>
                </div>
            `;
            return;
        }

        grid.innerHTML = '';
        agents.forEach(agent => {
            const card = this.createAgentCard(agent);
            grid.appendChild(card);
        });
    }

    // 创建智能体卡片
    createAgentCard(agent) {
        const card = document.createElement('div');
        card.className = 'col-md-6 col-lg-4 mb-3';
        
        card.innerHTML = `
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <i class="fas fa-robot fa-2x text-primary"></i>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="editAgent('${agent.id}')"><i class="fas fa-edit me-2"></i>编辑</a></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteAgent('${agent.id}')"><i class="fas fa-trash me-2"></i>删除</a></li>
                            </ul>
                        </div>
                    </div>
                    
                    <h6 class="card-title">${agent.name}</h6>
                    <p class="card-text small text-muted">${agent.description || '暂无描述'}</p>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary btn-sm w-100" onclick="runSimulationWithAgent('${agent.id}')">
                        <i class="fas fa-play me-1"></i>运行仿真
                    </button>
                </div>
            </div>
        `;
        
        return card;
    }

    // 获取存储的智能体
    getStoredAgents() {
        try {
            const agents = localStorage.getItem('qiaopi_agents');
            return agents ? JSON.parse(agents) : [];
        } catch (error) {
            console.error('Error loading agents from storage:', error);
            return [];
        }
    }

    // 更新最近仿真列表
    updateRecentSimulations() {
        const list = document.getElementById('recentSimulationsList');
        if (!list) return;

        const recent = this.simulations.slice(0, 5);
        list.innerHTML = '';

        recent.forEach(simulation => {
            const item = document.createElement('div');
            item.className = 'list-group-item';
            item.innerHTML = `
                <div class="simulation-info">
                    <span class="simulation-name">${simulation.name || simulation.id}</span>
                    <span class="simulation-date">${this.formatDate(simulation.createdAt)}</span>
                </div>
            `;
            item.addEventListener('click', () => this.viewSimulationDetails(simulation.id));
            list.appendChild(item);
        });
    }

    // 更新仿真选择器
    updateSimulationSelectors() {
        const select1 = document.getElementById('simulation1Select');
        const select2 = document.getElementById('simulation2Select');
        
        if (!select1 || !select2) return;

        // 清空选项
        select1.innerHTML = '<option value="">选择仿真1</option>';
        select2.innerHTML = '<option value="">选择仿真2</option>';

        // 添加选项
        this.simulations.forEach(simulation => {
            const option1 = document.createElement('option');
            option1.value = simulation.id;
            option1.textContent = simulation.name || simulation.id;
            select1.appendChild(option1);

            const option2 = document.createElement('option');
            option2.value = simulation.id;
            option2.textContent = simulation.name || simulation.id;
            select2.appendChild(option2);
        });
    }

    // 查看仿真详情
    async viewSimulationDetails(simulationId) {
        console.log('Viewing simulation details:', simulationId);
        
        const simulation = this.simulations.find(s => s.id === simulationId);
        if (!simulation) {
            this.showNotification('仿真不存在', 'error');
            return;
        }

        // 创建仿真详情模态框
        const modalHTML = `
            <div class="modal fade" id="simulationDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-chart-line me-2"></i>${simulation.name} - 详细信息
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>基本信息</h6>
                                    <table class="table table-sm">
                                        <tr><td>仿真ID</td><td>${simulation.id}</td></tr>
                                        <tr><td>创建时间</td><td>${this.formatDate(simulation.createdAt)}</td></tr>
                                        <tr><td>状态</td><td><span class="badge bg-success">已完成</span></td></tr>
                                        <tr><td>智能体数量</td><td>${simulation.agentCount || 'N/A'}</td></tr>
                                        <tr><td>时间跨度</td><td>${simulation.timeSpan ? `${simulation.timeSpan.startYear}-${simulation.timeSpan.endYear}` : 'N/A'}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>统计结果</h6>
                                    <table class="table table-sm">
                                        <tr><td>总汇款次数</td><td>${simulation.statistics?.total_remittances || 0}</td></tr>
                                        <tr><td>成功率</td><td>${Math.round((simulation.statistics?.success_rate || 0) * 100)}%</td></tr>
                                        <tr><td>总金额</td><td>${simulation.statistics?.total_amount || 0}</td></tr>
                                        <tr><td>平均金额</td><td>${simulation.statistics?.average_amount || 0}</td></tr>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6>时间序列图表</h6>
                                    <div class="chart-container" style="height: 400px;">
                                        <canvas id="simulationDetailChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="window.uiManager.exportSimulationResults('${simulationId}')">
                                <i class="fas fa-download me-2"></i>导出结果
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除现有模态框
        const existingModal = document.getElementById('simulationDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHTML;
        document.body.appendChild(modalContainer);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('simulationDetailsModal'));
        modal.show();

        // 创建图表
        setTimeout(() => {
            this.createSimulationDetailChart(simulation);
        }, 500);
    }

    // 创建仿真详情图表
    createSimulationDetailChart(simulation) {
        const canvas = document.getElementById('simulationDetailChart');
        if (!canvas) return;

        // 生成模拟时间序列数据
        const timeSeries = this.generateTimeSeriesData(simulation);
        
        const data = {
            labels: timeSeries.map(item => item.year),
            datasets: [
                {
                    label: '总汇款次数',
                    data: timeSeries.map(item => item.total_remittances),
                    borderColor: '#3498db',
                    backgroundColor: '#3498db20',
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: '成功率 (%)',
                    data: timeSeries.map(item => item.success_rate * 100),
                    borderColor: '#e74c3c',
                    backgroundColor: '#e74c3c20',
                    tension: 0.4,
                    yAxisID: 'y1'
                },
                {
                    label: '平均金额',
                    data: timeSeries.map(item => item.average_amount),
                    borderColor: '#2ecc71',
                    backgroundColor: '#2ecc7120',
                    tension: 0.4,
                    yAxisID: 'y2'
                }
            ]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '年份'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '汇款次数'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '成功率 (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    min: 0,
                    max: 100
                },
                y2: {
                    type: 'linear',
                    display: false,
                    position: 'right',
                    title: {
                        display: true,
                        text: '金额 (元)'
                    }
                }
            }
        };

        // 销毁已存在的图表
        if (window.Chart && window.Chart.getChart(canvas)) {
            window.Chart.getChart(canvas).destroy();
        }

        // 创建新图表
        if (window.Chart) {
            new Chart(canvas, {
                type: 'line',
                data: data,
                options: options
            });
        }
    }

    // 生成时间序列数据
    generateTimeSeriesData(simulation) {
        const timeSpan = simulation.timeSpan || { startYear: 1920, endYear: 1940 };
        const years = [];
        for (let year = timeSpan.startYear; year <= timeSpan.endYear; year += 2) {
            years.push(year);
        }

        return years.map(year => ({
            year: year,
            total_remittances: Math.floor(Math.random() * 100) + 50,
            success_rate: Math.random() * 0.3 + 0.7,
            average_amount: Math.floor(Math.random() * 200) + 100
        }));
    }

    // 显示仿真进度
    showSimulationProgress(agent) {
        const modalHTML = `
            <div class="modal fade" id="simulationProgressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cogs me-2"></i>仿真运行中
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="mb-3">
                                <h6>使用智能体: ${agent.name}</h6>
                                <p class="text-muted">${agent.description || '自定义智能体'}</p>
                            </div>
                            
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" id="simulationProgressBar" 
                                     style="width: 0%">0%</div>
                            </div>
                            
                            <div id="simulationStatus" class="text-muted">
                                正在初始化仿真环境...
                            </div>
                            
                            <div class="mt-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">运行中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除现有模态框
        const existingModal = document.getElementById('simulationProgressModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHTML;
        document.body.appendChild(modalContainer);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('simulationProgressModal'));
        modal.show();
    }

    // 运行智能体仿真
    async runAgentSimulation(agent) {
        const progressBar = document.getElementById('simulationProgressBar');
        const statusDiv = document.getElementById('simulationStatus');
        
        const steps = [
            { progress: 20, status: '正在初始化仿真环境...' },
            { progress: 40, status: '正在创建智能体实例...' },
            { progress: 60, status: '正在运行仿真逻辑...' },
            { progress: 80, status: '正在计算统计结果...' },
            { progress: 100, status: '仿真完成！' }
        ];

        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            
            // 更新进度条
            if (progressBar) {
                progressBar.style.width = step.progress + '%';
                progressBar.textContent = step.progress + '%';
            }
            
            // 更新状态
            if (statusDiv) {
                statusDiv.textContent = step.status;
            }
            
            // 等待一段时间模拟处理
            await new Promise(resolve => setTimeout(resolve, 800));
        }

        // 创建仿真结果
        const simulation = this.createAgentSimulationResult(agent);
        
        // 添加到仿真列表
        this.simulations.unshift(simulation);
        this.renderSimulationsGrid(this.simulations);
        
        // 关闭进度模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('simulationProgressModal'));
        if (modal) {
            modal.hide();
        }
        
        // 显示完成通知
        this.showNotification(`智能体 "${agent.name}" 的仿真已完成！`, 'success');
        
        // 自动跳转到仿真结果页面
        setTimeout(() => {
            this.showSection('simulations');
        }, 1000);
    }

    // 创建智能体仿真结果
    createAgentSimulationResult(agent) {
        const timeSpan = { startYear: 1920, endYear: 1940 };
        const agentCount = Math.floor(Math.random() * 50) + 20;
        
        // 根据智能体类型生成不同的结果
        let statistics = {};
        switch (agent.type) {
            case 'merchant':
                statistics = {
                    total_remittances: Math.floor(Math.random() * 200) + 100,
                    success_rate: Math.random() * 0.2 + 0.8,
                    total_amount: Math.floor(Math.random() * 10000) + 5000,
                    average_amount: Math.floor(Math.random() * 300) + 200
                };
                break;
            case 'worker':
                statistics = {
                    total_remittances: Math.floor(Math.random() * 150) + 80,
                    success_rate: Math.random() * 0.3 + 0.7,
                    total_amount: Math.floor(Math.random() * 8000) + 3000,
                    average_amount: Math.floor(Math.random() * 200) + 150
                };
                break;
            case 'student':
                statistics = {
                    total_remittances: Math.floor(Math.random() * 100) + 50,
                    success_rate: Math.random() * 0.4 + 0.6,
                    total_amount: Math.floor(Math.random() * 5000) + 2000,
                    average_amount: Math.floor(Math.random() * 150) + 100
                };
                break;
            default:
                statistics = {
                    total_remittances: Math.floor(Math.random() * 120) + 60,
                    success_rate: Math.random() * 0.3 + 0.7,
                    total_amount: Math.floor(Math.random() * 6000) + 3000,
                    average_amount: Math.floor(Math.random() * 180) + 120
                };
        }

        return {
            id: `simulation_${Date.now()}`,
            name: `${agent.name} 专属仿真`,
            agentTypes: [agent.type],
            createdAt: new Date().toISOString(),
            status: 'completed',
            customAgent: agent,
            agentCount: agentCount,
            timeSpan: timeSpan,
            statistics: statistics,
            description: `使用自定义智能体 "${agent.name}" 运行的仿真，模拟了 ${agentCount} 个智能体在 ${timeSpan.startYear}-${timeSpan.endYear} 年间的行为。`
        };
    }

    // 导出仿真结果
    exportSimulationResults(simulationId) {
        const simulation = this.simulations.find(s => s.id === simulationId);
        if (!simulation) {
            this.showNotification('仿真不存在', 'error');
            return;
        }

        // 准备导出数据
        const exportData = {
            simulation: {
                id: simulation.id,
                name: simulation.name,
                createdAt: simulation.createdAt,
                agentCount: simulation.agentCount,
                timeSpan: simulation.timeSpan,
                statistics: simulation.statistics
            },
            timeSeries: this.generateTimeSeriesData(simulation),
            exportedAt: new Date().toISOString()
        };

        // 创建下载链接
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `simulation_${simulation.id}_results.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
        
        this.showNotification('仿真结果已导出', 'success');
    }

    // 运行新仿真
    runNewSimulation() {
        console.log('Running new simulation');
        
        const modalHTML = `
            <div class="modal fade" id="newSimulationModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="fas fa-play me-2"></i>运行新仿真</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>创建仿真的方法</h6>
                                <ol>
                                    <li><strong>使用AI智能体</strong>: 先创建智能体，然后运行专属仿真</li>
                                    <li><strong>选择场景</strong>: 从预设场景中选择一个运行</li>
                                    <li><strong>快速演示</strong>: 运行演示仿真查看效果</li>
                                </ol>
                            </div>
                            
                            <div class="mb-3">
                                <label for="simulationName" class="form-label">仿真名称</label>
                                <input type="text" class="form-control" id="simulationName" value="快速演示仿真">
                            </div>
                            
                            <div class="mb-3">
                                <label for="simulationType" class="form-label">仿真类型</label>
                                <select class="form-select" id="simulationType">
                                    <option value="demo">演示仿真</option>
                                    <option value="network_analysis">网络分析</option>
                                    <option value="economic_impact">经济影响</option>
                                    <option value="social_dynamics">社会动力学</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="agentCount" class="form-label">智能体数量</label>
                                <input type="number" class="form-control" id="agentCount" value="200" min="50" max="1000">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-success" onclick="window.uiManager.createQuickSimulation()">
                                <i class="fas fa-rocket me-2"></i>快速运行
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.location.href='/agent-designer.html'">
                                <i class="fas fa-cog me-2"></i>设计智能体
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除现有模态框
        const existingModal = document.getElementById('newSimulationModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHTML;
        document.body.appendChild(modalContainer);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('newSimulationModal'));
        modal.show();
    }

    // 创建快速仿真
    createQuickSimulation() {
        const name = document.getElementById('simulationName').value;
        const type = document.getElementById('simulationType').value;
        const agentCount = document.getElementById('agentCount').value;
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('newSimulationModal'));
        modal.hide();
        
        this.showNotification('正在创建仿真...', 'info');
        
        // 模拟仿真创建过程
        setTimeout(() => {
            const newSimulation = {
                id: `simulation_${Date.now()}`,
                name: name,
                description: `${type}类型的快速演示仿真`,
                createdAt: new Date().toISOString(),
                status: 'completed',
                agentCount: parseInt(agentCount),
                timeSpan: { 
                    startYear: 1920, 
                    endYear: 1920 + Math.floor(Math.random() * 30) + 10 
                },
                statistics: {
                    total_remittances: Math.floor(Math.random() * 1000) + 500,
                    success_rate: Math.random() * 0.3 + 0.7,
                    total_amount: Math.floor(Math.random() * 100000) + 50000
                }
            };
            
            this.simulations.unshift(newSimulation);
            this.showNotification(`仿真 "${name}" 创建成功！`, 'success');
            this.showSection('simulations');
        }, 2000);
    }

    // 查看真实数据
    viewRealData() {
        this.showSection('real-data');
    }

    // 导出结果
    exportResults() {
        const exportData = {
            simulations: this.simulations,
            scenarios: this.scenarios,
            realData: this.realData,
            exportTime: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(exportData, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `qiaopi_simulation_data_${new Date().getTime()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('数据导出成功！', 'success');
    }

    // 导出仿真结果
    exportSimulationResults(simulationId) {
        const simulation = this.simulations.find(s => s.id === simulationId);
        if (!simulation) {
            this.showNotification('仿真结果不存在', 'error');
            return;
        }
        
        const dataStr = JSON.stringify(simulation, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `simulation_${simulation.name}_${new Date().getTime()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification(`仿真 "${simulation.name}" 结果已导出`, 'success');
    }

    // 对比仿真
    compareSimulations() {
        if (this.simulations.length < 2) {
            this.showNotification('需要至少2个仿真结果才能进行对比', 'warning');
            return;
        }
        
        this.showSection('comparison');
    }

    // 运行场景
    runScenario(scenarioId) {
        const scenario = this.scenarios.find(s => s.id === scenarioId);
        if (!scenario) {
            this.showNotification('场景不存在', 'error');
            return;
        }
        
        this.showNotification(`正在运行场景 "${scenario.name}"...`, 'info');
        
        // 模拟场景运行
        setTimeout(() => {
            const newSimulation = {
                id: `scenario_${scenarioId}_${Date.now()}`,
                name: `${scenario.name} - 仿真结果`,
                description: scenario.description,
                createdAt: new Date().toISOString(),
                status: 'completed',
                agentCount: scenario.parameters?.initial_migrants || 150,
                timeSpan: { 
                    startYear: 1920, 
                    endYear: 1920 + (scenario.parameters?.simulation_years || 20)
                },
                statistics: {
                    total_remittances: Math.floor(Math.random() * 1500) + 800,
                    success_rate: Math.random() * 0.3 + 0.7,
                    total_amount: Math.floor(Math.random() * 200000) + 100000
                },
                scenario: scenarioId
            };
            
            this.simulations.unshift(newSimulation);
            this.showNotification(`场景 "${scenario.name}" 运行完成！`, 'success');
            this.showSection('simulations');
        }, 3000);
    }

    // 格式化日期
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    }

    // 更新UI
    updateUI() {
        // 更新统计信息
        this.updateStatistics();
        
        // 更新图表 - 总是加载总览数据，因为这是默认页面
        this.loadOverviewData();
        
        // 如果当前在其他section，也加载相应数据
        if (this.currentSection !== 'overview') {
            this.loadSectionData(this.currentSection);
        }
    }

    // 更新统计信息
    updateStatistics() {
        // 更新页面上的统计数字
        const stats = {
            totalSimulations: this.simulations.length,
            totalScenarios: this.scenarios.length
        };

        // 更新DOM元素
        Object.entries(stats).forEach(([key, value]) => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = value;
            }
        });
    }

    // 显示通知消息
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 150);
            }
        }, 5000);
    }

    // 更新真实数据统计信息
    updateRealDataStats(data) {
        console.log('📊 更新真实数据统计...', data?.length || 0, '条记录');
        
        // 确保统计容器存在
        let statsContainer = document.getElementById('realDataStats');
        if (!statsContainer) {
            console.warn('统计容器不存在，创建基本统计显示');
            return;
        }

        // 创建或更新统计卡片
        statsContainer.innerHTML = `
            <div class="col-md-3">
                <div class="data-stat-item">
                    <div class="data-stat-label">总记录数</div>
                    <div class="data-stat-value">${(data?.length || 0).toLocaleString()}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="data-stat-item">
                    <div class="data-stat-label">平均金额</div>
                    <div class="data-stat-value">¥${this.calculateAverageAmount(data).toFixed(2)}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="data-stat-item">
                    <div class="data-stat-label">地区数量</div>
                    <div class="data-stat-value">${this.countUniqueRegions(data)}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="data-stat-item">
                    <div class="data-stat-label">时间跨度</div>
                    <div class="data-stat-value">${this.getTimeSpan(data)}</div>
                </div>
            </div>
        `;
    }

    // 辅助函数
    calculateAverageAmount(data) {
        if (!data || data.length === 0) return 0;
        const amounts = data.map(d => d.amount || 0).filter(a => a > 0);
        return amounts.length > 0 ? amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length : 0;
    }

    countUniqueRegions(data) {
        if (!data || data.length === 0) return 0;
        const regions = new Set(data.map(d => d.region || d.sender_location || '未知'));
        return regions.size;
    }

    getTimeSpan(data) {
        if (!data || data.length === 0) return 'N/A';
        const years = data.map(d => d.year || new Date(d.date || Date.now()).getFullYear()).filter(y => y);
        if (years.length === 0) return 'N/A';
        const minYear = Math.min(...years);
        const maxYear = Math.max(...years);
        return `${minYear}-${maxYear}`;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 创建全局UI管理器
    window.uiManager = new UIManager();
    
    // 初始化应用
    console.log('Qiaopi Simulation Web App initialized');
});

// 全局函数供HTML调用
function showSection(sectionName) {
    if (window.uiManager) {
        window.uiManager.showSection(sectionName);
    }
}

function loadSimulations() {
    if (window.uiManager) {
        window.uiManager.loadSimulationsData();
    }
}

function runNewSimulation() {
    if (window.uiManager) {
        window.uiManager.runNewSimulation();
    }
}

function viewRealData() {
    if (window.uiManager) {
        window.uiManager.viewRealData();
    }
}

function exportResults() {
    if (window.uiManager) {
        window.uiManager.exportResults();
    }
}

function compareSimulations() {
    if (window.uiManager) {
        window.uiManager.compareSimulations();
    }
}

function initializeApp() {
    console.log('Application initialized');
    // 初始化逻辑由UIManager处理
}

// AI智能体管理函数
function useTemplate(templateType) {
    window.location.href = `/agent-designer.html?template=${templateType}`;
}

function loadMyAgents() {
    if (window.uiManager) {
        window.uiManager.renderMyAgents();
    }
}

function editAgent(agentId) {
    window.location.href = `/agent-designer.html?edit=${agentId}`;
}

function deleteAgent(agentId) {
    if (confirm('确定要删除这个智能体吗？')) {
        let agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
        agents = agents.filter(a => a.id !== agentId);
        localStorage.setItem('qiaopi_agents', JSON.stringify(agents));
        
        if (window.uiManager) {
            window.uiManager.renderMyAgents();
            window.uiManager.showNotification('智能体已删除', 'info');
        }
    }
}

function runSimulationWithAgent(agentId) {
    const agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
    const agent = agents.find(a => a.id === agentId);
    
    if (!agent) {
        if (window.uiManager) {
            window.uiManager.showNotification('智能体不存在', 'error');
        }
        return;
    }

    if (window.uiManager) {
        window.uiManager.showNotification(`正在使用智能体 "${agent.name}" 运行仿真...`, 'info');
        
        // 显示仿真进度
        window.uiManager.showSimulationProgress(agent);
        
        // 运行实际仿真
        window.uiManager.runAgentSimulation(agent);
    }
}