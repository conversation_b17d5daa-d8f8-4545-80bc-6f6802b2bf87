#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Analytics and Machine Learning Module
高级分析与机器学习模块

Enterprise-grade analytics for Qiaopi simulation
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
import pickle

# Scientific computing
from scipy import stats, signal
from scipy.cluster import hierarchy
from scipy.spatial.distance import pdist, squareform

# Machine Learning
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA, NMF
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, IsolationForest
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import TimeSeriesSplit, cross_val_score

# Time series analysis
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, acf, pacf
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.holtwinters import ExponentialSmoothing

# Network analysis
import networkx as nx

# Deep learning (optional, for advanced features)
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

import warnings
warnings.filterwarnings('ignore')

import logging
logger = logging.getLogger(__name__)


@dataclass
class AnalyticsResult:
    """Container for analytics results"""
    timestamp: datetime
    analysis_type: str
    results: Dict[str, Any]
    metadata: Dict[str, Any]
    confidence_score: float = 0.0


class TimeSeriesAnalyzer:
    """
    Advanced time series analysis for simulation data
    仿真数据的高级时间序列分析
    """
    
    def __init__(self):
        self.models = {}
        self.forecasts = {}
        self.decompositions = {}
        
    def analyze_series(self, data: pd.Series, name: str) -> AnalyticsResult:
        """Comprehensive time series analysis"""
        results = {}
        
        # Basic statistics
        results['statistics'] = {
            'mean': float(data.mean()),
            'std': float(data.std()),
            'min': float(data.min()),
            'max': float(data.max()),
            'skewness': float(data.skew()),
            'kurtosis': float(data.kurtosis())
        }
        
        # Trend analysis
        results['trend'] = self._analyze_trend(data)
        
        # Stationarity test
        results['stationarity'] = self._test_stationarity(data)
        
        # Seasonality detection
        if len(data) > 24:  # Need enough data for seasonal decomposition
            results['seasonality'] = self._detect_seasonality(data)
        
        # Autocorrelation analysis
        results['autocorrelation'] = self._analyze_autocorrelation(data)
        
        # Change point detection
        results['change_points'] = self._detect_change_points(data)
        
        # Anomaly detection
        results['anomalies'] = self._detect_anomalies(data)
        
        return AnalyticsResult(
            timestamp=datetime.now(),
            analysis_type='time_series',
            results=results,
            metadata={'series_name': name, 'length': len(data)},
            confidence_score=self._calculate_confidence(results)
        )
    
    def _analyze_trend(self, data: pd.Series) -> Dict[str, Any]:
        """Analyze trend in time series"""
        x = np.arange(len(data))
        y = data.values
        
        # Linear regression for trend
        z = np.polyfit(x, y, 1)
        trend_slope = z[0]
        
        # Moving averages
        ma_short = data.rolling(window=min(10, len(data)//4)).mean()
        ma_long = data.rolling(window=min(30, len(data)//2)).mean()
        
        # Trend strength (R-squared of linear fit)
        _, _, r_value, _, _ = stats.linregress(x, y)
        trend_strength = r_value ** 2
        
        return {
            'direction': 'increasing' if trend_slope > 0 else 'decreasing',
            'slope': float(trend_slope),
            'strength': float(trend_strength),
            'ma_short': ma_short.tolist()[-10:] if len(ma_short) > 0 else [],
            'ma_long': ma_long.tolist()[-10:] if len(ma_long) > 0 else []
        }
    
    def _test_stationarity(self, data: pd.Series) -> Dict[str, Any]:
        """Test for stationarity using ADF test"""
        try:
            result = adfuller(data.dropna())
            return {
                'is_stationary': result[1] < 0.05,
                'adf_statistic': float(result[0]),
                'p_value': float(result[1]),
                'critical_values': {k: float(v) for k, v in result[4].items()}
            }
        except:
            return {'is_stationary': None, 'error': 'Insufficient data'}
    
    def _detect_seasonality(self, data: pd.Series) -> Dict[str, Any]:
        """Detect and analyze seasonality"""
        try:
            # Ensure we have a proper frequency
            if not isinstance(data.index, pd.DatetimeIndex):
                data.index = pd.date_range(start='2020', periods=len(data), freq='M')
            
            decomposition = seasonal_decompose(data, model='additive', period=12)
            
            seasonal_strength = np.std(decomposition.seasonal) / np.std(decomposition.resid)
            
            return {
                'has_seasonality': seasonal_strength > 0.5,
                'seasonal_strength': float(seasonal_strength),
                'period': 12,
                'seasonal_component': decomposition.seasonal.tolist()[-12:] if len(decomposition.seasonal) >= 12 else []
            }
        except:
            return {'has_seasonality': False, 'error': 'Could not decompose'}
    
    def _analyze_autocorrelation(self, data: pd.Series) -> Dict[str, Any]:
        """Analyze autocorrelation patterns"""
        try:
            acf_values = acf(data.dropna(), nlags=min(20, len(data)//4))
            pacf_values = pacf(data.dropna(), nlags=min(20, len(data)//4))
            
            # Find significant lags
            confidence_interval = 1.96 / np.sqrt(len(data))
            significant_lags = [i for i, v in enumerate(acf_values[1:], 1) 
                              if abs(v) > confidence_interval]
            
            return {
                'acf': acf_values.tolist(),
                'pacf': pacf_values.tolist(),
                'significant_lags': significant_lags[:5],
                'has_autocorrelation': len(significant_lags) > 0
            }
        except:
            return {'has_autocorrelation': False, 'error': 'Insufficient data'}
    
    def _detect_change_points(self, data: pd.Series) -> List[int]:
        """Detect significant change points in the series"""
        if len(data) < 10:
            return []
        
        # Simple method using rolling statistics
        window = min(10, len(data) // 5)
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        
        # Detect points where mean changes significantly
        mean_diff = rolling_mean.diff().abs()
        threshold = mean_diff.quantile(0.95)
        
        change_points = mean_diff[mean_diff > threshold].index.tolist()
        
        return [int(i) for i in range(len(data)) if i in change_points][:5]
    
    def _detect_anomalies(self, data: pd.Series) -> Dict[str, Any]:
        """Detect anomalies using multiple methods"""
        anomalies = []
        
        # Method 1: IQR
        Q1 = data.quantile(0.25)
        Q3 = data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        iqr_anomalies = data[(data < lower_bound) | (data > upper_bound)].index.tolist()
        
        # Method 2: Z-score
        z_scores = np.abs(stats.zscore(data.dropna()))
        z_anomalies = [i for i, z in enumerate(z_scores) if z > 3]
        
        # Method 3: Isolation Forest (if enough data)
        if len(data) > 50:
            try:
                iso_forest = IsolationForest(contamination=0.1, random_state=42)
                predictions = iso_forest.fit_predict(data.values.reshape(-1, 1))
                iso_anomalies = [i for i, p in enumerate(predictions) if p == -1]
                anomalies.extend(iso_anomalies)
            except:
                pass
        
        # Combine results
        anomalies.extend(iqr_anomalies)
        anomalies.extend(z_anomalies)
        anomaly_indices = list(set([int(a) for a in anomalies]))[:10]
        
        return {
            'anomaly_indices': anomaly_indices,
            'anomaly_count': len(anomaly_indices),
            'anomaly_rate': len(anomaly_indices) / len(data) if len(data) > 0 else 0
        }
    
    def _calculate_confidence(self, results: Dict) -> float:
        """Calculate confidence score for analysis"""
        confidence = 0.5  # Base confidence
        
        # Adjust based on data quality indicators
        if results.get('stationarity', {}).get('is_stationary'):
            confidence += 0.1
        
        if results.get('autocorrelation', {}).get('has_autocorrelation'):
            confidence += 0.1
        
        if results.get('anomalies', {}).get('anomaly_rate', 1) < 0.05:
            confidence += 0.1
        
        trend_strength = results.get('trend', {}).get('strength', 0)
        confidence += min(0.2, trend_strength * 0.2)
        
        return min(1.0, confidence)
    
    def forecast(self, data: pd.Series, horizon: int = 12) -> Dict[str, Any]:
        """Generate forecasts using multiple methods"""
        forecasts = {}
        
        # Method 1: ARIMA
        try:
            model = ARIMA(data, order=(1, 1, 1))
            fitted_model = model.fit()
            arima_forecast = fitted_model.forecast(steps=horizon)
            forecasts['arima'] = arima_forecast.tolist()
        except:
            forecasts['arima'] = None
        
        # Method 2: Exponential Smoothing
        try:
            model = ExponentialSmoothing(data, seasonal='add', seasonal_periods=12)
            fitted_model = model.fit()
            exp_forecast = fitted_model.forecast(steps=horizon)
            forecasts['exponential_smoothing'] = exp_forecast.tolist()
        except:
            forecasts['exponential_smoothing'] = None
        
        # Method 3: Simple linear extrapolation
        x = np.arange(len(data))
        y = data.values
        z = np.polyfit(x, y, 1)
        poly = np.poly1d(z)
        future_x = np.arange(len(data), len(data) + horizon)
        linear_forecast = poly(future_x)
        forecasts['linear'] = linear_forecast.tolist()
        
        # Calculate ensemble forecast
        valid_forecasts = [f for f in forecasts.values() if f is not None]
        if valid_forecasts:
            ensemble = np.mean(valid_forecasts, axis=0)
            forecasts['ensemble'] = ensemble.tolist()
        
        return forecasts


class NetworkAnalyzer:
    """
    Advanced network analysis for agent relationships
    智能体关系的高级网络分析
    """
    
    def __init__(self):
        self.graph = None
        self.metrics = {}
        
    def build_network(self, kinship_networks: Dict[str, List[str]], 
                     migrant_family_pairs: Dict[str, str]) -> nx.Graph:
        """Build NetworkX graph from simulation data"""
        G = nx.Graph()
        
        # Add nodes from kinship networks
        for network_id, agents in kinship_networks.items():
            for agent in agents:
                G.add_node(agent, network=network_id)
        
        # Add edges within kinship networks
        for network_id, agents in kinship_networks.items():
            for i in range(len(agents)):
                for j in range(i + 1, len(agents)):
                    G.add_edge(agents[i], agents[j], weight=1.0, type='kinship')
        
        # Add edges from migrant-family pairs
        for migrant, family in migrant_family_pairs.items():
            if G.has_node(migrant) and G.has_node(family):
                G.add_edge(migrant, family, weight=2.0, type='family')
        
        self.graph = G
        return G
    
    def analyze_network(self) -> AnalyticsResult:
        """Comprehensive network analysis"""
        if self.graph is None or len(self.graph) == 0:
            return AnalyticsResult(
                timestamp=datetime.now(),
                analysis_type='network',
                results={},
                metadata={'error': 'No network data'},
                confidence_score=0.0
            )
        
        results = {}
        
        # Basic metrics
        results['basic_metrics'] = {
            'num_nodes': self.graph.number_of_nodes(),
            'num_edges': self.graph.number_of_edges(),
            'density': nx.density(self.graph),
            'average_degree': sum(dict(self.graph.degree()).values()) / self.graph.number_of_nodes()
        }
        
        # Centrality measures
        results['centrality'] = self._calculate_centrality()
        
        # Community detection
        results['communities'] = self._detect_communities()
        
        # Network robustness
        results['robustness'] = self._analyze_robustness()
        
        # Small world properties
        results['small_world'] = self._analyze_small_world()
        
        # Key influencers
        results['influencers'] = self._identify_influencers()
        
        return AnalyticsResult(
            timestamp=datetime.now(),
            analysis_type='network',
            results=results,
            metadata={'graph_info': nx.info(self.graph)},
            confidence_score=0.85
        )
    
    def _calculate_centrality(self) -> Dict[str, Any]:
        """Calculate various centrality measures"""
        # Degree centrality
        degree_cent = nx.degree_centrality(self.graph)
        
        # Betweenness centrality (sample for large graphs)
        if self.graph.number_of_nodes() > 100:
            between_cent = nx.betweenness_centrality(self.graph, k=min(50, self.graph.number_of_nodes()))
        else:
            between_cent = nx.betweenness_centrality(self.graph)
        
        # Closeness centrality
        close_cent = nx.closeness_centrality(self.graph)
        
        # Eigenvector centrality
        try:
            eigen_cent = nx.eigenvector_centrality(self.graph, max_iter=100)
        except:
            eigen_cent = {}
        
        # Get top nodes for each centrality
        top_k = 5
        
        return {
            'top_degree': sorted(degree_cent.items(), key=lambda x: x[1], reverse=True)[:top_k],
            'top_betweenness': sorted(between_cent.items(), key=lambda x: x[1], reverse=True)[:top_k],
            'top_closeness': sorted(close_cent.items(), key=lambda x: x[1], reverse=True)[:top_k],
            'top_eigenvector': sorted(eigen_cent.items(), key=lambda x: x[1], reverse=True)[:top_k] if eigen_cent else [],
            'avg_degree_centrality': np.mean(list(degree_cent.values())),
            'avg_betweenness_centrality': np.mean(list(between_cent.values()))
        }
    
    def _detect_communities(self) -> Dict[str, Any]:
        """Detect communities in the network"""
        # Louvain community detection
        communities = list(nx.community.greedy_modularity_communities(self.graph))
        
        # Calculate modularity
        modularity = nx.community.modularity(self.graph, communities)
        
        # Community sizes
        community_sizes = [len(c) for c in communities]
        
        return {
            'num_communities': len(communities),
            'modularity': float(modularity),
            'community_sizes': community_sizes,
            'largest_community_size': max(community_sizes) if community_sizes else 0,
            'avg_community_size': np.mean(community_sizes) if community_sizes else 0
        }
    
    def _analyze_robustness(self) -> Dict[str, Any]:
        """Analyze network robustness"""
        # Connected components
        components = list(nx.connected_components(self.graph))
        
        # Largest component size
        largest_component_size = max(len(c) for c in components) if components else 0
        
        # Connectivity
        is_connected = nx.is_connected(self.graph)
        
        # Average clustering coefficient
        avg_clustering = nx.average_clustering(self.graph)
        
        return {
            'is_connected': is_connected,
            'num_components': len(components),
            'largest_component_ratio': largest_component_size / self.graph.number_of_nodes() if self.graph.number_of_nodes() > 0 else 0,
            'average_clustering': float(avg_clustering)
        }
    
    def _analyze_small_world(self) -> Dict[str, Any]:
        """Analyze small world properties"""
        try:
            # Average shortest path (for largest component if disconnected)
            if nx.is_connected(self.graph):
                avg_path_length = nx.average_shortest_path_length(self.graph)
            else:
                largest_cc = max(nx.connected_components(self.graph), key=len)
                subgraph = self.graph.subgraph(largest_cc)
                avg_path_length = nx.average_shortest_path_length(subgraph)
            
            # Clustering coefficient
            clustering_coef = nx.average_clustering(self.graph)
            
            # Random graph comparison
            n = self.graph.number_of_nodes()
            m = self.graph.number_of_edges()
            random_graph = nx.gnm_random_graph(n, m)
            
            random_path_length = nx.average_shortest_path_length(random_graph) if nx.is_connected(random_graph) else float('inf')
            random_clustering = nx.average_clustering(random_graph)
            
            # Small world coefficient
            if random_clustering > 0 and random_path_length > 0:
                small_world_coef = (clustering_coef / random_clustering) / (avg_path_length / random_path_length)
            else:
                small_world_coef = 0
            
            return {
                'avg_path_length': float(avg_path_length),
                'clustering_coefficient': float(clustering_coef),
                'small_world_coefficient': float(small_world_coef),
                'is_small_world': small_world_coef > 1
            }
        except:
            return {'error': 'Could not compute small world properties'}
    
    def _identify_influencers(self) -> List[Tuple[str, float]]:
        """Identify key influencers in the network"""
        # Combine multiple centrality measures
        degree_cent = nx.degree_centrality(self.graph)
        between_cent = nx.betweenness_centrality(self.graph)
        
        # Calculate influence score
        influence_scores = {}
        for node in self.graph.nodes():
            influence_scores[node] = (
                0.4 * degree_cent.get(node, 0) +
                0.6 * between_cent.get(node, 0)
            )
        
        # Return top influencers
        top_influencers = sorted(influence_scores.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return [(node[:8], score) for node, score in top_influencers]  # Truncate node IDs for display


class PredictiveModeling:
    """
    Machine Learning models for prediction and forecasting
    用于预测和预报的机器学习模型
    """
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        
    def train_remittance_predictor(self, historical_data: pd.DataFrame) -> Dict[str, Any]:
        """Train model to predict remittance success and amounts"""
        if len(historical_data) < 100:
            return {'error': 'Insufficient training data'}
        
        # Prepare features and target
        feature_cols = ['sender_income', 'sender_savings', 'family_size', 
                       'economic_status', 'years_abroad', 'social_capital']
        
        available_features = [col for col in feature_cols if col in historical_data.columns]
        
        if len(available_features) < 3:
            return {'error': 'Insufficient features'}
        
        X = historical_data[available_features].fillna(0)
        y = historical_data['remittance_amount'].fillna(0)
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers['remittance'] = scaler
        
        # Train multiple models
        models = {
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'gradient_boost': GradientBoostingRegressor(n_estimators=100, random_state=42)
        }
        
        results = {}
        
        for name, model in models.items():
            # Time series cross-validation
            tscv = TimeSeriesSplit(n_splits=3)
            scores = cross_val_score(model, X_scaled, y, cv=tscv, 
                                   scoring='neg_mean_squared_error')
            
            # Train on full data
            model.fit(X_scaled, y)
            self.models[f'remittance_{name}'] = model
            
            # Feature importance
            if hasattr(model, 'feature_importances_'):
                importance = dict(zip(available_features, model.feature_importances_))
                self.feature_importance[name] = importance
            
            results[name] = {
                'cv_score': -np.mean(scores),
                'std_score': np.std(scores),
                'feature_importance': self.feature_importance.get(name, {})
            }
        
        return results
    
    def predict_network_evolution(self, network_history: List[nx.Graph]) -> Dict[str, Any]:
        """Predict how the network will evolve"""
        if len(network_history) < 5:
            return {'error': 'Insufficient network history'}
        
        # Extract network features over time
        features_over_time = []
        
        for G in network_history:
            features = {
                'num_nodes': G.number_of_nodes(),
                'num_edges': G.number_of_edges(),
                'density': nx.density(G),
                'avg_clustering': nx.average_clustering(G),
                'num_components': nx.number_connected_components(G)
            }
            features_over_time.append(features)
        
        df = pd.DataFrame(features_over_time)
        
        # Forecast each metric
        predictions = {}
        
        for column in df.columns:
            series = df[column]
            
            # Simple linear projection
            x = np.arange(len(series))
            y = series.values
            z = np.polyfit(x, y, 2)  # Quadratic fit
            poly = np.poly1d(z)
            
            # Predict next 5 steps
            future_x = np.arange(len(series), len(series) + 5)
            forecast = poly(future_x)
            
            predictions[column] = {
                'current': float(series.iloc[-1]),
                'forecast': forecast.tolist(),
                'trend': 'increasing' if forecast[-1] > series.iloc[-1] else 'decreasing'
            }
        
        return predictions
    
    def anomaly_detection(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect anomalies in simulation data"""
        if len(data) < 50:
            return {'error': 'Insufficient data for anomaly detection'}
        
        # Prepare data
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        X = data[numeric_cols].fillna(0)
        
        # Scale data
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Isolation Forest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        predictions = iso_forest.fit_predict(X_scaled)
        
        # DBSCAN clustering for anomaly detection
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        clusters = dbscan.fit_predict(X_scaled)
        
        # Identify anomalies
        iso_anomalies = np.where(predictions == -1)[0]
        dbscan_anomalies = np.where(clusters == -1)[0]
        
        # Combine results
        all_anomalies = list(set(iso_anomalies) | set(dbscan_anomalies))
        
        return {
            'num_anomalies': len(all_anomalies),
            'anomaly_indices': all_anomalies[:20],  # Limit to 20 for display
            'anomaly_rate': len(all_anomalies) / len(data),
            'methods_agreement': len(set(iso_anomalies) & set(dbscan_anomalies)) / max(len(all_anomalies), 1)
        }


# Deep Learning models (optional, requires PyTorch)
if TORCH_AVAILABLE:
    class LSTMPredictor(nn.Module):
        """LSTM model for time series prediction"""
        
        def __init__(self, input_size, hidden_size=64, num_layers=2):
            super(LSTMPredictor, self).__init__()
            self.hidden_size = hidden_size
            self.num_layers = num_layers
            
            self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
            self.fc = nn.Linear(hidden_size, 1)
            
        def forward(self, x):
            h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
            c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
            
            out, _ = self.lstm(x, (h0, c0))
            out = self.fc(out[:, -1, :])
            return out


def create_analytics_dashboard_data(simulation_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process simulation data for dashboard visualization
    处理仿真数据用于仪表板可视化
    """
    
    # Initialize analyzers
    ts_analyzer = TimeSeriesAnalyzer()
    network_analyzer = NetworkAnalyzer()
    predictor = PredictiveModeling()
    
    dashboard_data = {
        'timestamp': datetime.now().isoformat(),
        'analyses': {}
    }
    
    # Time series analysis
    if 'time_series' in simulation_data:
        for series_name, series_data in simulation_data['time_series'].items():
            series = pd.Series(series_data)
            result = ts_analyzer.analyze_series(series, series_name)
            dashboard_data['analyses'][f'ts_{series_name}'] = result.results
    
    # Network analysis
    if 'kinship_networks' in simulation_data and 'migrant_family_pairs' in simulation_data:
        G = network_analyzer.build_network(
            simulation_data['kinship_networks'],
            simulation_data['migrant_family_pairs']
        )
        result = network_analyzer.analyze_network()
        dashboard_data['analyses']['network'] = result.results
    
    # Predictive modeling
    if 'historical_data' in simulation_data:
        df = pd.DataFrame(simulation_data['historical_data'])
        
        # Remittance prediction
        remittance_results = predictor.train_remittance_predictor(df)
        dashboard_data['analyses']['remittance_prediction'] = remittance_results
        
        # Anomaly detection
        anomaly_results = predictor.anomaly_detection(df)
        dashboard_data['analyses']['anomalies'] = anomaly_results
    
    return dashboard_data


if __name__ == "__main__":
    # Example usage
    print("Advanced Analytics Module Loaded")
    print(f"PyTorch available: {TORCH_AVAILABLE}")
    
    # Test with sample data
    sample_data = pd.Series(np.random.randn(100).cumsum())
    analyzer = TimeSeriesAnalyzer()
    result = analyzer.analyze_series(sample_data, "test_series")
    
    print(f"Analysis completed: {result.analysis_type}")
    print(f"Confidence score: {result.confidence_score:.2f}")
    print(f"Trend direction: {result.results['trend']['direction']}")