#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Visualization Dashboard for Qiaopi Network Simulation
高级侨批网络仿真可视化系统

Senior Data Engineer Implementation
"""

import dash
from dash import dcc, html, dash_table, Input, Output, State, callback_context
import dash_bootstrap_components as dbc
import dash_cytoscape as cyto
import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots

import pandas as pd
import numpy as np
import json
import redis
import threading
import queue
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulation_engine import QiaopiSimulationEngine, SimulationConfig
from agents import Location, QiaoxiangRegion, EconomicStatus, Occupation
from environment import Environment

# Configure structured logging
import structlog
logger = structlog.get_logger()

# Cytoscape layout for network visualization
cyto.load_extra_layouts()

class AdvancedVisualizationSystem:
    """
    Enterprise-grade visualization system for Qiaopi simulation
    企业级侨批仿真可视化系统
    """
    
    def __init__(self, simulation_engine: Optional[QiaopiSimulationEngine] = None):
        self.simulation = simulation_engine
        self.app = dash.Dash(
            __name__, 
            external_stylesheets=[
                dbc.themes.BOOTSTRAP,
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
            ],
            suppress_callback_exceptions=True
        )
        
        # Data storage
        self.time_series_data = []
        self.network_data = []
        self.agent_metrics = {}
        self.system_metrics = {}
        
        # Real-time queues
        self.data_queue = queue.Queue()
        self.event_queue = queue.Queue()
        
        # Redis connection for caching (optional)
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            self.redis_client.ping()
            self.use_redis = True
            logger.info("Redis connected for caching")
        except:
            self.use_redis = False
            logger.warning("Redis not available, using in-memory storage")
        
        # Initialize dashboard
        self._build_layout()
        self._register_callbacks()
        
        # Start background data processor
        self.processing_thread = threading.Thread(target=self._process_simulation_data, daemon=True)
        self.processing_thread.start()
    
    def _build_layout(self):
        """Build the advanced dashboard layout"""
        
        self.app.layout = dbc.Container([
            # Header
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.H1([
                            html.I(className="fas fa-network-wired me-3"),
                            "侨批网络智能仿真系统",
                            html.Small(" Qiaopi Network Simulation", className="text-muted ms-3")
                        ], className="display-4 mb-0"),
                        html.Hr(className="my-3"),
                        html.P("Advanced Real-time Monitoring & Analytics Dashboard", className="lead text-muted")
                    ])
                ], width=12)
            ], className="mb-4"),
            
            # Control Panel
            dbc.Row([
                dbc.Col([
                    self._create_control_panel()
                ], width=12)
            ], className="mb-4"),
            
            # Main Metrics Cards
            dbc.Row([
                dbc.Col(self._create_metric_card("simulation_time", "仿真时间", "fa-clock", "primary"), width=3),
                dbc.Col(self._create_metric_card("total_agents", "总智能体", "fa-users", "info"), width=3),
                dbc.Col(self._create_metric_card("total_remittance", "总汇款额", "fa-money-bill-wave", "success"), width=3),
                dbc.Col(self._create_metric_card("success_rate", "成功率", "fa-chart-line", "warning"), width=3),
            ], className="mb-4"),
            
            # Tab Navigation
            dbc.Tabs([
                dbc.Tab(self._create_overview_tab(), label="Overview 总览", tab_id="overview"),
                dbc.Tab(self._create_realtime_tab(), label="Real-time 实时监控", tab_id="realtime"),
                dbc.Tab(self._create_network_tab(), label="Network 网络分析", tab_id="network"),
                dbc.Tab(self._create_geographic_tab(), label="Geographic 地理分布", tab_id="geographic"),
                dbc.Tab(self._create_analytics_tab(), label="Analytics 深度分析", tab_id="analytics"),
                dbc.Tab(self._create_prediction_tab(), label="Prediction 预测建模", tab_id="prediction"),
                dbc.Tab(self._create_performance_tab(), label="Performance 性能监控", tab_id="performance"),
            ], id="main-tabs", active_tab="overview"),
            
            # Hidden divs for data storage
            html.Div(id="simulation-data", style={"display": "none"}),
            
            # Interval components for updates
            dcc.Interval(id="fast-interval", interval=1000, n_intervals=0),  # 1 second
            dcc.Interval(id="slow-interval", interval=5000, n_intervals=0),  # 5 seconds
            
        ], fluid=True, className="p-4")
    
    def _create_control_panel(self):
        """Create simulation control panel"""
        return dbc.Card([
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        dbc.ButtonGroup([
                            dbc.Button([html.I(className="fas fa-play me-2"), "Start"], 
                                      id="start-btn", color="success", size="lg"),
                            dbc.Button([html.I(className="fas fa-pause me-2"), "Pause"], 
                                      id="pause-btn", color="warning", size="lg"),
                            dbc.Button([html.I(className="fas fa-stop me-2"), "Stop"], 
                                      id="stop-btn", color="danger", size="lg"),
                            dbc.Button([html.I(className="fas fa-forward me-2"), "Speed"], 
                                      id="speed-btn", color="info", size="lg"),
                        ])
                    ], width=6),
                    dbc.Col([
                        dbc.Progress(id="simulation-progress", value=0, striped=True, animated=True, 
                                   className="mb-3", style={"height": "30px"}),
                    ], width=6),
                ]),
                dbc.Row([
                    dbc.Col([
                        html.Div([
                            html.Label("Simulation Speed", className="fw-bold"),
                            dcc.Slider(id="speed-slider", min=0.1, max=10, step=0.1, value=1,
                                     marks={0.1: '0.1x', 1: '1x', 5: '5x', 10: '10x'},
                                     tooltip={"placement": "bottom", "always_visible": True})
                        ])
                    ], width=6),
                    dbc.Col([
                        html.Div([
                            html.Label("Data Granularity", className="fw-bold"),
                            dcc.Dropdown(
                                id="granularity-dropdown",
                                options=[
                                    {"label": "Every Step", "value": "step"},
                                    {"label": "Daily", "value": "daily"},
                                    {"label": "Monthly", "value": "monthly"},
                                    {"label": "Yearly", "value": "yearly"}
                                ],
                                value="monthly",
                                clearable=False
                            )
                        ])
                    ], width=6),
                ], className="mt-3"),
            ])
        ], className="shadow-sm")
    
    def _create_metric_card(self, id_suffix, title, icon, color):
        """Create a metric display card"""
        return dbc.Card([
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.I(className=f"fas {icon} fa-2x text-{color}")
                    ], width=3),
                    dbc.Col([
                        html.H6(title, className="text-muted mb-1"),
                        html.H3(id=f"metric-{id_suffix}", children="0", className="mb-0"),
                        html.Small(id=f"metric-change-{id_suffix}", children="", 
                                 className="text-success")
                    ], width=9),
                ])
            ])
        ], className="shadow-sm h-100")
    
    def _create_overview_tab(self):
        """Create overview dashboard tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="time-series-overview", style={"height": "400px"})
                ], width=12)
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="agent-distribution", style={"height": "350px"})
                ], width=6),
                dbc.Col([
                    dcc.Graph(id="economic-status", style={"height": "350px"})
                ], width=6),
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="remittance-flow", style={"height": "400px"})
                ], width=12)
            ])
        ], fluid=True, className="mt-4")
    
    def _create_realtime_tab(self):
        """Create real-time monitoring tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.H5("Real-time Event Stream", className="mb-3"),
                        html.Div(id="event-stream", style={
                            "height": "200px",
                            "overflowY": "scroll",
                            "backgroundColor": "#f8f9fa",
                            "padding": "10px",
                            "borderRadius": "5px"
                        })
                    ])
                ], width=12)
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="realtime-metrics", style={"height": "350px"})
                ], width=6),
                dbc.Col([
                    dcc.Graph(id="realtime-heatmap", style={"height": "350px"})
                ], width=6),
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="agent-activity", style={"height": "300px"})
                ], width=12)
            ])
        ], fluid=True, className="mt-4")
    
    def _create_network_tab(self):
        """Create network analysis tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H5("Kinship Network Visualization", className="mb-3"),
                    cyto.Cytoscape(
                        id='network-graph',
                        layout={'name': 'cose-bilkent'},
                        style={'width': '100%', 'height': '600px'},
                        elements=[],
                        stylesheet=[
                            {
                                'selector': 'node',
                                'style': {
                                    'label': 'data(label)',
                                    'background-color': 'data(color)',
                                    'width': 'data(size)',
                                    'height': 'data(size)'
                                }
                            },
                            {
                                'selector': 'edge',
                                'style': {
                                    'line-color': '#ccc',
                                    'target-arrow-color': '#ccc',
                                    'target-arrow-shape': 'triangle',
                                    'curve-style': 'bezier',
                                    'width': 'data(weight)'
                                }
                            }
                        ]
                    )
                ], width=8),
                dbc.Col([
                    html.H5("Network Metrics", className="mb-3"),
                    html.Div(id="network-metrics", children=[
                        dbc.ListGroup([
                            dbc.ListGroupItem([
                                html.Strong("Total Networks: "),
                                html.Span(id="total-networks", children="0")
                            ]),
                            dbc.ListGroupItem([
                                html.Strong("Avg Network Size: "),
                                html.Span(id="avg-network-size", children="0")
                            ]),
                            dbc.ListGroupItem([
                                html.Strong("Network Density: "),
                                html.Span(id="network-density", children="0")
                            ]),
                            dbc.ListGroupItem([
                                html.Strong("Clustering Coefficient: "),
                                html.Span(id="clustering-coeff", children="0")
                            ]),
                        ])
                    ]),
                    html.Hr(),
                    dcc.Graph(id="degree-distribution", style={"height": "250px"})
                ], width=4),
            ])
        ], fluid=True, className="mt-4")
    
    def _create_geographic_tab(self):
        """Create geographic visualization tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H5("Migration Patterns Map", className="mb-3"),
                    dcc.Graph(id="migration-map", style={"height": "500px"})
                ], width=12)
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="location-sankey", style={"height": "400px"})
                ], width=6),
                dbc.Col([
                    dcc.Graph(id="regional-comparison", style={"height": "400px"})
                ], width=6),
            ])
        ], fluid=True, className="mt-4")
    
    def _create_analytics_tab(self):
        """Create advanced analytics tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H5("Statistical Analysis", className="mb-3"),
                    dcc.Graph(id="correlation-matrix", style={"height": "500px"})
                ], width=6),
                dbc.Col([
                    html.H5("Time Series Decomposition", className="mb-3"),
                    dcc.Graph(id="time-decomposition", style={"height": "500px"})
                ], width=6),
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    html.H5("Agent Behavior Clustering", className="mb-3"),
                    dcc.Graph(id="behavior-clustering", style={"height": "400px"})
                ], width=12)
            ])
        ], fluid=True, className="mt-4")
    
    def _create_prediction_tab(self):
        """Create prediction and forecasting tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H5("Remittance Forecasting", className="mb-3"),
                    dcc.Graph(id="forecast-chart", style={"height": "400px"})
                ], width=12)
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    html.H5("Prediction Model Performance", className="mb-3"),
                    dcc.Graph(id="model-performance", style={"height": "350px"})
                ], width=6),
                dbc.Col([
                    html.H5("Feature Importance", className="mb-3"),
                    dcc.Graph(id="feature-importance", style={"height": "350px"})
                ], width=6),
            ])
        ], fluid=True, className="mt-4")
    
    def _create_performance_tab(self):
        """Create system performance monitoring tab"""
        return dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H5("System Performance Metrics", className="mb-3"),
                    dcc.Graph(id="cpu-memory-chart", style={"height": "300px"})
                ], width=6),
                dbc.Col([
                    html.H5("Simulation Performance", className="mb-3"),
                    dcc.Graph(id="sim-performance", style={"height": "300px"})
                ], width=6),
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    html.H5("Data Pipeline Metrics", className="mb-3"),
                    dash_table.DataTable(
                        id='pipeline-metrics-table',
                        columns=[
                            {"name": "Metric", "id": "metric"},
                            {"name": "Value", "id": "value"},
                            {"name": "Status", "id": "status"},
                        ],
                        data=[],
                        style_cell={'textAlign': 'left'},
                        style_data_conditional=[
                            {
                                'if': {'column_id': 'status', 'filter_query': '{status} = "Good"'},
                                'backgroundColor': '#d4edda',
                                'color': 'black',
                            },
                            {
                                'if': {'column_id': 'status', 'filter_query': '{status} = "Warning"'},
                                'backgroundColor': '#fff3cd',
                                'color': 'black',
                            },
                            {
                                'if': {'column_id': 'status', 'filter_query': '{status} = "Critical"'},
                                'backgroundColor': '#f8d7da',
                                'color': 'black',
                            }
                        ]
                    )
                ], width=12)
            ])
        ], fluid=True, className="mt-4")
    
    def _register_callbacks(self):
        """Register all dashboard callbacks"""
        
        @self.app.callback(
            [Output("metric-simulation_time", "children"),
             Output("metric-total_agents", "children"),
             Output("metric-total_remittance", "children"),
             Output("metric-success_rate", "children")],
            [Input("fast-interval", "n_intervals")]
        )
        def update_metrics(n):
            """Update main metrics cards"""
            if self.simulation:
                current_year = self.simulation.environment.get_current_year()
                total_agents = len(self.simulation.migrants) + len(self.simulation.families) + len(self.simulation.institutions)
                
                # Get remittance stats
                stats = self.simulation.qiaopi_protocol.get_statistics()
                total_amount = stats.get('total_amount_remitted', 0)
                success_rate = stats.get('success_rate', 0)
                
                return (
                    f"{current_year:.1f}",
                    f"{total_agents:,}",
                    f"${total_amount:,.0f}",
                    f"{success_rate:.1%}"
                )
            return "N/A", "0", "$0", "0%"
        
        @self.app.callback(
            Output("time-series-overview", "figure"),
            [Input("slow-interval", "n_intervals")]
        )
        def update_time_series(n):
            """Update time series overview chart"""
            if not self.time_series_data:
                return go.Figure()
            
            df = pd.DataFrame(self.time_series_data)
            
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=("Remittance Volume Over Time", "Success Rate Trend"),
                vertical_spacing=0.15
            )
            
            # Remittance volume
            fig.add_trace(
                go.Scatter(x=df['timestamp'], y=df.get('remittance_volume', []),
                          mode='lines+markers', name='Volume',
                          line=dict(color='#1f77b4', width=2)),
                row=1, col=1
            )
            
            # Success rate
            fig.add_trace(
                go.Scatter(x=df['timestamp'], y=df.get('success_rate', []),
                          mode='lines', name='Success Rate',
                          line=dict(color='#2ca02c', width=2)),
                row=2, col=1
            )
            
            fig.update_layout(
                height=400,
                showlegend=True,
                title_text="Simulation Overview",
                hovermode='x unified'
            )
            
            return fig
        
        @self.app.callback(
            Output("network-graph", "elements"),
            [Input("slow-interval", "n_intervals")]
        )
        def update_network_graph(n):
            """Update network visualization"""
            if not self.simulation:
                return []
            
            elements = []
            
            # Sample networks for visualization (limit for performance)
            sample_size = min(50, len(self.simulation.kinship_networks))
            sampled_networks = dict(list(self.simulation.kinship_networks.items())[:sample_size])
            
            # Create nodes
            for network_id, agent_ids in sampled_networks.items():
                for agent_id in agent_ids[:10]:  # Limit agents per network
                    node_color = '#ff9999' if agent_id in self.simulation.migrants else '#9999ff'
                    elements.append({
                        'data': {
                            'id': agent_id[:8],
                            'label': agent_id[:4],
                            'color': node_color,
                            'size': 30
                        }
                    })
            
            # Create edges (simplified)
            for network_id, agent_ids in sampled_networks.items():
                if len(agent_ids) > 1:
                    for i in range(len(agent_ids) - 1):
                        elements.append({
                            'data': {
                                'source': agent_ids[i][:8],
                                'target': agent_ids[i+1][:8],
                                'weight': 1
                            }
                        })
            
            return elements
        
        @self.app.callback(
            Output("migration-map", "figure"),
            [Input("slow-interval", "n_intervals")]
        )
        def update_migration_map(n):
            """Update geographic migration patterns"""
            if not self.simulation:
                return go.Figure()
            
            # Count migrants by location
            location_counts = {}
            for migrant in self.simulation.migrants.values():
                loc = migrant.location.value
                location_counts[loc] = location_counts.get(loc, 0) + 1
            
            # Geographic coordinates (simplified)
            coordinates = {
                "新加坡": (1.3521, 103.8198),
                "马来亚": (4.2105, 101.9758),
                "泰国": (15.8700, 100.9925),
                "印尼": (-6.2088, 106.8456),
                "菲律宾": (14.5995, 120.9842),
                "香港": (22.3193, 114.1694)
            }
            
            fig = go.Figure()
            
            for location, count in location_counts.items():
                if location in coordinates:
                    lat, lon = coordinates[location]
                    fig.add_trace(go.Scattermapbox(
                        mode='markers+text',
                        lon=[lon],
                        lat=[lat],
                        marker={'size': min(30, 10 + count/10), 'color': 'red'},
                        text=f"{location}: {count}",
                        textposition='top right'
                    ))
            
            fig.update_layout(
                mapbox_style="open-street-map",
                mapbox=dict(center=dict(lat=10, lon=110), zoom=3),
                showlegend=False,
                height=500,
                title="Migrant Distribution Map"
            )
            
            return fig
        
        # More callbacks would be implemented here for other visualizations...
    
    def _process_simulation_data(self):
        """Background thread to process simulation data"""
        while True:
            try:
                if self.simulation and self.simulation.is_running:
                    # Collect current statistics
                    stats = self.simulation._calculate_current_statistics()
                    
                    # Add to time series
                    self.time_series_data.append({
                        'timestamp': datetime.now(),
                        'simulation_year': stats.get('current_year', 0),
                        'remittance_volume': stats.get('total_migrant_savings', 0),
                        'success_rate': stats.get('qiaopi_statistics', {}).get('success_rate', 0),
                        'total_agents': len(self.simulation.migrants) + len(self.simulation.families)
                    })
                    
                    # Keep only recent data (last 1000 points)
                    if len(self.time_series_data) > 1000:
                        self.time_series_data = self.time_series_data[-1000:]
                    
                    # Cache in Redis if available
                    if self.use_redis:
                        self.redis_client.set('latest_stats', json.dumps(stats, default=str))
                
            except Exception as e:
                logger.error(f"Error processing simulation data: {e}")
            
            threading.Event().wait(1)  # Sleep for 1 second
    
    def run(self, debug=True, port=8050):
        """Run the dashboard application"""
        logger.info(f"Starting dashboard on port {port}")
        self.app.run(debug=debug, port=port, host='0.0.0.0')


def create_dashboard_with_simulation():
    """Create dashboard with integrated simulation"""
    
    # Create simulation configuration
    config = SimulationConfig(
        start_year=1920,
        end_year=1950,
        steps_per_year=12,
        num_migrants=200,
        num_families=200,
        num_institutions=10,
        output_directory="visualization_output"
    )
    
    # Create simulation engine
    simulation = QiaopiSimulationEngine(config)
    
    # Create visualization system
    viz = AdvancedVisualizationSystem(simulation)
    
    return viz


if __name__ == "__main__":
    # Create and run the advanced dashboard
    dashboard = create_dashboard_with_simulation()
    dashboard.run(debug=True, port=8050)