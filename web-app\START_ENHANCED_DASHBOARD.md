# 🚀 增强版Dashboard使用指南

## 立即启动

### 1. 启动Web服务器
```bash
cd web-app
node server.js
```

### 2. 访问增强仿真页面
🌐 **直接访问**: http://localhost:3508/enhanced-simulation.html

或者

📊 **从主Dashboard**: http://localhost:3508 → 点击 "🔥增强仿真🔥" 按钮

## 🎯 新增功能

### Dashboard集成的增强功能

#### 1. **多时间序列可视化**
- 📈 **6个维度选择**: 整体、地区、货币、机构、事件、通道  
- 🔄 **动态切换**: 实时切换不同维度和指标
- 📊 **交互式图表**: Chart.js支持的专业图表

#### 2. **一键启动增强仿真**
- ⚙️ **配置界面**: 直观的参数设置
- 🚀 **后台运行**: 异步处理不阻塞页面
- 📊 **实时状态**: 运行进度和状态显示

#### 3. **高级分析模块展示**
- 🧠 **8个分析模块**: 趋势、聚类、预测、网络等
- ✅ **完成状态**: 每个模块的执行状态显示
- 📋 **详细结果**: 点击查看具体分析结果

#### 4. **API接口**
```javascript
// 启动增强仿真
POST /api/enhanced/run
{
  "config": {
    "startYear": 1920,
    "endYear": 1940,
    "migrants": 300,
    "families": 300,
    "institutions": 10
  }
}

// 获取结果
GET /api/enhanced/results

// 获取时间序列
GET /api/enhanced/timeseries?dimension=by_region

// 获取可视化数据
GET /api/enhanced/visualization
```

## 📊 使用步骤

### 步骤1: 启动Dashboard
```bash
cd web-app
node server.js
```

### 步骤2: 访问增强仿真
访问: http://localhost:3508/enhanced-simulation.html

### 步骤3: 配置参数
- 🗓️ **时间范围**: 设置仿真的开始和结束年份
- 👥 **智能体数量**: 配置移民、家庭、机构数量
- 🔧 **分析模块**: 选择启用的分析功能

### 步骤4: 启动仿真
点击 "🚀启动增强仿真" 按钮

### 步骤5: 查看结果
- 📈 **实时状态**: 查看仿真运行进度
- 📊 **多维图表**: 切换不同维度的时间序列
- 🧠 **高级分析**: 查看机器学习和统计分析结果

## 🎮 功能演示

### 多时间序列选择器
```
选择维度: [整体趋势 ▼]
子系列:   [新加坡 ▼] 
指标:     [汇款金额 ▼]
```
→ 自动生成对应的时间序列图表

### 分析模块状态表
| 模块 | 状态 | 指标 | 摘要 |
|------|------|------|------|
| 🔗 网络拓扑分析 | ✅完成 | 密度78% | 识别核心节点和社群 |
| 🧠 聚类分析 | ✅完成 | 5个群体 | 行为模式分类完成 |
| 📈 趋势分析 | ✅完成 | 7个趋势 | 上升/下降/稳定检测 |

## 🔧 API集成

Dashboard已集成以下API接口：

1. **`POST /api/enhanced/run`** - 启动增强仿真
2. **`GET /api/enhanced/results`** - 获取完整结果
3. **`GET /api/enhanced/timeseries`** - 获取多时间序列数据
4. **`GET /api/enhanced/visualization`** - 获取可视化数据

## 📈 结果文件

增强仿真将生成以下文件：
```
web-app/enhanced_results/
├── enhanced_analysis.json      # 完整增强分析
├── multi_timeseries.json     # 多维度时间序列
├── visualization_data.json   # 图表可视化数据
├── web_summary.json         # Web摘要
├── final_report.json        # 基础报告
├── migrants.csv             # 移民数据
└── qiaopi.csv              # 侨批数据
```

## 🎯 与原系统对比

| 特性 | 原Dashboard | 增强Dashboard |
|------|------------|---------------|
| 时间序列数量 | 1条 | **50+条** |
| 分析维度 | 1个 | **6个** |
| 图表类型 | 基础图表 | **多维度交互图表** |
| 分析深度 | 简单统计 | **ML+预测分析** |
| 操作方式 | 查看静态结果 | **动态配置+运行** |

## 💡 使用技巧

1. **首次使用**: 使用默认配置快速体验
2. **大规模分析**: 适当增加智能体数量
3. **快速测试**: 缩短时间范围(如1920-1925)
4. **深度分析**: 启用所有分析模块
5. **性能优化**: 移民数量控制在500以内

## 🌟 立即体验

**一键启动命令**:
```bash
cd web-app && node server.js
```

然后访问: **http://localhost:3508/enhanced-simulation.html**

现在你的Dashboard已经完全集成了增强版仿真功能！🎉