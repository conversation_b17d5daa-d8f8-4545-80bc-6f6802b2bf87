<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语法测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 AI智能体系统语法测试</h1>
    
    <div id="testResults">
        <div class="test-result info">正在测试JavaScript语法...</div>
    </div>

    <script>
        function runTests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '';

            // 测试1: 基本函数定义
            try {
                function testFunction() { return true; }
                results.innerHTML += '<div class="test-result success">✅ 基本函数定义 - 通过</div>';
            } catch (e) {
                results.innerHTML += `<div class="test-result error">❌ 基本函数定义 - 失败: ${e.message}</div>`;
            }

            // 测试2: 全局函数
            try {
                window.showSection = function(section) { 
                    console.log('showSection called:', section); 
                    return true;
                };
                window.loadSimulations = function() { 
                    console.log('loadSimulations called'); 
                    return true;
                };
                window.runNewSimulation = function() { 
                    console.log('runNewSimulation called'); 
                    return true;
                };
                window.viewRealData = function() { 
                    console.log('viewRealData called'); 
                    return true;
                };
                window.exportResults = function() { 
                    console.log('exportResults called'); 
                    return true;
                };
                window.compareSimulations = function() { 
                    console.log('compareSimulations called'); 
                    return true;
                };
                window.initializeApp = function() { 
                    console.log('initializeApp called'); 
                    return true;
                };

                results.innerHTML += '<div class="test-result success">✅ 全局函数定义 - 通过</div>';
            } catch (e) {
                results.innerHTML += `<div class="test-result error">❌ 全局函数定义 - 失败: ${e.message}</div>`;
            }

            // 测试3: 尝试调用函数
            try {
                showSection('overview');
                loadSimulations();
                runNewSimulation();
                viewRealData();
                exportResults();
                compareSimulations();
                initializeApp();
                results.innerHTML += '<div class="test-result success">✅ 函数调用 - 通过</div>';
            } catch (e) {
                results.innerHTML += `<div class="test-result error">❌ 函数调用 - 失败: ${e.message}</div>`;
            }

            // 测试4: UI Manager
            try {
                class TestUIManager {
                    constructor() {
                        this.test = true;
                    }
                    showSection(section) {
                        console.log('TestUIManager.showSection:', section);
                    }
                }
                window.uiManager = new TestUIManager();
                results.innerHTML += '<div class="test-result success">✅ UI Manager类 - 通过</div>';
            } catch (e) {
                results.innerHTML += `<div class="test-result error">❌ UI Manager类 - 失败: ${e.message}</div>`;
            }

            results.innerHTML += '<div class="test-result info">🎉 测试完成！如果所有测试都通过，说明JavaScript语法没有问题。</div>';
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>