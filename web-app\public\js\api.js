// API 接口封装
class QiaopiAPI {
    constructor() {
        this.baseURL = '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...(options.headers || {}) },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // GET 请求
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                url.searchParams.append(key, params[key]);
            }
        });

        const response = await fetch(url, {
            method: 'GET',
            headers: this.defaultHeaders
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    // POST 请求
    async post(endpoint, data = {}) {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method: 'POST',
            headers: this.defaultHeaders,
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    // 健康检查
    async healthCheck() {
        return this.get('/health');
    }

    // 获取所有仿真
    async getSimulations() {
        return this.get('/simulations');
    }

    // 获取特定仿真
    async getSimulation(id) {
        return this.get(`/simulations/${id}`);
    }

    // 获取仿真统计
    async getSimulationStatistics(id) {
        return this.get(`/simulations/${id}/statistics`);
    }

    // 获取时间序列数据
    async getTimeSeriesData(id) {
        return this.get(`/simulations/${id}/timeseries`);
    }

    // ===== 高级分析 API =====
    // 列出可用的final_report来源
    async listAdvancedReports() {
        return this.get('/advanced/list');
    }

    // 获取最新final_report（包含advanced_analysis）
    async getLatestAdvancedReport() {
        return this.get('/advanced/latest');
    }

    // 获取指定仿真ID的final_report
    async getAdvancedReportById(id) {
        return this.get(`/advanced/${id}`);
    }

    // 获取真实数据
    async getRealData() {
        return this.get('/real-data');
    }

    // 获取场景配置
    async getScenarios() {
        return this.get('/scenarios');
    }

    // 对比仿真
    async compareSimulations(simulationIds) {
        return this.post('/simulations/compare', { simulationIds });
    }

    // 上传仿真结果
    async uploadSimulation(formData) {
        const response = await fetch(`${this.baseURL}/simulations/upload`, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }
}

// 创建全局 API 实例
const api = new QiaopiAPI();

// 数据处理工具
class DataProcessor {
    // 格式化数字
    static formatNumber(num, decimals = 2) {
        if (num === null || num === undefined) return 'N/A';
        return new Intl.NumberFormat('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(num);
    }

    // 格式化百分比
    static formatPercentage(num, decimals = 1) {
        if (num === null || num === undefined) return 'N/A';
        return `${(num * 100).toFixed(decimals)}%`;
    }

    // 格式化日期
    static formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    }

    // 格式化时间跨度
    static formatTimeSpan(startYear, endYear) {
        if (!startYear || !endYear) return 'N/A';
        const span = endYear - startYear;
        return `${startYear} - ${endYear} (${span}年)`;
    }

    // 计算统计信息
    static calculateStatistics(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return {
                count: 0,
                mean: 0,
                median: 0,
                min: 0,
                max: 0,
                std: 0
            };
        }

        const sorted = [...data].sort((a, b) => a - b);
        const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
        const median = sorted[Math.floor(sorted.length / 2)];
        const min = sorted[0];
        const max = sorted[sorted.length - 1];
        
        const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
        const std = Math.sqrt(variance);

        return {
            count: data.length,
            mean: Number(mean.toFixed(2)),
            median: Number(median.toFixed(2)),
            min: Number(min.toFixed(2)),
            max: Number(max.toFixed(2)),
            std: Number(std.toFixed(2))
        };
    }

    // 提取时间序列数据
    static extractTimeSeries(data) {
        if (!data || !data.step_by_step) return [];
        
        return data.step_by_step.map(step => ({
            year: step.year,
            step: step.step,
            total_remittances: step.total_remittances || 0,
            successful_remittances: step.successful_remittances || 0,
            success_rate: step.success_rate || 0,
            total_amount: step.total_amount || 0,
            average_amount: step.average_amount || 0,
            active_institutions: step.active_institutions || 0,
            average_trust: step.average_trust || 0,
            wealthy_families: step.wealthy_families || 0,
            survival_families: step.survival_families || 0
        }));
    }

    // 生成颜色数组
    static generateColors(count) {
        const colors = [
            '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
            '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#d35400',
            '#c0392b', '#27ae60', '#2980b9', '#8e44ad', '#16a085'
        ];
        
        const result = [];
        for (let i = 0; i < count; i++) {
            result.push(colors[i % colors.length]);
        }
        return result;
    }

    // 深拷贝对象
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => DataProcessor.deepClone(item));
        if (obj instanceof Object) {
            return Object.fromEntries(
                Object.entries(obj).map(([key, value]) => [key, DataProcessor.deepClone(value)])
            );
        }
    }

    // 防抖函数
    static debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// 本地存储管理
class StorageManager {
    static set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Failed to set localStorage item:', error);
        }
    }

    static get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Failed to get localStorage item:', error);
            return defaultValue;
        }
    }

    static remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Failed to remove localStorage item:', error);
        }
    }

    static clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Failed to clear localStorage:', error);
        }
    }

    // 用户偏好设置
    static getPreferences() {
        return this.get('userPreferences', {
            theme: 'light',
            language: 'zh-CN',
            charts: {
                animation: true,
                responsive: true
            },
            simulations: {
                autoRefresh: false,
                refreshInterval: 30000
            }
        });
    }

    static setPreferences(preferences) {
        this.set('userPreferences', preferences);
    }

    // 最近查看的仿真
    static getRecentSimulations() {
        return this.get('recentSimulations', []);
    }

    static addRecentSimulation(simulation) {
        const recent = this.getRecentSimulations();
        const exists = recent.find(s => s.id === simulation.id);
        
        if (exists) {
            // 移到最前面
            recent.splice(recent.indexOf(exists), 1);
        }
        
        recent.unshift({
            id: simulation.id,
            name: simulation.name,
            viewedAt: new Date().toISOString()
        });

        // 只保留最近10个
        if (recent.length > 10) {
            recent.splice(10);
        }

        this.set('recentSimulations', recent);
    }

    // 仿真过滤器设置
    static getSimulationFilters() {
        return this.get('simulationFilters', {
            scenario: '',
            timeRange: '',
            search: '',
            sortBy: 'createdAt',
            sortOrder: 'desc'
        });
    }

    static setSimulationFilters(filters) {
        this.set('simulationFilters', filters);
    }
}

// 错误处理
class ErrorHandler {
    static handle(error, context = '') {
        console.error(`Error in ${context}:`, error);
        
        // 显示用户友好的错误消息
        const message = this.getUserFriendlyMessage(error);
        this.showError(message);
        
        // 可以添加错误上报逻辑
        this.reportError(error, context);
    }

    static getUserFriendlyMessage(error) {
        if (error instanceof TypeError) {
            return '数据类型错误，请检查输入格式';
        }
        
        if (error instanceof SyntaxError) {
            return '数据解析错误，请检查数据格式';
        }
        
        if (error.message.includes('NetworkError')) {
            return '网络连接错误，请检查网络设置';
        }
        
        if (error.message.includes('404')) {
            return '请求的资源不存在';
        }
        
        if (error.message.includes('500')) {
            return '服务器内部错误，请稍后重试';
        }
        
        return error.message || '发生未知错误';
    }

    static showError(message) {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show';
        errorDiv.innerHTML = `
            <strong>错误：</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 添加到页面
        const container = document.querySelector('.container') || document.body;
        container.insertBefore(errorDiv, container.firstChild);
        
        // 自动移除
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    static reportError(error, context) {
        // 这里可以添加错误上报逻辑
        console.log('Error reported:', {
            error: error.message,
            stack: error.stack,
            context: context,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
        });
    }
}

// 事件管理器
class EventManager {
    constructor() {
        this.events = {};
    }

    on(eventName, callback) {
        if (!this.events[eventName]) {
            this.events[eventName] = [];
        }
        this.events[eventName].push(callback);
    }

    off(eventName, callback) {
        if (!this.events[eventName]) return;
        this.events[eventName] = this.events[eventName].filter(cb => cb !== callback);
    }

    emit(eventName, data) {
        if (!this.events[eventName]) return;
        this.events[eventName].forEach(callback => callback(data));
    }

    once(eventName, callback) {
        const onceCallback = (data) => {
            callback(data);
            this.off(eventName, onceCallback);
        };
        this.on(eventName, onceCallback);
    }
}

// 创建全局事件管理器
const eventManager = new EventManager();

// 导出供其他模块使用
window.QiaopiAPI = api;
window.DataProcessor = DataProcessor;
window.StorageManager = StorageManager;
window.ErrorHandler = ErrorHandler;
window.EventManager = eventManager;