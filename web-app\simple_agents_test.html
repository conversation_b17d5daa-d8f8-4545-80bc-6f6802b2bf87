<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单AI智能体测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .content-section { 
            background: white; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-4">
        <h1 class="text-center mb-4">🤖 AI智能体功能测试</h1>
        
        <!-- 导航测试 -->
        <div class="text-center mb-4">
            <button class="btn btn-primary me-2" onclick="showAgentsSection()">
                <i class="fas fa-robot me-2"></i>显示AI智能体页面
            </button>
            <button class="btn btn-outline-secondary" onclick="hideAllSections()">
                隐藏所有页面
            </button>
        </div>

        <!-- 状态显示 -->
        <div id="statusInfo" class="alert alert-info">
            点击上面的按钮测试AI智能体页面显示
        </div>

        <!-- AI智能体页面内容 -->
        <div id="agents-section" class="content-section" style="display: none;">
            <div class="section-header">
                <h2><i class="fas fa-robot"></i> AI智能体管理</h2>
            </div>
            
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-0">设计和管理仿真中的AI智能体。每种智能体类型都有独特的行为模式和学习能力。</p>
                        </div>
                        <div>
                            <a href="/agent-designer.html" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>创建新智能体
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速模板 -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5><i class="fas fa-magic me-2"></i>快速模板</h5>
                    <p class="text-muted small">使用预设模板快速创建智能体</p>
                </div>
            </div>
            
            <div class="row">
                <!-- 移民智能体模板 -->
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-user-friends fa-2x text-primary"></i>
                            </div>
                            <h6 class="card-title">海外移民</h6>
                            <p class="card-text small">模拟海外华侨的汇款行为和决策过程</p>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> 强化学习算法</li>
                                <li><i class="fas fa-check text-success me-1"></i> 风险评估机制</li>
                                <li><i class="fas fa-check text-success me-1"></i> 宗族网络连接</li>
                                <li><i class="fas fa-check text-success me-1"></i> 经济决策模型</li>
                            </ul>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="useTemplate('migrant')">
                                <i class="fas fa-magic me-1"></i>使用模板
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 家庭智能体模板 -->
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-home fa-2x text-success"></i>
                            </div>
                            <h6 class="card-title">家乡家庭</h6>
                            <p class="card-text small">模拟接收汇款的家庭成员行为模式</p>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> 需求评估算法</li>
                                <li><i class="fas fa-check text-success me-1"></i> 资源分配策略</li>
                                <li><i class="fas fa-check text-success me-1"></i> 社会网络维护</li>
                                <li><i class="fas fa-check text-success me-1"></i> 感恩表达机制</li>
                            </ul>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="useTemplate('family')">
                                <i class="fas fa-magic me-1"></i>使用模板
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 金融机构模板 -->
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <i class="fas fa-building fa-2x text-warning"></i>
                            </div>
                            <h6 class="card-title">金融机构</h6>
                            <p class="card-text small">模拟银庄、批局等金融中介机构</p>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success me-1"></i> 风险管理系统</li>
                                <li><i class="fas fa-check text-success me-1"></i> 信用评估模型</li>
                                <li><i class="fas fa-check text-success me-1"></i> 网络优化算法</li>
                                <li><i class="fas fa-check text-success me-1"></i> 盈利最大化策略</li>
                            </ul>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-outline-warning btn-sm w-100" onclick="useTemplate('institution')">
                                <i class="fas fa-magic me-1"></i>使用模板
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的智能体 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="fas fa-user-cog me-2"></i>我的智能体</h5>
                        <button class="btn btn-outline-secondary btn-sm" onclick="loadMyAgents()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                    
                    <div id="myAgentsGrid" class="row">
                        <div class="col-12 text-center py-4">
                            <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">还没有创建智能体</h5>
                            <p class="text-muted">点击"创建新智能体"开始设计你的第一个AI智能体</p>
                            <a href="/agent-designer.html" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>创建智能体
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showAgentsSection() {
            const section = document.getElementById('agents-section');
            const status = document.getElementById('statusInfo');
            
            if (section) {
                section.style.display = 'block';
                status.className = 'alert alert-success';
                status.innerHTML = '✅ AI智能体页面已显示！你应该能看到智能体模板和管理界面了。';
            } else {
                status.className = 'alert alert-danger';
                status.innerHTML = '❌ 找不到agents-section元素';
            }
        }

        function hideAllSections() {
            const section = document.getElementById('agents-section');
            const status = document.getElementById('statusInfo');
            
            if (section) {
                section.style.display = 'none';
                status.className = 'alert alert-info';
                status.innerHTML = '⚪ AI智能体页面已隐藏';
            }
        }

        function useTemplate(template) {
            alert(`🎯 使用${template}模板！\n\n这将跳转到智能体设计器并自动填入${template}类型的预设配置。`);
            
            // 实际应用中会跳转到设计器
            const designerUrl = `/agent-designer.html?template=${template}`;
            if (confirm('是否跳转到智能体设计器？')) {
                window.open(designerUrl, '_blank');
            }
        }

        function loadMyAgents() {
            alert('🔄 刷新我的智能体列表...\n\n这会重新加载用户创建的所有智能体。');
        }

        // 自动显示说明
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.getElementById('statusInfo').innerHTML = `
                    <strong>🧪 这是AI智能体功能的独立测试页面</strong><br>
                    点击 "显示AI智能体页面" 按钮查看完整的AI智能体管理界面。<br>
                    <small class="text-muted">这个页面展示了主页面中AI智能体部分应该显示的内容。</small>
                `;
            }, 1000);
        });
    </script>
</body>
</html>