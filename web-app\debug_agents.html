<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体调试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { 
            background: #f8f9fa; 
            padding: 20px;
            font-family: 'Segoe UI', sans-serif;
        }
        .debug-section { 
            background: white; 
            padding: 20px; 
            margin: 20px 0; 
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            margin: 5px;
            min-width: 120px;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-bug"></i> AI智能体功能调试</h1>
        <p class="text-muted">检测AI智能体页面为什么显示空白</p>

        <!-- 测试结果显示 -->
        <div class="debug-section">
            <h4>🔍 检测结果</h4>
            <div id="testResults">
                <div class="alert alert-info">正在检测...</div>
            </div>
        </div>

        <!-- 手动测试按钮 -->
        <div class="debug-section">
            <h4>🧪 手动测试</h4>
            <button class="btn btn-primary test-btn" onclick="testShowSection()">测试显示AI智能体</button>
            <button class="btn btn-success test-btn" onclick="testElementExists()">检查HTML元素</button>
            <button class="btn btn-warning test-btn" onclick="testJavaScript()">测试JavaScript函数</button>
            <button class="btn btn-info test-btn" onclick="forceShowAgents()">强制显示AI智能体</button>
        </div>

        <!-- 现场演示 AI智能体内容 -->
        <div class="debug-section">
            <h4>🤖 AI智能体内容预览</h4>
            <div class="alert alert-success">
                <p><strong>这就是AI智能体页面应该显示的内容：</strong></p>
            </div>
            
            <!-- 直接嵌入AI智能体内容 -->
            <div class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-robot"></i> AI智能体管理</h2>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <p class="mb-0">设计和管理仿真中的AI智能体。每种智能体类型都有独特的行为模式和学习能力。</p>
                            </div>
                            <div>
                                <a href="/agent-designer.html" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>创建新智能体
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Agent Templates -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5><i class="fas fa-magic me-2"></i>快速模板</h5>
                        <p class="text-muted small">使用预设模板快速创建智能体</p>
                    </div>
                </div>
                
                <div class="row">
                    <!-- 移民智能体模板 -->
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <i class="fas fa-user-friends fa-2x text-primary"></i>
                                </div>
                                <h6 class="card-title">海外移民</h6>
                                <p class="card-text small">模拟海外华侨的汇款行为和决策过程</p>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-1"></i> 强化学习算法</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 风险评估机制</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 宗族网络连接</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 经济决策模型</li>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-outline-primary btn-sm w-100" onclick="useTemplate('migrant')">
                                    <i class="fas fa-magic me-1"></i>使用模板
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 家庭智能体模板 -->
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <i class="fas fa-home fa-2x text-success"></i>
                                </div>
                                <h6 class="card-title">家乡家庭</h6>
                                <p class="card-text small">模拟接收汇款的家庭成员行为模式</p>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-1"></i> 需求评估算法</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 资源分配策略</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 社会网络维护</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 感恩表达机制</li>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-outline-success btn-sm w-100" onclick="useTemplate('family')">
                                    <i class="fas fa-magic me-1"></i>使用模板
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 金融机构模板 -->
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <i class="fas fa-building fa-2x text-warning"></i>
                                </div>
                                <h6 class="card-title">金融机构</h6>
                                <p class="card-text small">模拟银庄、批局等金融中介机构</p>
                                <ul class="list-unstyled small">
                                    <li><i class="fas fa-check text-success me-1"></i> 风险管理系统</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 信用评估模型</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 网络优化算法</li>
                                    <li><i class="fas fa-check text-success me-1"></i> 盈利最大化策略</li>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-outline-warning btn-sm w-100" onclick="useTemplate('institution')">
                                    <i class="fas fa-magic me-1"></i>使用模板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- My Agents List -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-user-cog me-2"></i>我的智能体</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="loadMyAgents()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                        
                        <div id="myAgentsGrid" class="row">
                            <div class="col-12 text-center py-4">
                                <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">还没有创建智能体</h5>
                                <p class="text-muted">点击"创建新智能体"开始设计你的第一个AI智能体</p>
                                <a href="/agent-designer.html" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>创建智能体
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试控制 -->
        <div class="debug-section">
            <h4>🎮 测试控制</h4>
            <button class="btn btn-danger test-btn" onclick="window.location.href='http://localhost:3508'">回到主页面</button>
            <button class="btn btn-secondary test-btn" onclick="window.location.reload()">刷新页面</button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testShowSection() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="alert alert-info">正在测试显示AI智能体页面...</div>';
            
            // 检查是否有agents-section元素
            const agentsSection = document.getElementById('agents-section');
            if (agentsSection) {
                results.innerHTML += '<div class="alert alert-success">✅ 找到agents-section元素</div>';
                
                // 尝试显示
                agentsSection.style.display = 'block';
                results.innerHTML += '<div class="alert alert-success">✅ 已设置display: block</div>';
                
                // 检查是否真的显示了
                const computedStyle = window.getComputedStyle(agentsSection);
                results.innerHTML += `<div class="alert alert-info">📊 元素状态: display=${computedStyle.display}, visibility=${computedStyle.visibility}</div>`;
            } else {
                results.innerHTML += '<div class="alert alert-danger">❌ 未找到agents-section元素</div>';
            }
        }

        function testElementExists() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="alert alert-info">检查HTML元素...</div>';
            
            const elements = [
                'agents-section',
                'agentTemplatesGrid', 
                'myAgentsGrid'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results.innerHTML += `<div class="alert alert-success">✅ 找到元素: ${id}</div>`;
                } else {
                    results.innerHTML += `<div class="alert alert-danger">❌ 缺失元素: ${id}</div>`;
                }
            });
        }

        function testJavaScript() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="alert alert-info">测试JavaScript函数...</div>';
            
            const functions = ['showSection', 'loadMyAgents', 'useTemplate'];
            
            functions.forEach(funcName => {
                if (window[funcName] && typeof window[funcName] === 'function') {
                    results.innerHTML += `<div class="alert alert-success">✅ 函数存在: ${funcName}</div>`;
                } else {
                    results.innerHTML += `<div class="alert alert-danger">❌ 函数缺失: ${funcName}</div>`;
                }
            });
            
            // 测试uiManager
            if (window.uiManager) {
                results.innerHTML += '<div class="alert alert-success">✅ uiManager已创建</div>';
            } else {
                results.innerHTML += '<div class="alert alert-danger">❌ uiManager未创建</div>';
            }
        }

        function forceShowAgents() {
            const results = document.getElementById('testResults');
            
            // 尝试强制显示agents section
            try {
                // 隐藏所有section
                const sections = document.querySelectorAll('.content-section');
                sections.forEach(section => {
                    section.style.display = 'none';
                });
                
                // 强制显示agents-section
                const agentsSection = document.getElementById('agents-section');
                if (agentsSection) {
                    agentsSection.style.display = 'block';
                    agentsSection.style.visibility = 'visible';
                    agentsSection.style.opacity = '1';
                    
                    results.innerHTML = '<div class="alert alert-success">✅ 强制显示AI智能体页面成功！应该可以看到内容了。</div>';
                    
                    // 同时调用loadAgentsData
                    if (window.uiManager && window.uiManager.loadAgentsData) {
                        window.uiManager.loadAgentsData();
                        results.innerHTML += '<div class="alert alert-info">📊 已调用loadAgentsData函数</div>';
                    }
                } else {
                    results.innerHTML = '<div class="alert alert-danger">❌ 无法找到agents-section元素</div>';
                }
            } catch (error) {
                results.innerHTML = `<div class="alert alert-danger">❌ 强制显示失败: ${error.message}</div>`;
            }
        }

        // 自动检测
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testElementExists();
                testJavaScript();
            }, 1000);
        });

        // 定义必要的全局函数
        function useTemplate(template) {
            alert(`使用模板: ${template}\n将跳转到智能体设计器...`);
            window.open(`/agent-designer.html?template=${template}`, '_blank');
        }

        function loadMyAgents() {
            alert('刷新我的智能体列表...');
        }
    </script>
</body>
</html>