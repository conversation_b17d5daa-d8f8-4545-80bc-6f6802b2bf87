#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Installation and Run Script for Qiaopi Visualization
侨批可视化系统完整安装运行脚本
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def check_python_version():
    """Check if Python version is adequate"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor} detected")
    return True

def install_package(package):
    """Install a package using pip"""
    try:
        print(f"  Installing {package}...", end=" ", flush=True)
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package],
            capture_output=True,
            text=True,
            timeout=60
        )
        if result.returncode == 0:
            print("✓")
            return True
        else:
            print("✗")
            return False
    except Exception as e:
        print(f"✗ ({e})")
        return False

def upgrade_pip():
    """Upgrade pip to latest version"""
    try:
        print("Upgrading pip...", end=" ", flush=True)
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
            capture_output=True,
            timeout=30
        )
        print("✓")
        return True
    except:
        print("✗")
        return False

def install_dependencies():
    """Install all required dependencies"""
    print("\n" + "="*60)
    print("Installing Dependencies")
    print("="*60)
    
    # Core requirements
    core_packages = [
        "dash",
        "plotly",
        "pandas",
        "numpy",
    ]
    
    # Optional but recommended
    optional_packages = [
        "dash-bootstrap-components",
        "flask",
        "flask-socketio",
        "python-socketio",
        "networkx",
        "scikit-learn",
        "scipy",
        "statsmodels",
        "psutil",
        "dash-cytoscape",
        "aiohttp",
        "aiohttp-cors",
    ]
    
    print("\n📦 Installing core packages (required):")
    core_success = True
    for package in core_packages:
        if not install_package(package):
            core_success = False
    
    if not core_success:
        print("\n❌ Failed to install core packages. Cannot continue.")
        return False
    
    print("\n📦 Installing optional packages (for advanced features):")
    for package in optional_packages:
        install_package(package)  # Continue even if some fail
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "visualization_output",
        "simple_visualization_output",
        "logs",
        "data"
    ]
    
    print("\n📁 Creating directories:")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✓ {directory}")

def run_simple_dashboard():
    """Run the simple dashboard"""
    print("\n" + "="*60)
    print("Starting Simple Dashboard")
    print("="*60)
    
    # Check if visualization folder exists
    if os.path.exists("visualization/simple_dashboard.py"):
        dashboard_path = "visualization/simple_dashboard.py"
    elif os.path.exists("simple_dashboard.py"):
        dashboard_path = "simple_dashboard.py"
    else:
        print("❌ Dashboard file not found!")
        print("Creating a minimal dashboard...")
        
        # Create minimal dashboard inline
        minimal_dashboard = '''
import dash
from dash import dcc, html
import plotly.graph_objs as go
import random

app = dash.Dash(__name__)

app.layout = html.Div([
    html.H1("Qiaopi Network Simulation", style={'textAlign': 'center'}),
    html.H3("侨批网络仿真 - Demo Mode", style={'textAlign': 'center', 'color': 'gray'}),
    html.Hr(),
    dcc.Graph(
        id='demo-chart',
        figure={
            'data': [
                go.Scatter(
                    x=list(range(100)),
                    y=[random.randint(0, 100) for _ in range(100)],
                    mode='lines',
                    name='Demo Data'
                )
            ],
            'layout': go.Layout(title='Simulation Demo Data')
        }
    ),
    html.Div("This is a demo dashboard. Real simulation not available.", 
             style={'textAlign': 'center', 'marginTop': '20px', 'color': 'orange'})
])

if __name__ == "__main__":
    print("Opening dashboard at http://localhost:8050")
    app.run_server(debug=False, port=8050, host="0.0.0.0")
'''
        with open("minimal_dashboard.py", "w") as f:
            f.write(minimal_dashboard)
        dashboard_path = "minimal_dashboard.py"
    
    print(f"📊 Running dashboard from: {dashboard_path}")
    print("\n" + "="*60)
    print("🌐 Dashboard will open at: http://localhost:8050")
    print("🛑 Press Ctrl+C to stop")
    print("="*60 + "\n")
    
    # Open browser after a short delay
    def open_browser():
        time.sleep(3)
        webbrowser.open("http://localhost:8050")
    
    import threading
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # Run the dashboard
    try:
        subprocess.run([sys.executable, dashboard_path])
    except KeyboardInterrupt:
        print("\n\n✅ Dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Error running dashboard: {e}")

def main():
    """Main installation and run process"""
    print("""
╔════════════════════════════════════════════════════════════╗
║       Qiaopi Network Visualization System Setup           ║
║       侨批网络可视化系统安装程序                              ║
╚════════════════════════════════════════════════════════════╝
    """)
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Please install Python 3.7 or higher")
        return 1
    
    # Upgrade pip
    upgrade_pip()
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install core dependencies")
        print("Try manual installation:")
        print("  pip install dash plotly pandas numpy")
        return 1
    
    # Create directories
    create_directories()
    
    print("\n✅ Setup complete!")
    
    # Ask user whether to run
    print("\n" + "="*60)
    response = input("Would you like to start the dashboard now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes', '']:
        run_simple_dashboard()
    else:
        print("\nTo run the dashboard later, use:")
        print("  python visualization/simple_dashboard.py")
        print("     or")
        print("  python install_and_run.py")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n✅ Process terminated by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)