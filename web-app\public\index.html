<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侨批网络仿真可视化平台</title>
    <meta name="description" content="基于真实数据的侨批网络仿真结果可视化">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 注意：已移除有问题的插件，改用原生Chart.js图表类型 -->
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/main.css">
    
    <!-- Real Data Page Styles -->
    <style>
        .data-stat-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .data-stat-item:hover {
            transform: translateY(-5px);
        }
        
        .data-stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }
        
        .data-stat-value {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .chart-container h5 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .insight-item {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            margin-bottom: 1rem;
        }
        
        .insight-item h6 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .insight-item ul li {
            margin-bottom: 0.5rem;
            color: #555;
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #2c3e50;
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .pagination .page-link {
            color: #3498db;
            border-color: #dee2e6;
        }
        
        .pagination .page-item.active .page-link {
            background-color: #3498db;
            border-color: #3498db;
        }
        
        .pagination .page-link:hover {
            color: #2980b9;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }
        
        .real-data-stats {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .real-data-stats h5 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px 10px 0 0 !important;
        }
        
        .card-header h5 {
            margin: 0;
            font-weight: 600;
        }
        
        .btn-outline-primary {
            border-color: white;
            color: white;
        }
        
        .btn-outline-primary:hover {
            background-color: white;
            color: #667eea;
        }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" href="/images/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-network-wired"></i> 侨批网络仿真
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('overview')"><i class="fas fa-home"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('simulations')"><i class="fas fa-chart-line"></i> 仿真结果</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('agents')"><i class="fas fa-robot"></i> AI智能体</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('scenarios')"><i class="fas fa-flask"></i> 场景分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('real-data')"><i class="fas fa-database"></i> 真实数据</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('comparison')"><i class="fas fa-balance-scale"></i> 对比分析</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/enhanced-simulation.html"><i class="fas fa-rocket"></i> 🔥增强仿真🔥</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/api/health" target="_blank">
                            <i class="fas fa-heartbeat"></i> API状态
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid">
        <!-- Hero Section -->
        <section class="hero-section text-center py-5">
            <div class="container">
                <h1 class="display-4 fw-bold text-white mb-4">
                    侨批网络仿真可视化平台
                </h1>
                <p class="lead text-white mb-4">
                    基于13,403条真实侨批数据的AI智能体仿真结果展示
                </p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="stat-card">
                                    <div class="stat-number">13,403</div>
                                    <div class="stat-label">真实侨批记录</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-card">
                                    <div class="stat-number">7</div>
                                    <div class="stat-label">仿真场景</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-card">
                                    <div class="stat-number">50+</div>
                                    <div class="stat-label">仿真年份</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="py-4">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="action-buttons d-flex justify-content-center gap-3 flex-wrap">
                            <button class="btn btn-primary" onclick="loadSimulations()">
                                <i class="fas fa-sync-alt"></i> 刷新数据
                            </button>
                            <button class="btn btn-success" onclick="runNewSimulation()">
                                <i class="fas fa-play"></i> 运行仿真
                            </button>
                            <button class="btn btn-info" onclick="viewRealData()">
                                <i class="fas fa-database"></i> 查看真实数据
                            </button>
                            <button class="btn btn-warning" onclick="showSection('agents')" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); border: none;">
                                <i class="fas fa-robot"></i> 🔥 AI智能体 🔥
                            </button>
                            <a href="/enhanced-simulation.html" class="btn btn-primary" style="background: linear-gradient(45deg, #667eea, #764ba2); border: none;">
                                <i class="fas fa-rocket"></i> 🚀 增强仿真 🚀
                            </a>
                            <button class="btn btn-secondary" onclick="exportResults()">
                                <i class="fas fa-download"></i> 导出结果
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="py-4">
            <div class="container">
                <div class="row">
                    <!-- Sidebar -->
                    <div class="col-lg-3">
                        <div class="sidebar">
                            <h5 class="sidebar-title">快速导航</h5>
                            <div class="nav flex-column">
                                <a class="nav-link active" href="#" onclick="showSection('overview')">
                                    <i class="fas fa-tachometer-alt"></i> 总览
                                </a>
                                <a class="nav-link" href="#" onclick="showSection('simulations')">
                                    <i class="fas fa-chart-line"></i> 仿真结果
                                </a>
                                <a class="nav-link" href="#" onclick="showSection('agents')">
                                    <i class="fas fa-robot"></i> AI智能体
                                </a>
                                <a class="nav-link" href="#" onclick="showSection('scenarios')">
                                    <i class="fas fa-flask"></i> 场景分析
                                </a>
                                <a class="nav-link" href="#" onclick="showSection('comparison')">
                                    <i class="fas fa-balance-scale"></i> 对比分析
                                </a>
                                <a class="nav-link" href="#" onclick="showSection('real-data')">
                                    <i class="fas fa-database"></i> 真实数据
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="col-lg-9">
                        <!-- Overview Section -->
                        <div id="overview-section" class="content-section">
                            <div class="section-header">
                                <h2><i class="fas fa-tachometer-alt"></i> 系统总览</h2>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <h5>仿真分布</h5>
                                        <canvas id="simulationDistributionChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <h5>场景类型</h5>
                                        <canvas id="scenarioTypeChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="recent-simulations">
                                        <h5>最近仿真</h5>
                                        <div id="recentSimulationsList" class="list-group">
                                            <!-- 动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Simulations Section -->
                        <div id="simulations-section" class="content-section" style="display: none;">
                            <div class="section-header">
                                <h2><i class="fas fa-chart-line"></i> 仿真结果</h2>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="simulation-filters">
                                            <select class="form-select me-2" id="scenarioFilter">
                                                <option value="">所有场景</option>
                                            </select>
                                            <select class="form-select me-2" id="timeFilter">
                                                <option value="">所有时间</option>
                                                <option value="recent">最近30天</option>
                                                <option value="month">本月</option>
                                                <option value="year">本年</option>
                                            </select>
                                            <input type="text" class="form-control" id="searchInput" placeholder="搜索仿真...">
                                        </div>
                                        <button class="btn btn-primary" onclick="loadSimulations()">
                                            <i class="fas fa-sync-alt"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row" id="simulationsGrid">
                                <!-- 动态加载仿真卡片 -->
                            </div>
                        </div>

                        <!-- AI Agents Section -->
                        <div id="agents-section" class="content-section" style="display: none;">
                            <div class="section-header">
                                <h2><i class="fas fa-robot"></i> AI智能体管理</h2>
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <p class="mb-0">设计和管理仿真中的AI智能体。每种智能体类型都有独特的行为模式和学习能力。</p>
                                        </div>
                                        <div>
                                            <a href="/agent-designer.html" class="btn btn-primary">
                                                <i class="fas fa-plus me-2"></i>创建新智能体
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Agent Templates -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5><i class="fas fa-magic me-2"></i>快速模板</h5>
                                    <p class="text-muted small">使用预设模板快速创建智能体</p>
                                </div>
                            </div>
                            
                            <div class="row" id="agentTemplatesGrid">
                                <!-- 移民智能体模板 -->
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <i class="fas fa-user-friends fa-2x text-primary"></i>
                                            </div>
                                            <h6 class="card-title">海外移民</h6>
                                            <p class="card-text small">模拟海外华侨的汇款行为和决策过程</p>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 强化学习算法</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 风险评估机制</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 宗族网络连接</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 经济决策模型</li>
                                            </ul>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-primary btn-sm w-100" onclick="useTemplate('migrant')">
                                                <i class="fas fa-magic me-1"></i>使用模板
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 家庭智能体模板 -->
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <i class="fas fa-home fa-2x text-success"></i>
                                            </div>
                                            <h6 class="card-title">家乡家庭</h6>
                                            <p class="card-text small">模拟接收汇款的家庭成员行为模式</p>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 需求评估算法</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 资源分配策略</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 社会网络维护</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 感恩表达机制</li>
                                            </ul>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-success btn-sm w-100" onclick="useTemplate('family')">
                                                <i class="fas fa-magic me-1"></i>使用模板
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 金融机构模板 -->
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="text-center mb-3">
                                                <i class="fas fa-building fa-2x text-warning"></i>
                                            </div>
                                            <h6 class="card-title">金融机构</h6>
                                            <p class="card-text small">模拟银庄、批局等金融中介机构</p>
                                            <ul class="list-unstyled small">
                                                <li><i class="fas fa-check text-success me-1"></i> 风险管理系统</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 信用评估模型</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 网络优化算法</li>
                                                <li><i class="fas fa-check text-success me-1"></i> 盈利最大化策略</li>
                                            </ul>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-outline-warning btn-sm w-100" onclick="useTemplate('institution')">
                                                <i class="fas fa-magic me-1"></i>使用模板
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- My Agents List -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-user-cog me-2"></i>我的智能体</h5>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="loadMyAgents()">
                                            <i class="fas fa-sync-alt me-1"></i>刷新
                                        </button>
                                    </div>
                                    
                                    <div id="myAgentsGrid" class="row">
                                        <!-- 动态加载用户创建的智能体 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Scenarios Section -->
                        <div id="scenarios-section" class="content-section" style="display: none;">
                            <div class="section-header">
                                <h2><i class="fas fa-flask"></i> 场景分析</h2>
                            </div>
                            
                            <div class="row" id="scenariosGrid">
                                <!-- 动态加载场景卡片 -->
                            </div>
                        </div>

                        <!-- Comparison Section -->
                        <div id="comparison-section" class="content-section" style="display: none;">
                            <div class="section-header">
                                <h2><i class="fas fa-balance-scale"></i> 对比分析</h2>
                            </div>
                            
                            <div class="comparison-panel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>选择仿真对比</h5>
                                        <div id="simulationSelector">
                                            <div class="simulation-selection">
                                                <select class="form-select mb-2" id="simulation1Select">
                                                    <option value="">选择仿真1</option>
                                                </select>
                                                <select class="form-select mb-2" id="simulation2Select">
                                                    <option value="">选择仿真2</option>
                                                </select>
                                                <button class="btn btn-primary" onclick="compareSimulations()">
                                                    <i class="fas fa-balance-scale"></i> 开始对比
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>快速对比</h5>
                                        <div id="quickComparison">
                                            <!-- 动态加载快速对比选项 -->
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="comparison-results mt-4" id="comparisonResults">
                                    <!-- 对比结果 -->
                                </div>
                            </div>
                        </div>

                        <!-- Real Data Section -->
                        <div id="real-data-section" class="content-section" style="display: none;">
                            <div class="section-header">
                                <h2><i class="fas fa-database"></i> 真实数据</h2>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <h5>汇款金额分布</h5>
                                        <canvas id="realAmountDistributionChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <h5>地理分布</h5>
                                        <canvas id="realGeographicDistributionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="real-data-stats">
                                        <h5>数据统计</h5>
                                        <div id="realDataStats" class="row">
                                            <!-- 动态加载统计数据 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>侨批网络仿真可视化平台</h6>
                    <p class="small mb-0">基于真实数据的AI智能体仿真研究</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="small mb-0">
                        <i class="fas fa-code"></i> Node.js + Express + Chart.js
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>正在加载数据...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/js/api.js"></script>
    <script src="/js/charts.js"></script>
    <script src="/js/ui.js"></script>
    
    <!-- AI智能体调试脚本 -->
    <script src="/ai_agents_debug.js"></script>
    
    <!-- Page specific scripts -->
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });
    </script>
</body>
</html>