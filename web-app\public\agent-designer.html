<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体设计器 - 侨批网络仿真</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #16a085;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --border-radius: 8px;
            --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: var(--dark-color);
        }

        .navbar {
            box-shadow: var(--box-shadow);
        }

        .agent-designer-container {
            margin-top: 20px;
        }

        .designer-sidebar {
            background: white;
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--box-shadow);
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .designer-main {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            min-height: 600px;
        }

        .agent-type-card {
            border: 2px solid #e9ecef;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: var(--transition);
        }

        .agent-type-card:hover {
            border-color: var(--secondary-color);
            transform: translateY(-2px);
        }

        .agent-type-card.selected {
            border-color: var(--primary-color);
            background-color: #f8f9fa;
        }

        .agent-type-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--secondary-color);
        }

        .parameter-group {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid var(--secondary-color);
        }

        .parameter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .preview-panel {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
        }

        .behavior-config {
            border: 1px solid #dee2e6;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 15px;
        }

        .learning-config {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .memory-config {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .social-config {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .range-slider {
            width: 100%;
            margin: 10px 0;
        }

        .range-value {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            display: inline-block;
            min-width: 40px;
            text-align: center;
        }

        .agent-preview {
            border: 2px dashed #dee2e6;
            border-radius: var(--border-radius);
            padding: 20px;
            text-align: center;
            background: #fdfdfe;
        }

        .agent-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 2rem;
        }

        .btn-create-agent {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: var(--border-radius);
            font-weight: 600;
            transition: var(--transition);
        }

        .btn-create-agent:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot"></i> AI智能体设计器
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home"></i> 返回主页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#"><i class="fas fa-robot"></i> 智能体设计</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm" onclick="saveAgentDesign()">
                            <i class="fas fa-save"></i> 保存设计
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container agent-designer-container">
        <div class="row">
            <!-- Sidebar - Agent Types -->
            <div class="col-lg-3">
                <div class="designer-sidebar">
                    <h5><i class="fas fa-layer-group me-2"></i>智能体类型</h5>
                    <p class="small text-muted">选择智能体的基础类型</p>
                    
                    <div id="agentTypesList">
                        <!-- 移民智能体 -->
                        <div class="agent-type-card" data-type="migrant" onclick="selectAgentType('migrant')">
                            <div class="agent-type-icon">
                                <i class="fas fa-user-friends"></i>
                            </div>
                            <h6>移民智能体</h6>
                            <p class="small">模拟海外移民的行为决策</p>
                        </div>

                        <!-- 家庭智能体 -->
                        <div class="agent-type-card" data-type="family" onclick="selectAgentType('family')">
                            <div class="agent-type-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <h6>家庭智能体</h6>
                            <p class="small">模拟家乡家庭的接收行为</p>
                        </div>

                        <!-- 金融机构智能体 -->
                        <div class="agent-type-card" data-type="institution" onclick="selectAgentType('institution')">
                            <div class="agent-type-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <h6>金融机构</h6>
                            <p class="small">模拟银庄、批局等金融中介</p>
                        </div>

                        <!-- 政策制定者智能体 -->
                        <div class="agent-type-card" data-type="government" onclick="selectAgentType('government')">
                            <div class="agent-type-icon">
                                <i class="fas fa-landmark"></i>
                            </div>
                            <h6>政策制定者</h6>
                            <p class="small">模拟政府政策对网络的影响</p>
                        </div>

                        <!-- 商人智能体 -->
                        <div class="agent-type-card" data-type="merchant" onclick="selectAgentType('merchant')">
                            <div class="agent-type-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <h6>商人智能体</h6>
                            <p class="small">模拟商业网络中的交易行为</p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6><i class="fas fa-list me-2"></i>我的智能体</h6>
                        <div id="myAgentsList" class="list-group">
                            <!-- 用户创建的智能体列表 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Designer Area -->
            <div class="col-lg-9">
                <div class="designer-main">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-cogs me-2"></i>AI智能体设计器</h2>
                        <div>
                            <button class="btn btn-outline-secondary me-2" onclick="resetDesigner()">
                                <i class="fas fa-refresh"></i> 重置
                            </button>
                            <button class="btn btn-create-agent" onclick="createAgent()">
                                <i class="fas fa-plus me-2"></i>创建智能体
                            </button>
                        </div>
                    </div>

                    <!-- Designer Content -->
                    <div id="designerContent">
                        <!-- Welcome Screen -->
                        <div id="welcomeScreen" class="text-center py-5">
                            <div class="agent-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h4 class="mb-3">欢迎使用AI智能体设计器</h4>
                            <p class="text-muted mb-4">
                                请从左侧选择一个智能体类型开始设计。你可以配置智能体的行为参数、学习能力、记忆模式和社交特征。
                            </p>
                            <div class="row justify-content-center">
                                <div class="col-md-8">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-lightbulb me-2"></i>设计提示</h6>
                                        <ul class="mb-0 text-start">
                                            <li>选择智能体类型决定了基础行为模式</li>
                                            <li>调整参数可以精确控制智能体行为</li>
                                            <li>启用学习能力让智能体适应环境变化</li>
                                            <li>配置社交特征影响智能体间的互动</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Agent Configuration Form -->
                        <div id="agentConfigForm" style="display: none;">
                            <form id="agentForm">
                                <!-- Basic Information -->
                                <div class="parameter-group">
                                    <h5><i class="fas fa-info-circle me-2"></i>基础信息</h5>
                                    <div class="parameter-grid">
                                        <div class="mb-3">
                                            <label for="agentName" class="form-label">智能体名称</label>
                                            <input type="text" class="form-control" id="agentName" placeholder="例如: 潮汕移民" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="agentDescription" class="form-label">描述</label>
                                            <textarea class="form-control" id="agentDescription" rows="2" placeholder="描述这个智能体的特点和用途"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="agentCount" class="form-label">数量</label>
                                            <input type="number" class="form-control" id="agentCount" min="1" max="1000" value="100">
                                        </div>
                                        <div class="mb-3">
                                            <label for="agentLifespan" class="form-label">生命周期(年)</label>
                                            <input type="number" class="form-control" id="agentLifespan" min="10" max="100" value="60">
                                        </div>
                                    </div>
                                </div>

                                <!-- Behavior Configuration -->
                                <div class="behavior-config">
                                    <h5><i class="fas fa-brain me-2"></i>行为配置</h5>
                                    <div class="parameter-grid">
                                        <div class="mb-3">
                                            <label for="riskTolerance" class="form-label">
                                                风险容忍度 <span class="range-value" id="riskToleranceValue">0.5</span>
                                            </label>
                                            <input type="range" class="range-slider" id="riskTolerance" min="0" max="1" step="0.1" value="0.5" oninput="updateRangeValue('riskTolerance')">
                                            <small class="text-muted">控制智能体对风险的接受程度</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="decisionSpeed" class="form-label">
                                                决策速度 <span class="range-value" id="decisionSpeedValue">0.7</span>
                                            </label>
                                            <input type="range" class="range-slider" id="decisionSpeed" min="0.1" max="1" step="0.1" value="0.7" oninput="updateRangeValue('decisionSpeed')">
                                            <small class="text-muted">智能体做出决策的速度</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="cooperativeness" class="form-label">
                                                合作倾向 <span class="range-value" id="cooperativenessValue">0.8</span>
                                            </label>
                                            <input type="range" class="range-slider" id="cooperativeness" min="0" max="1" step="0.1" value="0.8" oninput="updateRangeValue('cooperativeness')">
                                            <small class="text-muted">与其他智能体合作的意愿</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="adaptability" class="form-label">
                                                适应性 <span class="range-value" id="adaptabilityValue">0.6</span>
                                            </label>
                                            <input type="range" class="range-slider" id="adaptability" min="0" max="1" step="0.1" value="0.6" oninput="updateRangeValue('adaptability')">
                                            <small class="text-muted">适应环境变化的能力</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Learning Configuration -->
                                <div class="learning-config">
                                    <h5><i class="fas fa-graduation-cap me-2"></i>学习能力</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enableLearning" checked>
                                                    <label class="form-check-label" for="enableLearning">
                                                        启用机器学习
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="learningRate" class="form-label">
                                                    学习率 <span class="range-value" id="learningRateValue">0.01</span>
                                                </label>
                                                <input type="range" class="range-slider" id="learningRate" min="0.001" max="0.1" step="0.001" value="0.01" oninput="updateRangeValue('learningRate')">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="experienceWeight" class="form-label">
                                                    经验权重 <span class="range-value" id="experienceWeightValue">0.7</span>
                                                </label>
                                                <input type="range" class="range-slider" id="experienceWeight" min="0" max="1" step="0.1" value="0.7" oninput="updateRangeValue('experienceWeight')">
                                            </div>
                                            <div class="mb-3">
                                                <label for="forgettingFactor" class="form-label">
                                                    遗忘因子 <span class="range-value" id="forgettingFactorValue">0.1</span>
                                                </label>
                                                <input type="range" class="range-slider" id="forgettingFactor" min="0" max="0.5" step="0.05" value="0.1" oninput="updateRangeValue('forgettingFactor')">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Memory Configuration -->
                                <div class="memory-config">
                                    <h5><i class="fas fa-memory me-2"></i>记忆模式</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="memoryCapacity" class="form-label">
                                                    记忆容量 <span class="range-value" id="memoryCapacityValue">100</span>
                                                </label>
                                                <input type="range" class="range-slider" id="memoryCapacity" min="10" max="500" step="10" value="100" oninput="updateRangeValue('memoryCapacity')">
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="episodicMemory" checked>
                                                    <label class="form-check-label" for="episodicMemory">
                                                        情节记忆
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="semanticMemory" checked>
                                                    <label class="form-check-label" for="semanticMemory">
                                                        语义记忆
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="proceduralMemory">
                                                    <label class="form-check-label" for="proceduralMemory">
                                                        程序性记忆
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Social Configuration -->
                                <div class="social-config">
                                    <h5><i class="fas fa-users me-2"></i>社交特征</h5>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="trustLevel" class="form-label">
                                                    信任水平 <span class="range-value" id="trustLevelValue">0.7</span>
                                                </label>
                                                <input type="range" class="range-slider" id="trustLevel" min="0" max="1" step="0.1" value="0.7" oninput="updateRangeValue('trustLevel')">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="networkSize" class="form-label">
                                                    网络规模 <span class="range-value" id="networkSizeValue">20</span>
                                                </label>
                                                <input type="range" class="range-slider" id="networkSize" min="5" max="100" step="5" value="20" oninput="updateRangeValue('networkSize')">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="influenceRadius" class="form-label">
                                                    影响半径 <span class="range-value" id="influenceRadiusValue">10</span>
                                                </label>
                                                <input type="range" class="range-slider" id="influenceRadius" min="1" max="50" step="1" value="10" oninput="updateRangeValue('influenceRadius')">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="enableReputationSystem">
                                                <label class="form-check-label" for="enableReputationSystem">
                                                    启用声誉系统
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" id="enableClanConnections" checked>
                                                <label class="form-check-label" for="enableClanConnections">
                                                    启用宗族连接
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Economic Behavior -->
                                <div class="parameter-group">
                                    <h5><i class="fas fa-dollar-sign me-2"></i>经济行为</h5>
                                    <div class="parameter-grid">
                                        <div class="mb-3">
                                            <label for="wealthLevel" class="form-label">
                                                财富水平 <span class="range-value" id="wealthLevelValue">中等</span>
                                            </label>
                                            <select class="form-select" id="wealthLevel" onchange="updateSelectValue('wealthLevel')">
                                                <option value="poor">贫困</option>
                                                <option value="modest">温饱</option>
                                                <option value="middle" selected>中等</option>
                                                <option value="wealthy">富裕</option>
                                                <option value="very_wealthy">非常富裕</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="savingsRate" class="form-label">
                                                储蓄率 <span class="range-value" id="savingsRateValue">0.3</span>
                                            </label>
                                            <input type="range" class="range-slider" id="savingsRate" min="0" max="1" step="0.05" value="0.3" oninput="updateRangeValue('savingsRate')">
                                        </div>
                                        <div class="mb-3">
                                            <label for="investmentPropensity" class="form-label">
                                                投资倾向 <span class="range-value" id="investmentPropensityValue">0.4</span>
                                            </label>
                                            <input type="range" class="range-slider" id="investmentPropensity" min="0" max="1" step="0.1" value="0.4" oninput="updateRangeValue('investmentPropensity')">
                                        </div>
                                        <div class="mb-3">
                                            <label for="remittanceFrequency" class="form-label">
                                                汇款频率(月) <span class="range-value" id="remittanceFrequencyValue">3</span>
                                            </label>
                                            <input type="range" class="range-slider" id="remittanceFrequency" min="1" max="12" step="1" value="3" oninput="updateRangeValue('remittanceFrequency')">
                                        </div>
                                    </div>
                                </div>

                                <!-- Geographic Configuration -->
                                <div class="parameter-group">
                                    <h5><i class="fas fa-map-marked-alt me-2"></i>地理配置</h5>
                                    <div class="parameter-grid">
                                        <div class="mb-3">
                                            <label for="originRegion" class="form-label">籍贯地区</label>
                                            <select class="form-select" id="originRegion">
                                                <option value="guangdong">广东</option>
                                                <option value="fujian">福建</option>
                                                <option value="hainan">海南</option>
                                                <option value="guangxi">广西</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="destinationRegion" class="form-label">目的地区</label>
                                            <select class="form-select" id="destinationRegion">
                                                <option value="southeast_asia">东南亚</option>
                                                <option value="americas">美洲</option>
                                                <option value="australia">澳洲</option>
                                                <option value="europe">欧洲</option>
                                                <option value="other">其他</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="migrationYear" class="form-label">移民年份</label>
                                            <input type="number" class="form-control" id="migrationYear" min="1850" max="1950" value="1920">
                                        </div>
                                        <div class="mb-3">
                                            <label for="mobilityLevel" class="form-label">
                                                流动性 <span class="range-value" id="mobilityLevelValue">0.5</span>
                                            </label>
                                            <input type="range" class="range-slider" id="mobilityLevel" min="0" max="1" step="0.1" value="0.5" oninput="updateRangeValue('mobilityLevel')">
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced AI Configuration -->
                                <div class="parameter-group">
                                    <h5><i class="fas fa-microchip me-2"></i>高级AI配置</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="aiModel" class="form-label">AI模型类型</label>
                                                <select class="form-select" id="aiModel">
                                                    <option value="rule_based">基于规则</option>
                                                    <option value="neural_network">神经网络</option>
                                                    <option value="reinforcement_learning" selected>强化学习</option>
                                                    <option value="hybrid">混合模型</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="explorationRate" class="form-label">
                                                    探索率 <span class="range-value" id="explorationRateValue">0.1</span>
                                                </label>
                                                <input type="range" class="range-slider" id="explorationRate" min="0" max="0.5" step="0.01" value="0.1" oninput="updateRangeValue('explorationRate')">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="discountFactor" class="form-label">
                                                    折扣因子 <span class="range-value" id="discountFactorValue">0.9</span>
                                                </label>
                                                <input type="range" class="range-slider" id="discountFactor" min="0.1" max="1" step="0.05" value="0.9" oninput="updateRangeValue('discountFactor')">
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="enableReflection" checked>
                                                    <label class="form-check-label" for="enableReflection">
                                                        启用反思机制
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Agent Preview -->
                    <div id="agentPreview" class="preview-panel" style="display: none;">
                        <h6><i class="fas fa-eye me-2"></i>智能体预览</h6>
                        <div class="agent-preview">
                            <div class="agent-avatar" id="previewAvatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <h5 id="previewName">未命名智能体</h5>
                            <p id="previewDescription" class="text-muted">点击左侧选择智能体类型</p>
                            <div class="row mt-3">
                                <div class="col-4">
                                    <div class="text-center">
                                        <div class="h4 text-primary" id="previewRiskLevel">50%</div>
                                        <small class="text-muted">风险容忍</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-center">
                                        <div class="h4 text-success" id="previewCoopLevel">80%</div>
                                        <small class="text-muted">合作倾向</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="text-center">
                                        <div class="h4 text-info" id="previewAdaptLevel">60%</div>
                                        <small class="text-muted">适应性</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                    </div>
                    <h5>智能体创建成功！</h5>
                    <p class="text-muted mb-0">你的AI智能体已经准备就绪</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">继续设计</button>
                    <button type="button" class="btn btn-success" onclick="runSimulationWithNewAgent()">运行仿真</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedAgentType = null;
        let agentConfig = {};
        let createdAgents = [];

        // 选择智能体类型
        function selectAgentType(type) {
            selectedAgentType = type;
            
            // 更新选中状态
            document.querySelectorAll('.agent-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
            
            // 显示配置表单
            document.getElementById('welcomeScreen').style.display = 'none';
            document.getElementById('agentConfigForm').style.display = 'block';
            document.getElementById('agentPreview').style.display = 'block';
            
            // 加载默认配置
            loadDefaultConfig(type);
            updatePreview();
        }

        // 加载默认配置
        function loadDefaultConfig(type) {
            const defaults = {
                migrant: {
                    name: '海外移民',
                    description: '在海外工作并定期向家乡汇款的移民',
                    riskTolerance: 0.6,
                    cooperativeness: 0.8,
                    wealthLevel: 'middle',
                    remittanceFrequency: 3,
                    icon: 'fas fa-user-friends'
                },
                family: {
                    name: '家乡家庭',
                    description: '接收海外汇款的家庭成员',
                    riskTolerance: 0.3,
                    cooperativeness: 0.9,
                    wealthLevel: 'modest',
                    remittanceFrequency: 0,
                    icon: 'fas fa-home'
                },
                institution: {
                    name: '金融机构',
                    description: '提供汇款服务的银庄或批局',
                    riskTolerance: 0.4,
                    cooperativeness: 0.6,
                    wealthLevel: 'wealthy',
                    remittanceFrequency: 0,
                    icon: 'fas fa-building'
                },
                government: {
                    name: '政策制定者',
                    description: '影响汇款政策的政府机构',
                    riskTolerance: 0.2,
                    cooperativeness: 0.5,
                    wealthLevel: 'very_wealthy',
                    remittanceFrequency: 0,
                    icon: 'fas fa-landmark'
                },
                merchant: {
                    name: '商人',
                    description: '从事贸易活动的商人',
                    riskTolerance: 0.7,
                    cooperativeness: 0.7,
                    wealthLevel: 'wealthy',
                    remittanceFrequency: 6,
                    icon: 'fas fa-handshake'
                }
            };

            const config = defaults[type] || defaults.migrant;
            
            // 填充表单
            document.getElementById('agentName').value = config.name;
            document.getElementById('agentDescription').value = config.description;
            document.getElementById('riskTolerance').value = config.riskTolerance;
            document.getElementById('cooperativeness').value = config.cooperativeness;
            document.getElementById('wealthLevel').value = config.wealthLevel;
            document.getElementById('remittanceFrequency').value = config.remittanceFrequency;
            
            // 更新显示值
            updateAllRangeValues();
            updateAllSelectValues();
            
            agentConfig = { ...config, type: type };
        }

        // 更新范围滑块显示值
        function updateRangeValue(id) {
            const slider = document.getElementById(id);
            const valueSpan = document.getElementById(id + 'Value');
            if (slider && valueSpan) {
                let value = parseFloat(slider.value);
                if (value < 1) {
                    valueSpan.textContent = value.toFixed(2);
                } else {
                    valueSpan.textContent = Math.round(value);
                }
                agentConfig[id] = value;
                updatePreview();
            }
        }

        // 更新选择框显示值
        function updateSelectValue(id) {
            const select = document.getElementById(id);
            const valueSpan = document.getElementById(id + 'Value');
            if (select && valueSpan) {
                const selectedOption = select.options[select.selectedIndex];
                valueSpan.textContent = selectedOption.text;
                agentConfig[id] = select.value;
                updatePreview();
            }
        }

        // 更新所有范围值
        function updateAllRangeValues() {
            const ranges = ['riskTolerance', 'decisionSpeed', 'cooperativeness', 'adaptability', 
                           'learningRate', 'experienceWeight', 'forgettingFactor', 'memoryCapacity',
                           'trustLevel', 'networkSize', 'influenceRadius', 'savingsRate', 
                           'investmentPropensity', 'remittanceFrequency', 'explorationRate', 'discountFactor'];
            ranges.forEach(id => updateRangeValue(id));
        }

        // 更新所有选择值
        function updateAllSelectValues() {
            const selects = ['wealthLevel', 'originRegion', 'destinationRegion', 'aiModel'];
            selects.forEach(id => updateSelectValue(id));
        }

        // 更新预览
        function updatePreview() {
            if (!selectedAgentType) return;
            
            const name = document.getElementById('agentName').value || '未命名智能体';
            const description = document.getElementById('agentDescription').value || '暂无描述';
            const riskTolerance = document.getElementById('riskTolerance').value;
            const cooperativeness = document.getElementById('cooperativeness').value;
            const adaptability = document.getElementById('adaptability').value;
            
            document.getElementById('previewName').textContent = name;
            document.getElementById('previewDescription').textContent = description;
            document.getElementById('previewRiskLevel').textContent = Math.round(riskTolerance * 100) + '%';
            document.getElementById('previewCoopLevel').textContent = Math.round(cooperativeness * 100) + '%';
            document.getElementById('previewAdaptLevel').textContent = Math.round(adaptability * 100) + '%';
            
            // 更新头像图标
            const iconMap = {
                migrant: 'fas fa-user-friends',
                family: 'fas fa-home',
                institution: 'fas fa-building',
                government: 'fas fa-landmark',
                merchant: 'fas fa-handshake'
            };
            
            const avatarIcon = document.querySelector('#previewAvatar i');
            if (avatarIcon) {
                avatarIcon.className = iconMap[selectedAgentType] || 'fas fa-user';
            }
        }

        // 创建智能体
        function createAgent() {
            if (!selectedAgentType) {
                alert('请先选择智能体类型');
                return;
            }

            // 收集所有配置数据
            const agentData = {
                id: generateAgentId(),
                type: selectedAgentType,
                name: document.getElementById('agentName').value,
                description: document.getElementById('agentDescription').value,
                count: parseInt(document.getElementById('agentCount').value),
                lifespan: parseInt(document.getElementById('agentLifespan').value),
                
                // 行为参数
                behavior: {
                    riskTolerance: parseFloat(document.getElementById('riskTolerance').value),
                    decisionSpeed: parseFloat(document.getElementById('decisionSpeed').value),
                    cooperativeness: parseFloat(document.getElementById('cooperativeness').value),
                    adaptability: parseFloat(document.getElementById('adaptability').value)
                },
                
                // 学习配置
                learning: {
                    enabled: document.getElementById('enableLearning').checked,
                    learningRate: parseFloat(document.getElementById('learningRate').value),
                    experienceWeight: parseFloat(document.getElementById('experienceWeight').value),
                    forgettingFactor: parseFloat(document.getElementById('forgettingFactor').value),
                    aiModel: document.getElementById('aiModel').value,
                    explorationRate: parseFloat(document.getElementById('explorationRate').value),
                    discountFactor: parseFloat(document.getElementById('discountFactor').value),
                    enableReflection: document.getElementById('enableReflection').checked
                },
                
                // 记忆配置
                memory: {
                    capacity: parseInt(document.getElementById('memoryCapacity').value),
                    episodic: document.getElementById('episodicMemory').checked,
                    semantic: document.getElementById('semanticMemory').checked,
                    procedural: document.getElementById('proceduralMemory').checked
                },
                
                // 社交配置
                social: {
                    trustLevel: parseFloat(document.getElementById('trustLevel').value),
                    networkSize: parseInt(document.getElementById('networkSize').value),
                    influenceRadius: parseInt(document.getElementById('influenceRadius').value),
                    reputationSystem: document.getElementById('enableReputationSystem').checked,
                    clanConnections: document.getElementById('enableClanConnections').checked
                },
                
                // 经济配置
                economic: {
                    wealthLevel: document.getElementById('wealthLevel').value,
                    savingsRate: parseFloat(document.getElementById('savingsRate').value),
                    investmentPropensity: parseFloat(document.getElementById('investmentPropensity').value),
                    remittanceFrequency: parseInt(document.getElementById('remittanceFrequency').value)
                },
                
                // 地理配置
                geographic: {
                    originRegion: document.getElementById('originRegion').value,
                    destinationRegion: document.getElementById('destinationRegion').value,
                    migrationYear: parseInt(document.getElementById('migrationYear').value),
                    mobilityLevel: parseFloat(document.getElementById('mobilityLevel').value)
                },
                
                createdAt: new Date().toISOString()
            };

            // 保存到本地存储
            saveAgentToStorage(agentData);
            
            // 添加到已创建列表
            createdAgents.push(agentData);
            updateMyAgentsList();
            
            // 显示成功消息
            showSuccessModal(agentData);
            
            console.log('Agent created:', agentData);
        }

        // 生成智能体ID
        function generateAgentId() {
            return 'agent_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 保存智能体到本地存储
        function saveAgentToStorage(agentData) {
            const agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
            agents.push(agentData);
            localStorage.setItem('qiaopi_agents', JSON.stringify(agents));
        }

        // 更新我的智能体列表
        function updateMyAgentsList() {
            const list = document.getElementById('myAgentsList');
            const agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
            
            if (agents.length === 0) {
                list.innerHTML = '<div class="text-muted small p-2">暂无创建的智能体</div>';
                return;
            }
            
            list.innerHTML = '';
            agents.forEach(agent => {
                const item = document.createElement('div');
                item.className = 'list-group-item d-flex justify-content-between align-items-center';
                item.innerHTML = `
                    <div>
                        <small class="fw-bold">${agent.name}</small>
                        <br>
                        <small class="text-muted">${agent.type}</small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="editAgent('${agent.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteAgent('${agent.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                list.appendChild(item);
            });
        }

        // 显示成功模态框
        function showSuccessModal(agentData) {
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
        }

        // 使用新智能体运行仿真
        function runSimulationWithNewAgent() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('successModal'));
            modal.hide();
            
            // 这里应该跳转到仿真运行页面，并传递智能体配置
            alert(`将使用新创建的智能体运行仿真！\n智能体: ${agentConfig.name}\n类型: ${selectedAgentType}`);
            
            // 跳转回主页面的仿真部分
            window.location.href = '/#simulations';
        }

        // 重置设计器
        function resetDesigner() {
            if (confirm('确定要重置所有配置吗？这将清除当前的设计进度。')) {
                selectedAgentType = null;
                agentConfig = {};
                
                document.querySelectorAll('.agent-type-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                document.getElementById('welcomeScreen').style.display = 'block';
                document.getElementById('agentConfigForm').style.display = 'none';
                document.getElementById('agentPreview').style.display = 'none';
                
                document.getElementById('agentForm').reset();
            }
        }

        // 保存智能体设计
        function saveAgentDesign() {
            if (!selectedAgentType) {
                alert('请先配置智能体');
                return;
            }
            
            const designData = {
                type: selectedAgentType,
                config: agentConfig,
                formData: new FormData(document.getElementById('agentForm')),
                savedAt: new Date().toISOString()
            };
            
            localStorage.setItem('qiaopi_agent_design_temp', JSON.stringify(designData));
            
            // 显示保存确认
            const notification = document.createElement('div');
            notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
            notification.innerHTML = `
                <i class="fas fa-check me-2"></i>设计已保存
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.remove(), 3000);
        }

        // 编辑智能体
        function editAgent(agentId) {
            const agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
            const agent = agents.find(a => a.id === agentId);
            
            if (agent) {
                // 加载智能体配置到表单
                selectAgentType(agent.type);
                
                // 填充表单数据
                Object.entries(agent).forEach(([key, value]) => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = value;
                        } else {
                            element.value = value;
                        }
                    }
                });
                
                updatePreview();
            }
        }

        // 删除智能体
        function deleteAgent(agentId) {
            if (confirm('确定要删除这个智能体吗？')) {
                let agents = JSON.parse(localStorage.getItem('qiaopi_agents') || '[]');
                agents = agents.filter(a => a.id !== agentId);
                localStorage.setItem('qiaopi_agents', JSON.stringify(agents));
                updateMyAgentsList();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateMyAgentsList();
            
            // 监听表单变化
            document.getElementById('agentForm').addEventListener('input', updatePreview);
            document.getElementById('agentForm').addEventListener('change', updatePreview);
            
            console.log('Agent Designer initialized');
        });
    </script>
</body>
</html>