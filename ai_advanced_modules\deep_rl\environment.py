"""
侨批网络强化学习环境
Qiaopi Network Reinforcement Learning Environment

实现了用于训练强化学习智能体的环境类，包括：
- 多智能体交互环境
- 奖励函数设计
- 状态转移逻辑
- 性能评估指标
"""

import gymnasium as gym
from gymnasium import spaces
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import logging
from collections import defaultdict
import random

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class EnvironmentConfig:
    """环境配置类"""
    max_steps: int = 1000
    num_migrants: int = 100
    num_families: int = 80
    num_institutions: int = 10
    
    # 奖励函数权重
    reward_weights: Dict[str, float] = None
    
    # 环境参数
    economic_volatility: float = 0.1
    political_stability_base: float = 0.8
    communication_cost_base: float = 0.05
    
    def __post_init__(self):
        if self.reward_weights is None:
            self.reward_weights = {
                'successful_remittance': 1.0,
                'family_satisfaction': 0.8,
                'economic_efficiency': 0.6,
                'network_stability': 0.4,
                'institution_profit': 0.3,
                'social_welfare': 0.5
            }


class QiaopiEnvironment(gym.Env):
    """侨批网络多智能体强化学习环境"""
    
    def __init__(self, config: EnvironmentConfig):
        super().__init__()
        
        self.config = config
        self.current_step = 0
        
        # 环境状态
        self.global_state = self._initialize_global_state()
        self.agent_states = self._initialize_agent_states()
        
        # 定义观测空间（全局观测）
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self._get_global_observation_dim(),),
            dtype=np.float32
        )
        
        # 定义动作空间（多智能体联合动作）
        self.action_space = spaces.Dict({
            'migrants': spaces.MultiDiscrete([10] * config.num_migrants),
            'families': spaces.MultiDiscrete([8] * config.num_families),
            'institutions': spaces.MultiDiscrete([12] * config.num_institutions)
        })
        
        # 性能追踪
        self.performance_metrics = defaultdict(list)
        self.episode_rewards = []
        
        logger.info(f"初始化侨批网络环境：{config.num_migrants}移民，{config.num_families}家庭，{config.num_institutions}机构")
    
    def _initialize_global_state(self) -> Dict[str, Any]:
        """初始化全局环境状态"""
        return {
            'economic_growth_rate': np.random.normal(0.03, 0.02),
            'political_stability': self.config.political_stability_base,
            'exchange_rate': 1.0 + np.random.normal(0, 0.1),
            'communication_cost': self.config.communication_cost_base,
            'transportation_cost': 0.02,
            'regulatory_environment': 0.7,
            'technology_level': 0.5,
            'market_competition': 0.6,
            'cultural_events': np.random.choice([0, 1], p=[0.9, 0.1]),  # 节庆等
            'natural_disasters': np.random.choice([0, 1], p=[0.95, 0.05]),
            'war_status': 0,  # 0=和平, 1=战争
            'trade_routes_status': 0.9  # 贸易路线畅通程度
        }
    
    def _initialize_agent_states(self) -> Dict[str, List[Dict]]:
        """初始化智能体状态"""
        states = {
            'migrants': [],
            'families': [],
            'institutions': []
        }
        
        # 初始化移民状态
        for i in range(self.config.num_migrants):
            migrant_state = {
                'agent_id': f'migrant_{i}',
                'income_level': np.random.uniform(50, 200),
                'savings': np.random.uniform(10, 500),
                'years_abroad': np.random.randint(1, 20),
                'social_capital': np.random.uniform(0.1, 0.8),
                'health_status': np.random.uniform(0.7, 1.0),
                'family_connection_strength': np.random.uniform(0.3, 0.9),
                'last_remittance_amount': 0.0,
                'last_remittance_time': np.random.randint(0, 12),
                'success_rate_personal': 0.8,
                'preferred_institution': np.random.randint(0, self.config.num_institutions),
                'risk_tolerance': np.random.uniform(0.2, 0.8)
            }
            states['migrants'].append(migrant_state)
        
        # 初始化家庭状态
        for i in range(self.config.num_families):
            family_state = {
                'agent_id': f'family_{i}',
                'cash_reserves': np.random.uniform(5, 100),
                'family_size': np.random.randint(3, 8),
                'dependency_ratio': np.random.uniform(0.2, 0.7),
                'monthly_expenses': np.random.uniform(20, 80),
                'urgent_need_level': np.random.uniform(0, 0.3),
                'education_investment': np.random.uniform(0, 50),
                'property_value': np.random.uniform(0, 1000),
                'debt_level': np.random.uniform(0, 200),
                'social_status': np.random.uniform(0.1, 0.9),
                'remittance_expectation': np.random.uniform(30, 150),
                'satisfaction_level': 0.7
            }
            states['families'].append(family_state)
        
        # 初始化机构状态
        for i in range(self.config.num_institutions):
            institution_state = {
                'agent_id': f'institution_{i}',
                'liquidity': np.random.uniform(1000, 10000),
                'transaction_volume': 0.0,
                'success_rate': np.random.uniform(0.7, 0.95),
                'fee_rate': np.random.uniform(0.03, 0.08),
                'market_share': 1.0 / self.config.num_institutions,
                'customer_count': 0,
                'revenue': 0.0,
                'operational_cost': np.random.uniform(100, 500),
                'trust_rating': np.random.uniform(0.6, 0.9),
                'technology_level': np.random.uniform(0.3, 0.8),
                'network_coverage': np.random.uniform(0.4, 0.9),
                'risk_management_score': np.random.uniform(0.5, 0.9)
            }
            states['institutions'].append(institution_state)
        
        return states
    
    def _get_global_observation_dim(self) -> int:
        """计算全局观测维度"""
        # 全局状态维度
        global_dim = len(self.global_state)
        
        # 聚合智能体状态维度
        migrant_agg_dim = 10  # 移民群体聚合特征
        family_agg_dim = 8   # 家庭群体聚合特征
        institution_agg_dim = 8  # 机构群体聚合特征
        
        # 网络特征维度
        network_dim = 5
        
        return global_dim + migrant_agg_dim + family_agg_dim + institution_agg_dim + network_dim
    
    def _get_observation(self) -> np.ndarray:
        """获取当前观测"""
        obs = []
        
        # 全局状态
        obs.extend(list(self.global_state.values()))
        
        # 移民群体聚合特征
        migrant_states = self.agent_states['migrants']
        migrant_agg = [
            np.mean([m['income_level'] for m in migrant_states]),
            np.mean([m['savings'] for m in migrant_states]),
            np.mean([m['social_capital'] for m in migrant_states]),
            np.mean([m['success_rate_personal'] for m in migrant_states]),
            np.std([m['income_level'] for m in migrant_states]),
            len([m for m in migrant_states if m['last_remittance_time'] <= 3]),  # 最近汇款数量
            np.mean([m['family_connection_strength'] for m in migrant_states]),
            np.mean([m['risk_tolerance'] for m in migrant_states]),
            np.mean([m['years_abroad'] for m in migrant_states]),
            np.mean([m['health_status'] for m in migrant_states])
        ]
        obs.extend(migrant_agg)
        
        # 家庭群体聚合特征
        family_states = self.agent_states['families']
        family_agg = [
            np.mean([f['cash_reserves'] for f in family_states]),
            np.mean([f['family_size'] for f in family_states]),
            np.mean([f['urgent_need_level'] for f in family_states]),
            np.mean([f['satisfaction_level'] for f in family_states]),
            np.mean([f['debt_level'] for f in family_states]),
            np.mean([f['education_investment'] for f in family_states]),
            len([f for f in family_states if f['urgent_need_level'] > 0.5]),  # 急需帮助家庭数
            np.mean([f['social_status'] for f in family_states])
        ]
        obs.extend(family_agg)
        
        # 机构群体聚合特征
        institution_states = self.agent_states['institutions']
        institution_agg = [
            np.mean([i['liquidity'] for i in institution_states]),
            np.mean([i['success_rate'] for i in institution_states]),
            np.mean([i['fee_rate'] for i in institution_states]),
            np.mean([i['trust_rating'] for i in institution_states]),
            np.sum([i['transaction_volume'] for i in institution_states]),
            np.mean([i['technology_level'] for i in institution_states]),
            np.std([i['market_share'] for i in institution_states]),  # 市场集中度
            np.mean([i['risk_management_score'] for i in institution_states])
        ]
        obs.extend(institution_agg)
        
        # 网络特征
        network_features = [
            self._calculate_network_density(),
            self._calculate_system_efficiency(),
            self._calculate_trust_index(),
            self._calculate_financial_inclusion(),
            self._calculate_system_resilience()
        ]
        obs.extend(network_features)
        
        return np.array(obs, dtype=np.float32)
    
    def step(self, actions: Dict[str, np.ndarray]) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """执行一步环境动态"""
        self.current_step += 1
        
        # 解析动作
        migrant_actions = actions['migrants']
        family_actions = actions['families']
        institution_actions = actions['institutions']
        
        # 处理智能体交互
        interactions = self._process_agent_interactions(
            migrant_actions, family_actions, institution_actions
        )
        
        # 更新智能体状态
        self._update_agent_states(interactions)
        
        # 更新全局环境状态
        self._update_global_state()
        
        # 计算奖励
        reward = self._calculate_reward(interactions)
        
        # 检查终止条件
        terminated = self.current_step >= self.config.max_steps
        truncated = False
        
        # 收集性能指标
        self._collect_performance_metrics(interactions)
        
        # 获取新观测
        observation = self._get_observation()
        
        # 构建信息字典
        info = {
            'step': self.current_step,
            'interactions': len(interactions),
            'system_efficiency': self._calculate_system_efficiency(),
            'network_stability': self._calculate_network_stability(),
            'economic_welfare': self._calculate_economic_welfare()
        }
        
        return observation, reward, terminated, truncated, info
    
    def _process_agent_interactions(
        self, 
        migrant_actions: np.ndarray,
        family_actions: np.ndarray,
        institution_actions: np.ndarray
    ) -> List[Dict]:
        """处理智能体间的交互"""
        interactions = []
        
        # 处理汇款交互
        for i, migrant_action in enumerate(migrant_actions):
            migrant = self.agent_states['migrants'][i]
            
            # 解码移民动作
            action_type = self._decode_migrant_action(migrant_action)
            
            if action_type['type'] not in ['no_remittance', 'wait_and_observe']:
                # 选择接收家庭（简化：随机选择或基于历史关系）
                family_idx = i % len(self.agent_states['families'])
                family = self.agent_states['families'][family_idx]
                
                # 选择机构
                institution_idx = migrant.get('preferred_institution', 0)
                institution = self.agent_states['institutions'][institution_idx]
                
                # 计算汇款金额
                amount = self._calculate_remittance_amount(migrant, family, action_type)
                
                if amount > 0 and migrant['savings'] >= amount:
                    # 创建交互记录
                    interaction = {
                        'type': 'remittance',
                        'migrant_id': migrant['agent_id'],
                        'family_id': family['agent_id'],
                        'institution_id': institution['agent_id'],
                        'amount': amount,
                        'action_type': action_type,
                        'success_probability': self._calculate_success_probability(
                            migrant, family, institution
                        ),
                        'fee': amount * institution['fee_rate'],
                        'processing_time': self._calculate_processing_time(institution)
                    }
                    
                    # 模拟成功/失败
                    interaction['success'] = (
                        np.random.random() < interaction['success_probability']
                    )
                    
                    interactions.append(interaction)
        
        # 处理机构策略调整
        for i, institution_action in enumerate(institution_actions):
            institution = self.agent_states['institutions'][i]
            action_type = self._decode_institution_action(institution_action)
            
            # 应用机构策略
            self._apply_institution_strategy(institution, action_type)
        
        return interactions
    
    def _update_agent_states(self, interactions: List[Dict]):
        """基于交互结果更新智能体状态"""
        
        # 更新移民状态
        for interaction in interactions:
            if interaction['type'] == 'remittance':
                # 更新发送方（移民）
                migrant_id = interaction['migrant_id']
                migrant_idx = int(migrant_id.split('_')[1])
                migrant = self.agent_states['migrants'][migrant_idx]
                
                # 扣除汇款金额和费用
                total_cost = interaction['amount'] + interaction['fee']
                migrant['savings'] -= total_cost
                migrant['last_remittance_amount'] = interaction['amount']
                migrant['last_remittance_time'] = 0
                
                # 更新个人成功率
                if interaction['success']:
                    migrant['success_rate_personal'] = min(0.95, 
                        migrant['success_rate_personal'] * 1.01)
                else:
                    migrant['success_rate_personal'] = max(0.5,
                        migrant['success_rate_personal'] * 0.98)
                
                # 更新接收方（家庭）
                if interaction['success']:
                    family_id = interaction['family_id']
                    family_idx = int(family_id.split('_')[1])
                    family = self.agent_states['families'][family_idx]
                    
                    family['cash_reserves'] += interaction['amount']
                    family['satisfaction_level'] = min(1.0,
                        family['satisfaction_level'] + 0.1)
                    
                    # 减少紧急需求
                    family['urgent_need_level'] = max(0,
                        family['urgent_need_level'] - 0.2)
                else:
                    # 失败时增加不满和急需程度
                    family_id = interaction['family_id']
                    family_idx = int(family_id.split('_')[1])
                    family = self.agent_states['families'][family_idx]
                    
                    family['satisfaction_level'] = max(0,
                        family['satisfaction_level'] - 0.05)
                    family['urgent_need_level'] = min(1.0,
                        family['urgent_need_level'] + 0.1)
                
                # 更新机构状态
                institution_id = interaction['institution_id']
                institution_idx = int(institution_id.split('_')[1])
                institution = self.agent_states['institutions'][institution_idx]
                
                institution['transaction_volume'] += interaction['amount']
                institution['customer_count'] += 1
                
                if interaction['success']:
                    institution['revenue'] += interaction['fee']
                    institution['success_rate'] = min(0.98,
                        institution['success_rate'] * 1.001)
                    institution['trust_rating'] = min(0.95,
                        institution['trust_rating'] * 1.001)
                else:
                    institution['revenue'] -= interaction['amount'] * 0.1  # 损失
                    institution['success_rate'] = max(0.6,
                        institution['success_rate'] * 0.999)
                    institution['trust_rating'] = max(0.4,
                        institution['trust_rating'] * 0.998)
        
        # 自然状态演化
        self._natural_state_evolution()
    
    def _natural_state_evolution(self):
        """自然状态演化（时间推移效应）"""
        
        # 移民状态演化
        for migrant in self.agent_states['migrants']:
            # 收入自然增长
            migrant['income_level'] *= (1 + np.random.normal(0.01, 0.05))
            migrant['income_level'] = max(10, migrant['income_level'])
            
            # 储蓄自然积累
            monthly_savings = migrant['income_level'] * 0.3
            migrant['savings'] += monthly_savings
            
            # 社会资本增长
            migrant['social_capital'] = min(1.0, 
                migrant['social_capital'] + np.random.uniform(0, 0.01))
            
            # 时间流逝
            migrant['last_remittance_time'] += 1
        
        # 家庭状态演化
        for family in self.agent_states['families']:
            # 月度消费
            monthly_consumption = family['monthly_expenses']
            family['cash_reserves'] = max(0, 
                family['cash_reserves'] - monthly_consumption)
            
            # 紧急需求自然产生
            if np.random.random() < 0.05:  # 5%概率
                family['urgent_need_level'] = min(1.0,
                    family['urgent_need_level'] + np.random.uniform(0.1, 0.3))
            
            # 满意度自然衰减
            family['satisfaction_level'] = max(0,
                family['satisfaction_level'] - 0.01)
        
        # 机构状态演化
        for institution in self.agent_states['institutions']:
            # 运营成本
            institution['revenue'] -= institution['operational_cost']
            
            # 市场竞争压力
            if institution['market_share'] > 1.5 / len(self.agent_states['institutions']):
                # 高市场份额面临更大竞争压力
                institution['fee_rate'] = max(0.02,
                    institution['fee_rate'] - 0.001)
    
    def _update_global_state(self):
        """更新全局环境状态"""
        # 经济波动
        self.global_state['economic_growth_rate'] += np.random.normal(0, 0.01)
        self.global_state['economic_growth_rate'] = np.clip(
            self.global_state['economic_growth_rate'], -0.1, 0.1)
        
        # 政治稳定性变化
        if np.random.random() < 0.02:  # 2%概率发生政治事件
            shock = np.random.uniform(-0.2, 0.1)
            self.global_state['political_stability'] += shock
            self.global_state['political_stability'] = np.clip(
                self.global_state['political_stability'], 0.1, 1.0)
        
        # 汇率波动
        self.global_state['exchange_rate'] *= (1 + np.random.normal(0, 0.02))
        self.global_state['exchange_rate'] = max(0.5, self.global_state['exchange_rate'])
        
        # 技术进步
        self.global_state['technology_level'] = min(1.0,
            self.global_state['technology_level'] + 0.001)
        
        # 通信成本随技术进步下降
        tech_factor = 1 - self.global_state['technology_level'] * 0.5
        self.global_state['communication_cost'] = (
            self.config.communication_cost_base * tech_factor
        )
    
    def _calculate_reward(self, interactions: List[Dict]) -> float:
        """计算系统奖励"""
        reward_components = {}
        weights = self.config.reward_weights
        
        # 成功汇款奖励
        successful_remittances = sum(1 for i in interactions 
                                   if i.get('success', False))
        total_remittances = len(interactions)
        success_rate = successful_remittances / max(1, total_remittances)
        reward_components['successful_remittance'] = success_rate * weights['successful_remittance']
        
        # 家庭满意度奖励
        avg_family_satisfaction = np.mean([f['satisfaction_level'] 
                                         for f in self.agent_states['families']])
        reward_components['family_satisfaction'] = avg_family_satisfaction * weights['family_satisfaction']
        
        # 经济效率奖励
        system_efficiency = self._calculate_system_efficiency()
        reward_components['economic_efficiency'] = system_efficiency * weights['economic_efficiency']
        
        # 网络稳定性奖励
        network_stability = self._calculate_network_stability()
        reward_components['network_stability'] = network_stability * weights['network_stability']
        
        # 机构盈利能力奖励
        avg_institution_profit = np.mean([max(0, i['revenue'] - i['operational_cost'])
                                        for i in self.agent_states['institutions']])
        normalized_profit = np.tanh(avg_institution_profit / 1000)  # 归一化
        reward_components['institution_profit'] = normalized_profit * weights['institution_profit']
        
        # 社会福利奖励
        social_welfare = self._calculate_social_welfare()
        reward_components['social_welfare'] = social_welfare * weights['social_welfare']
        
        # 计算总奖励
        total_reward = sum(reward_components.values())
        
        # 添加惩罚项
        penalties = self._calculate_penalties()
        total_reward -= penalties
        
        return total_reward
    
    def _calculate_system_efficiency(self) -> float:
        """计算系统效率"""
        # 基于处理速度、成本、成功率的综合指标
        avg_success_rate = np.mean([i['success_rate'] for i in self.agent_states['institutions']])
        avg_fee_rate = np.mean([i['fee_rate'] for i in self.agent_states['institutions']])
        
        # 效率 = 成功率 / 费率（归一化）
        efficiency = avg_success_rate / (avg_fee_rate * 10)  # 归一化
        return min(1.0, efficiency)
    
    def _calculate_network_stability(self) -> float:
        """计算网络稳定性"""
        # 基于智能体状态变化的方差
        migrant_savings_std = np.std([m['savings'] for m in self.agent_states['migrants']])
        family_satisfaction_std = np.std([f['satisfaction_level'] for f in self.agent_states['families']])
        institution_performance_std = np.std([i['success_rate'] for i in self.agent_states['institutions']])
        
        # 稳定性与变异程度负相关
        stability = 1.0 / (1.0 + migrant_savings_std/100 + family_satisfaction_std + institution_performance_std)
        return stability
    
    def _calculate_social_welfare(self) -> float:
        """计算社会福利"""
        # 综合考虑财富分配、满意度、可及性
        
        # 财富分配公平性（基尼系数的简化版）
        migrant_savings = [m['savings'] for m in self.agent_states['migrants']]
        wealth_gini = self._calculate_gini_coefficient(migrant_savings)
        wealth_equality = 1 - wealth_gini
        
        # 整体满意度
        avg_satisfaction = np.mean([f['satisfaction_level'] for f in self.agent_states['families']])
        
        # 服务可及性
        accessibility = self._calculate_service_accessibility()
        
        social_welfare = (wealth_equality + avg_satisfaction + accessibility) / 3
        return social_welfare
    
    def _calculate_penalties(self) -> float:
        """计算惩罚项"""
        penalties = 0.0
        
        # 系统不稳定惩罚
        if self._calculate_network_stability() < 0.3:
            penalties += 0.5
        
        # 高失败率惩罚
        avg_success_rate = np.mean([i['success_rate'] for i in self.agent_states['institutions']])
        if avg_success_rate < 0.6:
            penalties += (0.6 - avg_success_rate) * 2
        
        # 过度集中惩罚
        market_shares = [i['market_share'] for i in self.agent_states['institutions']]
        max_share = max(market_shares)
        if max_share > 0.5:  # 超过50%市场份额
            penalties += (max_share - 0.5) * 1.5
        
        return penalties
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """重置环境"""
        super().reset(seed=seed)
        
        self.current_step = 0
        self.global_state = self._initialize_global_state()
        self.agent_states = self._initialize_agent_states()
        self.performance_metrics = defaultdict(list)
        
        observation = self._get_observation()
        info = {'step': 0, 'reset': True}
        
        logger.info("环境已重置")
        return observation, info
    
    def render(self, mode: str = 'human'):
        """渲染环境状态"""
        if mode == 'human':
            print(f"\n=== 环境状态 (步骤 {self.current_step}) ===")
            print(f"经济增长率: {self.global_state['economic_growth_rate']:.3f}")
            print(f"政治稳定性: {self.global_state['political_stability']:.3f}")
            print(f"系统效率: {self._calculate_system_efficiency():.3f}")
            print(f"网络稳定性: {self._calculate_network_stability():.3f}")
            
            # 智能体统计
            avg_migrant_savings = np.mean([m['savings'] for m in self.agent_states['migrants']])
            avg_family_satisfaction = np.mean([f['satisfaction_level'] for f in self.agent_states['families']])
            avg_institution_success = np.mean([i['success_rate'] for i in self.agent_states['institutions']])
            
            print(f"平均移民储蓄: {avg_migrant_savings:.2f}")
            print(f"平均家庭满意度: {avg_family_satisfaction:.3f}")
            print(f"平均机构成功率: {avg_institution_success:.3f}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            'total_steps': self.current_step,
            'system_efficiency': self._calculate_system_efficiency(),
            'network_stability': self._calculate_network_stability(),
            'social_welfare': self._calculate_social_welfare(),
            'average_success_rate': np.mean([i['success_rate'] for i in self.agent_states['institutions']]),
            'total_transaction_volume': sum([i['transaction_volume'] for i in self.agent_states['institutions']]),
            'performance_metrics': dict(self.performance_metrics)
        }
    
    # 辅助方法
    def _decode_migrant_action(self, action: int) -> Dict[str, Any]:
        """解码移民动作"""
        action_mapping = {
            0: {"type": "no_remittance"},
            1: {"type": "small_remittance", "amount_factor": 0.1},
            2: {"type": "medium_remittance", "amount_factor": 0.3},
            3: {"type": "large_remittance", "amount_factor": 0.6},
            4: {"type": "emergency_remittance", "amount_factor": 0.8},
            5: {"type": "investment_remittance", "amount_factor": 0.5},
            6: {"type": "festival_remittance", "amount_factor": 0.4},
            7: {"type": "change_institution"},
            8: {"type": "explore_new_institution"},
            9: {"type": "wait_and_observe"}
        }
        return action_mapping.get(action, action_mapping[0])
    
    def _decode_institution_action(self, action: int) -> Dict[str, Any]:
        """解码机构动作"""
        action_mapping = {
            0: {"type": "maintain_status"},
            1: {"type": "reduce_fees", "change": -0.005},
            2: {"type": "increase_fees", "change": 0.003},
            3: {"type": "improve_service", "investment": 0.1},
            4: {"type": "expand_network", "investment": 0.15},
            5: {"type": "enhance_security", "investment": 0.08},
            6: {"type": "marketing_campaign", "investment": 0.05},
            7: {"type": "technology_upgrade", "investment": 0.12},
            8: {"type": "risk_management"},
            9: {"type": "partnership"},
            10: {"type": "differentiation"},
            11: {"type": "cost_optimization"}
        }
        return action_mapping.get(action, action_mapping[0])
    
    def _calculate_remittance_amount(self, migrant: Dict, family: Dict, action_type: Dict) -> float:
        """计算汇款金额"""
        base_amount = migrant['income_level'] * action_type.get('amount_factor', 0.3)
        
        # 考虑家庭紧急程度
        urgency_multiplier = 1 + family['urgent_need_level']
        
        # 考虑移民储蓄能力
        savings_limit = migrant['savings'] * 0.8
        
        final_amount = min(base_amount * urgency_multiplier, savings_limit)
        return max(0, final_amount)
    
    def _calculate_success_probability(self, migrant: Dict, family: Dict, institution: Dict) -> float:
        """计算交易成功概率"""
        base_prob = institution['success_rate']
        
        # 环境因素
        political_factor = self.global_state['political_stability']
        economic_factor = 1 + self.global_state['economic_growth_rate']
        
        # 智能体因素
        migrant_reliability = migrant['success_rate_personal']
        institution_trust = institution['trust_rating']
        
        final_prob = base_prob * political_factor * economic_factor * migrant_reliability * institution_trust
        return min(0.98, max(0.02, final_prob))
    
    def _calculate_processing_time(self, institution: Dict) -> int:
        """计算处理时间"""
        base_time = 7  # 基础7天
        tech_factor = institution['technology_level']
        efficiency_factor = institution.get('efficiency_score', 0.7)
        
        processing_time = base_time * (2 - tech_factor) * (2 - efficiency_factor)
        return max(1, int(processing_time))
    
    def _apply_institution_strategy(self, institution: Dict, action_type: Dict):
        """应用机构策略"""
        if action_type['type'] == 'reduce_fees':
            institution['fee_rate'] = max(0.01, 
                institution['fee_rate'] + action_type.get('change', -0.005))
        elif action_type['type'] == 'increase_fees':
            institution['fee_rate'] = min(0.15,
                institution['fee_rate'] + action_type.get('change', 0.003))
        elif action_type['type'] == 'improve_service':
            investment = action_type.get('investment', 0.1)
            institution['operational_cost'] += investment * 100
            institution['success_rate'] = min(0.98,
                institution['success_rate'] + investment * 0.1)
        elif action_type['type'] == 'technology_upgrade':
            investment = action_type.get('investment', 0.12)
            institution['operational_cost'] += investment * 150
            institution['technology_level'] = min(1.0,
                institution['technology_level'] + investment)
    
    def _calculate_network_density(self) -> float:
        """计算网络密度"""
        # 简化的网络密度指标
        active_connections = 0
        total_possible = len(self.agent_states['migrants']) * len(self.agent_states['families'])
        
        # 计算活跃连接数（基于最近交互）
        for migrant in self.agent_states['migrants']:
            if migrant['last_remittance_time'] <= 6:  # 最近6个月内有汇款
                active_connections += 1
        
        return active_connections / max(1, total_possible)
    
    def _calculate_trust_index(self) -> float:
        """计算信任指数"""
        institution_trust = np.mean([i['trust_rating'] for i in self.agent_states['institutions']])
        migrant_confidence = np.mean([m['success_rate_personal'] for m in self.agent_states['migrants']])
        family_satisfaction = np.mean([f['satisfaction_level'] for f in self.agent_states['families']])
        
        return (institution_trust + migrant_confidence + family_satisfaction) / 3
    
    def _calculate_financial_inclusion(self) -> float:
        """计算金融包容性"""
        # 基于服务覆盖率和可负担性
        affordable_services = sum(1 for i in self.agent_states['institutions'] 
                                 if i['fee_rate'] <= 0.06)
        total_institutions = len(self.agent_states['institutions'])
        
        affordability = affordable_services / max(1, total_institutions)
        
        # 服务可及性
        accessibility = self._calculate_service_accessibility()
        
        return (affordability + accessibility) / 2
    
    def _calculate_service_accessibility(self) -> float:
        """计算服务可及性"""
        # 基于机构网络覆盖率
        avg_coverage = np.mean([i['network_coverage'] for i in self.agent_states['institutions']])
        return avg_coverage
    
    def _calculate_system_resilience(self) -> float:
        """计算系统韧性"""
        # 基于多样性和冗余性
        institution_diversity = 1 - np.var([i['market_share'] for i in self.agent_states['institutions']])
        technological_diversity = np.std([i['technology_level'] for i in self.agent_states['institutions']])
        
        resilience = (institution_diversity + min(1.0, technological_diversity)) / 2
        return resilience
    
    def _calculate_gini_coefficient(self, values: List[float]) -> float:
        """计算基尼系数"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        cumsum = np.cumsum(sorted_values)
        
        return (n + 1 - 2 * sum((n + 1 - i) * v for i, v in enumerate(sorted_values, 1))) / (n * sum(sorted_values))
    
    def _collect_performance_metrics(self, interactions: List[Dict]):
        """收集性能指标"""
        self.performance_metrics['step'].append(self.current_step)
        self.performance_metrics['interactions_count'].append(len(interactions))
        self.performance_metrics['success_rate'].append(
            sum(1 for i in interactions if i.get('success', False)) / max(1, len(interactions))
        )
        self.performance_metrics['system_efficiency'].append(self._calculate_system_efficiency())
        self.performance_metrics['network_stability'].append(self._calculate_network_stability())
        self.performance_metrics['social_welfare'].append(self._calculate_social_welfare())


# 使用示例
if __name__ == "__main__":
    # 创建环境配置
    config = EnvironmentConfig(
        max_steps=100,
        num_migrants=20,
        num_families=15,
        num_institutions=3
    )
    
    # 创建环境
    env = QiaopiEnvironment(config)
    
    # 重置环境
    obs, info = env.reset()
    print(f"初始观测维度: {obs.shape}")
    
    # 运行几步
    for step in range(5):
        # 随机动作
        actions = {
            'migrants': np.random.randint(0, 10, size=config.num_migrants),
            'families': np.random.randint(0, 8, size=config.num_families),
            'institutions': np.random.randint(0, 12, size=config.num_institutions)
        }
        
        obs, reward, terminated, truncated, info = env.step(actions)
        
        print(f"步骤 {step+1}: 奖励={reward:.3f}, 信息={info}")
        
        if terminated or truncated:
            break
    
    # 获取性能摘要
    summary = env.get_performance_summary()
    print(f"\n性能摘要: {summary}")
