// 分析模块可视化脚本

// 全局变量
let analysisData = null;
let charts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAnalysisData();
    initializeEventListeners();
});

// 加载分析数据
async function loadAnalysisData() {
    try {
        // 从服务器获取分析结果
        const response = await fetch('/api/enhanced/results');
        const data = await response.json();
        
        if (data.available && data.results) {
            analysisData = data.results;
            
            // 初始化各个可视化模块
            initMultiTimeseries();
            initTrendAnalysis();
            initCorrelationAnalysis();
            initClusteringAnalysis();
            initPredictionAnalysis();
            initNetworkAnalysis();
            initEventImpactAnalysis();
        } else {
            // 如果没有数据，生成演示数据
            await generateDemoData();
        }
    } catch (error) {
        console.error('加载分析数据失败:', error);
        // 使用演示数据
        await generateDemoData();
    }
}

// 生成演示数据
async function generateDemoData() {
    try {
        const response = await fetch('/api/enhanced/generate-demo', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });
        
        const result = await response.json();
        if (result.success) {
            // 重新加载数据
            await loadAnalysisData();
        }
    } catch (error) {
        console.error('生成演示数据失败:', error);
        // 使用本地演示数据
        useLocalDemoData();
    }
}

// 使用本地演示数据
function useLocalDemoData() {
    analysisData = {
        enhanced_analysis: {
            multi_timeseries_analysis: generateLocalTimeseries(),
            trend_analysis: generateLocalTrends(),
            correlation_analysis: generateLocalCorrelation(),
            clustering_analysis: generateLocalClustering(),
            prediction_analysis: generateLocalPrediction(),
            network_topology_analysis: generateLocalNetwork(),
            event_impact_analysis: generateLocalEventImpact()
        }
    };
    
    // 初始化各个可视化模块
    initMultiTimeseries();
    initTrendAnalysis();
    initCorrelationAnalysis();
    initClusteringAnalysis();
    initPredictionAnalysis();
    initNetworkAnalysis();
    initEventImpactAnalysis();
}

// 1. 多时间序列分析可视化
function initMultiTimeseries() {
    const ctx = document.getElementById('multiTimeseriesChart');
    if (!ctx) return;
    
    // 准备数据
    const years = Array.from({length: 21}, (_, i) => 1920 + i);
    const datasets = [
        {
            label: '广东',
            data: years.map((_, i) => 100 + i * 5 + Math.random() * 10),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.4
        },
        {
            label: '福建',
            data: years.map((_, i) => 80 + i * 4 + Math.random() * 8),
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.4
        },
        {
            label: '浙江',
            data: years.map((_, i) => 60 + i * 3 + Math.random() * 6),
            borderColor: 'rgb(255, 205, 86)',
            backgroundColor: 'rgba(255, 205, 86, 0.1)',
            tension: 0.4
        }
    ];
    
    if (charts.multiTimeseries) {
        charts.multiTimeseries.destroy();
    }
    
    charts.multiTimeseries = new Chart(ctx, {
        type: 'line',
        data: {
            labels: years,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '各地区汇款流量趋势'
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '年份'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '流量数量'
                    }
                }
            }
        }
    });
}

// 2. 趋势分析可视化
function initTrendAnalysis() {
    const ctx = document.getElementById('trendChart');
    if (!ctx) return;
    
    const years = Array.from({length: 21}, (_, i) => 1920 + i);
    const trendData = years.map((_, i) => 100 + i * 10 + Math.sin(i * 0.5) * 20);
    const trendLine = years.map((_, i) => 100 + i * 10); // 趋势线
    
    if (charts.trend) {
        charts.trend.destroy();
    }
    
    charts.trend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: years,
            datasets: [
                {
                    label: '实际值',
                    data: trendData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    fill: true
                },
                {
                    label: '趋势线',
                    data: trendLine,
                    borderColor: 'rgb(255, 99, 132)',
                    borderDash: [5, 5],
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '汇款总量趋势分析'
                }
            }
        }
    });
}

// 3. 相关性分析可视化
function initCorrelationAnalysis() {
    const container = document.getElementById('correlationHeatmap');
    if (!container) return;
    
    // 生成相关性矩阵数据
    const variables = ['流量', '金额', '成功率', '储蓄', '收入'];
    const correlationMatrix = [
        [1.00, 0.85, 0.72, 0.68, 0.45],
        [0.85, 1.00, 0.78, 0.71, 0.52],
        [0.72, 0.78, 1.00, 0.65, 0.38],
        [0.68, 0.71, 0.65, 1.00, 0.82],
        [0.45, 0.52, 0.38, 0.82, 1.00]
    ];
    
    const data = [{
        z: correlationMatrix,
        x: variables,
        y: variables,
        type: 'heatmap',
        colorscale: 'RdBu',
        reversescale: true,
        showscale: true,
        hoverongaps: false,
        hovertemplate: '%{x} - %{y}<br>相关系数: %{z:.2f}<extra></extra>'
    }];
    
    const layout = {
        title: '变量相关性矩阵',
        xaxis: { title: '' },
        yaxis: { title: '', autorange: 'reversed' },
        width: null,
        height: 400,
        margin: { t: 50, l: 100, r: 50, b: 100 }
    };
    
    Plotly.newPlot(container, data, layout, {responsive: true});
    
    // 填充相关性表格
    fillCorrelationTable();
}

// 填充相关性表格
function fillCorrelationTable() {
    const tbody = document.getElementById('correlationTable');
    if (!tbody) return;
    
    const correlations = [
        { pair: '流量 - 金额', coef: 0.85, sig: '***', desc: '强正相关，流量增加时金额显著增加' },
        { pair: '储蓄 - 收入', coef: 0.82, sig: '***', desc: '强正相关，收入水平直接影响储蓄能力' },
        { pair: '流量 - 成功率', coef: 0.72, sig: '**', desc: '中等正相关，流量增加时成功率提升' },
        { pair: '金额 - 储蓄', coef: 0.71, sig: '**', desc: '中等正相关，汇款金额与储蓄水平相关' }
    ];
    
    tbody.innerHTML = correlations.map(item => `
        <tr>
            <td>${item.pair}</td>
            <td><strong>${item.coef}</strong></td>
            <td>${item.sig}</td>
            <td><small>${item.desc}</small></td>
        </tr>
    `).join('');
}

// 4. 聚类分析可视化
function initClusteringAnalysis() {
    // 饼图 - 群体分布
    const ctx = document.getElementById('clusterChart');
    if (ctx) {
        if (charts.cluster) {
            charts.cluster.destroy();
        }
        
        charts.cluster = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['保守型', '平衡型', '积极型'],
                datasets: [{
                    data: [120, 100, 80],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(255, 99, 132, 0.8)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '移民行为群体分布'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // 散点图 - 群体分布
    const scatterContainer = document.getElementById('clusterScatter');
    if (scatterContainer) {
        // 生成散点数据
        const clusters = [
            { name: '保守型', color: 'rgba(54, 162, 235, 0.6)', size: 120 },
            { name: '平衡型', color: 'rgba(255, 206, 86, 0.6)', size: 100 },
            { name: '积极型', color: 'rgba(255, 99, 132, 0.6)', size: 80 }
        ];
        
        const traces = clusters.map((cluster, idx) => {
            const x = Array.from({length: cluster.size}, () => Math.random() * 10 + idx * 15);
            const y = Array.from({length: cluster.size}, () => Math.random() * 10 + idx * 10);
            
            return {
                x: x,
                y: y,
                mode: 'markers',
                name: cluster.name,
                marker: {
                    color: cluster.color,
                    size: 8
                }
            };
        });
        
        const layout = {
            title: '群体特征分布图',
            xaxis: { title: '风险偏好' },
            yaxis: { title: '汇款频率' },
            height: 400
        };
        
        Plotly.newPlot(scatterContainer, traces, layout, {responsive: true});
    }
    
    // 填充群体描述
    fillClusterProfiles();
}

// 填充群体描述
function fillClusterProfiles() {
    const container = document.getElementById('clusterProfiles');
    if (!container) return;
    
    const profiles = [
        {
            name: '保守型',
            size: 120,
            color: '#36a2eb',
            traits: ['低风险偏好', '定期汇款', '家庭导向']
        },
        {
            name: '平衡型',
            size: 100,
            color: '#ffce56',
            traits: ['中等风险', '稳定频率', '储蓄意识']
        },
        {
            name: '积极型',
            size: 80,
            color: '#ff6384',
            traits: ['高风险偏好', '大额汇款', '投资导向']
        }
    ];
    
    container.innerHTML = profiles.map(profile => `
        <div class="mb-3 p-3" style="background: ${profile.color}20; border-left: 4px solid ${profile.color};">
            <h6>${profile.name} (${profile.size}人)</h6>
            <small>${profile.traits.join(' · ')}</small>
        </div>
    `).join('');
}

// 5. 预测分析可视化
function initPredictionAnalysis() {
    const ctx = document.getElementById('predictionChart');
    if (!ctx) return;
    
    // 历史数据 + 预测数据
    const historicalYears = Array.from({length: 21}, (_, i) => 1920 + i);
    const futureYears = Array.from({length: 5}, (_, i) => 1941 + i);
    const allYears = [...historicalYears, ...futureYears];
    
    const historicalData = historicalYears.map((_, i) => 10000 + i * 500);
    const predictionData = new Array(21).fill(null).concat([15000, 16500, 18000, 19500, 21000]);
    const upperBound = new Array(21).fill(null).concat([16500, 18200, 20000, 21800, 23500]);
    const lowerBound = new Array(21).fill(null).concat([13500, 14800, 16000, 17200, 18500]);
    
    if (charts.prediction) {
        charts.prediction.destroy();
    }
    
    charts.prediction = new Chart(ctx, {
        type: 'line',
        data: {
            labels: allYears,
            datasets: [
                {
                    label: '历史数据',
                    data: historicalData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    fill: false
                },
                {
                    label: '预测值',
                    data: predictionData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderDash: [5, 5],
                    fill: false
                },
                {
                    label: '置信上界',
                    data: upperBound,
                    borderColor: 'rgba(255, 99, 132, 0.3)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderDash: [2, 2],
                    fill: '+1'
                },
                {
                    label: '置信下界',
                    data: lowerBound,
                    borderColor: 'rgba(255, 99, 132, 0.3)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderDash: [2, 2],
                    fill: '-1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '5年储蓄预测'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '年份'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '储蓄总额'
                    }
                }
            }
        }
    });
    
    // 情景预测图
    initScenarioChart();
}

// 情景预测图
function initScenarioChart() {
    const container = document.getElementById('scenarioChart');
    if (!container) return;
    
    const years = Array.from({length: 6}, (_, i) => 1940 + i);
    
    const scenarios = [
        {
            name: '乐观情景',
            y: [14000, 16000, 18500, 21500, 24500, 27000],
            line: { color: 'rgb(34, 139, 34)' }
        },
        {
            name: '基准情景',
            y: [14000, 15500, 17000, 18500, 20000, 21500],
            line: { color: 'rgb(30, 144, 255)' }
        },
        {
            name: '悲观情景',
            y: [14000, 14500, 15000, 15500, 16000, 16500],
            line: { color: 'rgb(255, 69, 0)' }
        }
    ];
    
    const traces = scenarios.map(scenario => ({
        x: years,
        y: scenario.y,
        type: 'scatter',
        mode: 'lines+markers',
        name: scenario.name,
        line: scenario.line
    }));
    
    const layout = {
        title: '多情景预测分析',
        xaxis: { title: '年份' },
        yaxis: { title: '预测值' },
        height: 300
    };
    
    Plotly.newPlot(container, traces, layout, {responsive: true});
}

// 6. 网络拓扑分析可视化
function initNetworkAnalysis() {
    const container = document.getElementById('networkGraph');
    if (!container) return;
    
    // 生成网络节点和边
    const nodes = [
        { id: 'HK', label: '香港', size: 30, color: '#ff6384' },
        { id: 'SG', label: '新加坡', size: 25, color: '#36a2eb' },
        { id: 'GZ', label: '广州', size: 28, color: '#ffce56' },
        { id: 'XM', label: '厦门', size: 22, color: '#4bc0c0' },
        { id: 'MNL', label: '马尼拉', size: 20, color: '#9966ff' },
        { id: 'SF', label: '旧金山', size: 18, color: '#ff9f40' },
        { id: 'NYC', label: '纽约', size: 16, color: '#ff6384' },
        { id: 'LON', label: '伦敦', size: 15, color: '#36a2eb' }
    ];
    
    const edges = [
        { from: 0, to: 2, value: 10 },
        { from: 0, to: 3, value: 8 },
        { from: 1, to: 2, value: 7 },
        { from: 1, to: 4, value: 6 },
        { from: 2, to: 3, value: 9 },
        { from: 3, to: 4, value: 5 },
        { from: 5, to: 0, value: 4 },
        { from: 6, to: 0, value: 3 },
        { from: 7, to: 1, value: 3 }
    ];
    
    // 使用Plotly绘制网络图
    const nodeTrace = {
        x: nodes.map((_, i) => Math.cos(2 * Math.PI * i / nodes.length) * 150),
        y: nodes.map((_, i) => Math.sin(2 * Math.PI * i / nodes.length) * 150),
        text: nodes.map(n => n.label),
        mode: 'markers+text',
        hoverinfo: 'text',
        marker: {
            color: nodes.map(n => n.color),
            size: nodes.map(n => n.size),
            line: { color: 'white', width: 2 }
        },
        textposition: 'top center',
        type: 'scatter'
    };
    
    const edgeTraces = edges.map(edge => ({
        x: [
            Math.cos(2 * Math.PI * edge.from / nodes.length) * 150,
            Math.cos(2 * Math.PI * edge.to / nodes.length) * 150
        ],
        y: [
            Math.sin(2 * Math.PI * edge.from / nodes.length) * 150,
            Math.sin(2 * Math.PI * edge.to / nodes.length) * 150
        ],
        mode: 'lines',
        line: { 
            color: 'rgba(125, 125, 125, 0.3)',
            width: edge.value / 2
        },
        hoverinfo: 'none',
        type: 'scatter'
    }));
    
    const data = [...edgeTraces, nodeTrace];
    
    const layout = {
        title: '侨批网络拓扑结构',
        showlegend: false,
        hovermode: 'closest',
        xaxis: { showgrid: false, zeroline: false, showticklabels: false },
        yaxis: { showgrid: false, zeroline: false, showticklabels: false },
        height: 500,
        margin: { t: 50, l: 50, r: 50, b: 50 }
    };
    
    Plotly.newPlot(container, data, layout, {responsive: true});
}

// 7. 事件影响分析可视化
function initEventImpactAnalysis() {
    const container = document.getElementById('eventImpactChart');
    if (!container) return;
    
    // 生成事件影响时间序列
    const years = Array.from({length: 21}, (_, i) => 1920 + i);
    const baselineData = years.map((_, i) => 100 + i * 5);
    
    // 添加事件影响
    const eventData = [...baselineData];
    // 1929年经济危机
    for (let i = 9; i < 14; i++) {
        eventData[i] *= 0.6;
    }
    // 1937年战争
    for (let i = 17; i < 21; i++) {
        eventData[i] *= 0.4;
    }
    
    const traces = [
        {
            x: years,
            y: baselineData,
            type: 'scatter',
            mode: 'lines',
            name: '基准线',
            line: { color: 'rgba(0, 0, 0, 0.3)', dash: 'dash' }
        },
        {
            x: years,
            y: eventData,
            type: 'scatter',
            mode: 'lines',
            name: '实际值',
            line: { color: 'rgb(255, 99, 132)' },
            fill: 'tonexty',
            fillcolor: 'rgba(255, 99, 132, 0.1)'
        }
    ];
    
    // 添加事件标记
    const annotations = [
        {
            x: 1929,
            y: eventData[9],
            xref: 'x',
            yref: 'y',
            text: '经济危机',
            showarrow: true,
            arrowhead: 2,
            ax: -50,
            ay: -30,
            bgcolor: 'rgba(255, 99, 132, 0.8)',
            bordercolor: 'white',
            borderwidth: 2
        },
        {
            x: 1937,
            y: eventData[17],
            xref: 'x',
            yref: 'y',
            text: '战争爆发',
            showarrow: true,
            arrowhead: 2,
            ax: -50,
            ay: -30,
            bgcolor: 'rgba(255, 99, 132, 0.8)',
            bordercolor: 'white',
            borderwidth: 2
        }
    ];
    
    const layout = {
        title: '重大事件对汇款流量的影响',
        xaxis: { title: '年份' },
        yaxis: { title: '汇款流量' },
        height: 400,
        annotations: annotations,
        shapes: [
            {
                type: 'rect',
                xref: 'x',
                yref: 'paper',
                x0: 1929,
                y0: 0,
                x1: 1933,
                y1: 1,
                fillcolor: 'rgba(255, 0, 0, 0.1)',
                line: { width: 0 }
            },
            {
                type: 'rect',
                xref: 'x',
                yref: 'paper',
                x0: 1937,
                y0: 0,
                x1: 1940,
                y1: 1,
                fillcolor: 'rgba(255, 0, 0, 0.1)',
                line: { width: 0 }
            }
        ]
    };
    
    Plotly.newPlot(container, traces, layout, {responsive: true});
    
    // 填充事件表格
    fillEventTable();
}

// 填充事件表格
function fillEventTable() {
    const tbody = document.getElementById('eventTable');
    if (!tbody) return;
    
    const events = [
        {
            name: '经济大萧条',
            time: '1929-1933',
            scope: '全球',
            impact: '严重',
            recovery: '4年',
            color: 'danger'
        },
        {
            name: '银价波动',
            time: '1934-1935',
            scope: '东南亚',
            impact: '中等',
            recovery: '1年',
            color: 'warning'
        },
        {
            name: '抗日战争',
            time: '1937-1945',
            scope: '中国及东南亚',
            impact: '极严重',
            recovery: '8年+',
            color: 'danger'
        },
        {
            name: '货币改革',
            time: '1935',
            scope: '中国',
            impact: '轻微',
            recovery: '6个月',
            color: 'info'
        }
    ];
    
    tbody.innerHTML = events.map(event => `
        <tr>
            <td><strong>${event.name}</strong></td>
            <td>${event.time}</td>
            <td>${event.scope}</td>
            <td><span class="badge bg-${event.color}">${event.impact}</span></td>
            <td>${event.recovery}</td>
        </tr>
    `).join('');
}

// 初始化事件监听器
function initializeEventListeners() {
    // Tab切换时重新渲染图表
    const tabElements = document.querySelectorAll('a[data-bs-toggle="tab"]');
    tabElements.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const target = event.target.getAttribute('href').substring(1);
            
            // 根据激活的tab重新渲染相应的图表
            switch(target) {
                case 'timeseries':
                    if (charts.multiTimeseries) {
                        charts.multiTimeseries.resize();
                    }
                    break;
                case 'trend':
                    if (charts.trend) {
                        charts.trend.resize();
                    }
                    break;
                case 'correlation':
                    Plotly.Plots.resize('correlationHeatmap');
                    break;
                case 'clustering':
                    if (charts.cluster) {
                        charts.cluster.resize();
                    }
                    Plotly.Plots.resize('clusterScatter');
                    break;
                case 'prediction':
                    if (charts.prediction) {
                        charts.prediction.resize();
                    }
                    Plotly.Plots.resize('scenarioChart');
                    break;
                case 'network':
                    Plotly.Plots.resize('networkGraph');
                    break;
                case 'event':
                    Plotly.Plots.resize('eventImpactChart');
                    break;
            }
        });
    });
}

// 导出数据功能
function exportData() {
    if (!analysisData) {
        alert('没有可导出的数据');
        return;
    }
    
    const dataStr = JSON.stringify(analysisData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analysis_results_${new Date().toISOString().slice(0,10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// 生成本地演示数据的辅助函数
function generateLocalTimeseries() {
    return {
        overall: {
            "总体趋势": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                flow_count: 180 + i * 8,
                flow_amount: 1800 + i * 80
            }))
        }
    };
}

function generateLocalTrends() {
    return {
        overall_trends: {
            remittance_volume: "increasing",
            success_rate: "stable",
            network_density: "growing"
        }
    };
}

function generateLocalCorrelation() {
    return {
        correlation_matrix: [
            [1.00, 0.85, 0.72],
            [0.85, 1.00, 0.78],
            [0.72, 0.78, 1.00]
        ]
    };
}

function generateLocalClustering() {
    return {
        migrant_behavior_clusters: {
            cluster_profiles: {
                conservative: { size: 120 },
                balanced: { size: 100 },
                aggressive: { size: 80 }
            }
        }
    };
}

function generateLocalPrediction() {
    return {
        "5_year_forecast": {
            predicted_savings: [15000, 16500, 18000, 19500, 21000]
        }
    };
}

function generateLocalNetwork() {
    return {
        network_density: 0.45,
        centrality_analysis: {
            top_nodes: ["hong_kong", "singapore", "guangzhou"]
        }
    };
}

function generateLocalEventImpact() {
    return {
        events: [
            { name: "经济危机", year: 1929, impact: -0.4 },
            { name: "战争", year: 1937, impact: -0.6 }
        ]
    };
}