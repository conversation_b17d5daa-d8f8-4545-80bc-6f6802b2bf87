#!/bin/bash

echo "========================================"
echo "    侨批网络AI智能体仿真平台"
echo "========================================"
echo

echo "[1/3] 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi
echo "✅ Node.js已安装: $(node --version)"

echo
echo "[2/3] 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python3"
    exit 1
fi
echo "✅ Python已安装: $(python3 --version)"

echo
echo "[3/3] 启动Web服务器..."
echo "🚀 启动中，请稍候..."
echo

echo "================================"
echo "  访问地址:"
echo "  📊 主仪表盘: http://localhost:3508"
echo "  🤖 AI智能体设计器: http://localhost:3508/agent-designer.html"
echo "  🧠 高级AI设计器: http://localhost:3508/advanced-agent-designer.html"
echo "================================"
echo
echo "按 Ctrl+C 停止服务器"
echo

node server.js