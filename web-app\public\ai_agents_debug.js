// AI智能体调试工具
console.log('🔧 AI智能体调试工具加载中...');

function debugAgentsSection() {
    console.log('\n🔍 开始调试AI智能体页面...');
    
    // 1. 检查HTML元素是否存在
    const agentsSection = document.getElementById('agents-section');
    console.log('1️⃣ agents-section元素:', agentsSection);
    
    if (!agentsSection) {
        console.error('❌ 未找到agents-section元素');
        return false;
    }
    
    // 2. 检查当前显示状态
    const currentDisplay = window.getComputedStyle(agentsSection).display;
    const currentVisibility = window.getComputedStyle(agentsSection).visibility;
    console.log('2️⃣ 当前显示状态:', { display: currentDisplay, visibility: currentVisibility });
    
    // 3. 检查内容是否存在
    const hasContent = agentsSection.innerHTML.trim().length > 0;
    console.log('3️⃣ 是否有内容:', hasContent);
    console.log('3️⃣ 内容长度:', agentsSection.innerHTML.length);
    
    // 4. 强制显示
    agentsSection.style.display = 'block';
    agentsSection.style.visibility = 'visible';
    agentsSection.style.opacity = '1';
    console.log('4️⃣ 已强制设置为可见');
    
    // 5. 检查子元素
    const templates = document.getElementById('agentTemplatesGrid');
    const myAgents = document.getElementById('myAgentsGrid');
    console.log('5️⃣ 子元素:', { templates, myAgents });
    
    return true;
}

function forceShowAgents() {
    console.log('💪 强制显示AI智能体页面...');
    
    // 隐藏所有其他section
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示agents-section
    const agentsSection = document.getElementById('agents-section');
    if (agentsSection) {
        agentsSection.style.display = 'block';
        agentsSection.style.visibility = 'visible';
        agentsSection.style.opacity = '1';
        
        // 确保Bootstrap类没有冲突
        agentsSection.classList.remove('d-none', 'hidden');
        
        console.log('✅ AI智能体页面已强制显示');
        
        // 更新导航状态
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('onclick') && link.getAttribute('onclick').includes("'agents'")) {
                link.classList.add('active');
            }
        });
        
        // 显示成功提示
        if (window.uiManager) {
            window.uiManager.showNotification('✅ AI智能体页面已强制显示！', 'success');
        } else {
            console.log('✅ AI智能体页面已显示！');
        }
        
        return true;
    } else {
        console.error('❌ 未找到agents-section元素');
        return false;
    }
}

// 添加紧急修复按钮
function addEmergencyButton() {
    // 检查是否已经添加过
    if (document.getElementById('emergency-agents-btn')) {
        return;
    }
    
    const button = document.createElement('button');
    button.id = 'emergency-agents-btn';
    button.innerHTML = '🚨 紧急显示AI智能体';
    button.className = 'btn btn-danger btn-sm position-fixed';
    button.style.cssText = `
        top: 20px; 
        right: 20px; 
        z-index: 9999;
        box-shadow: 0 4px 12px rgba(220,53,69,0.3);
        animation: pulse 2s infinite;
    `;
    
    button.onclick = function() {
        console.log('🚨 用户点击紧急显示按钮');
        
        // 强制显示AI智能体页面
        const success = forceShowAgents();
        
        if (success) {
            // 移除紧急按钮
            this.style.display = 'none';
            
            // 显示成功信息
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success position-fixed';
            successDiv.style.cssText = 'top: 70px; right: 20px; z-index: 9999; width: 300px;';
            successDiv.innerHTML = `
                <h6>✅ 修复成功！</h6>
                <p class="mb-0">AI智能体页面现在应该可以正常显示了。</p>
            `;
            document.body.appendChild(successDiv);
            
            // 3秒后移除成功信息
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }
    };
    
    document.body.appendChild(button);
    
    // 添加脉冲动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    `;
    document.head.appendChild(style);
    
    console.log('🚨 紧急修复按钮已添加');
}

// 自动检测和修复
function autoDetectAndFix() {
    console.log('🤖 自动检测AI智能体页面问题...');
    
    setTimeout(() => {
        const agentsSection = document.getElementById('agents-section');
        
        if (!agentsSection) {
            console.error('🚨 严重问题：agents-section元素不存在');
            addEmergencyButton();
            return;
        }
        
        // 只在用户实际访问agents页面时才检查可见性
        // 默认情况下content-section都是隐藏的，这是正常的
        console.log('✅ agents-section元素存在，调试工具就绪');
        
        // 不在页面加载时自动添加紧急按钮
        // 只有在用户点击智能体页面后仍然不可见时才添加
    }, 1000);
}

// 页面加载后执行检测
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 页面加载完成，AI智能体调试工具已就绪');
    
    // 只做基础检测，不自动添加紧急按钮
    autoDetectAndFix();
    
    // 监听showSection调用
    const originalShowSection = window.showSection;
    window.showSection = function(sectionName) {
        console.log('📞 showSection被调用:', sectionName);
        
        if (sectionName === 'agents') {
            console.log('🎯 用户要查看AI智能体页面');
            
            // 调用原函数
            if (originalShowSection) {
                originalShowSection(sectionName);
            }
            
            // 额外检查
            setTimeout(() => {
                const agentsSection = document.getElementById('agents-section');
                if (agentsSection) {
                    const computedStyle = window.getComputedStyle(agentsSection);
                    const isVisible = computedStyle.display !== 'none';
                    const hasContent = agentsSection.innerHTML.trim().length > 50; // 确保有实际内容
                    
                    console.log('🔍 AI智能体页面检查:', { 
                        isVisible, 
                        hasContent, 
                        display: computedStyle.display,
                        innerHTML: agentsSection.innerHTML.length 
                    });
                    
                    if (!isVisible || !hasContent) {
                        console.log('🔧 自动修复显示问题...');
                        forceShowAgents();
                        
                        // 只在修复后仍有问题时才添加紧急按钮
                        setTimeout(() => {
                            const stillHidden = window.getComputedStyle(agentsSection).display === 'none';
                            if (stillHidden) {
                                console.warn('⚠️ 修复失败，添加紧急按钮');
                                addEmergencyButton();
                            }
                        }, 500);
                    }
                } else {
                    console.error('🚨 致命错误：agents-section元素消失了');
                    addEmergencyButton();
                }
            }, 100);
        } else {
            // 调用原函数
            if (originalShowSection) {
                originalShowSection(sectionName);
            }
        }
    };
});

// 导出调试函数
window.debugAgentsSection = debugAgentsSection;
window.forceShowAgents = forceShowAgents;