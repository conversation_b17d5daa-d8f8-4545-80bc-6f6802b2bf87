#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实AI智能体仿真
"""

import os
import sys
import subprocess
import json

def test_module_imports():
    """测试模块导入"""
    print("Testing module imports...")
    
    # 切换到父目录
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        # 测试基础模块
        try:
            from simulation_engine import QiaopiSimulationEngine, SimulationConfig
            print("  ✅ simulation_engine imported")
        except ImportError as e:
            print(f"  ❌ simulation_engine failed: {e}")
            return False
            
        try:
            from agents import MigrantAgent, FamilyAgent, InstitutionAgent
            print("  ✅ agents imported")
        except ImportError as e:
            print(f"  ❌ agents failed: {e}")
            return False
            
        try:
            from environment import Environment
            print("  ✅ environment imported")
        except ImportError as e:
            print(f"  ❌ environment failed: {e}")
            return False
            
        # 测试增强模块（可选）
        try:
            from enhanced_simulation_engine import EnhancedQiaopiSimulationEngine
            print("  ✅ enhanced_simulation_engine imported")
            enhanced_available = True
        except ImportError as e:
            print(f"  ⚠️  enhanced_simulation_engine not available: {e}")
            enhanced_available = False
        
        return True, enhanced_available
        
    except Exception as e:
        print(f"  ❌ Unexpected error: {e}")
        return False, False
    finally:
        os.chdir(original_dir)

def test_basic_ai_simulation():
    """测试基础AI仿真"""
    print("\nTesting basic AI simulation...")
    
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    os.chdir(parent_dir)
    
    try:
        from simulation_engine import QiaopiSimulationEngine, SimulationConfig
        
        # 创建小规模测试配置
        config = SimulationConfig(
            start_year=1920,
            end_year=1922,  # 只运行2年，快速测试
            num_migrants=20,
            num_families=20, 
            num_institutions=3,
            output_directory='test_ai_results'
        )
        
        print(f"  Configuration: {config.num_migrants} migrants, {config.num_families} families, {config.num_institutions} institutions")
        print(f"  Time period: {config.start_year}-{config.end_year}")
        
        # 运行仿真
        print("  Running AI agent simulation...")
        engine = QiaopiSimulationEngine(config)
        results = engine.run_simulation()
        
        print("  ✅ Basic AI simulation completed")
        print(f"  📊 Statistics entries: {len(results.get('statistics', []))}")
        print(f"  🤖 Agent types: {list(results.get('agents', {}).keys())}")
        
        # 验证AI智能体确实被创建和使用
        agents = results.get('agents', {})
        total_agents = sum(len(agent_list) for agent_list in agents.values())
        print(f"  👥 Total AI agents: {total_agents}")
        
        return True, results
        
    except Exception as e:
        print(f"  ❌ Basic AI simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        os.chdir(original_dir)

def run_real_ai_script_test():
    """测试真实AI脚本"""
    print("\nTesting real AI script execution...")
    
    original_dir = os.getcwd()
    parent_dir = os.path.dirname(os.getcwd())
    
    try:
        # 检查脚本是否存在
        script_path = os.path.join(parent_dir, 'run_real_enhanced_simulation.py')
        if not os.path.exists(script_path):
            print(f"  ❌ Script not found: {script_path}")
            return False
            
        print(f"  📍 Found script: {script_path}")
        
        # 运行脚本
        cmd = [
            sys.executable, script_path,
            '--start-year', '1920',
            '--end-year', '1922',
            '--migrants', '20',
            '--families', '20', 
            '--institutions', '3',
            '--output-dir', 'test_enhanced_results'
        ]
        
        print(f"  💻 Command: {' '.join(cmd)}")
        print(f"  📁 Working directory: {parent_dir}")
        
        os.chdir(parent_dir)
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        print(f"  📊 Exit code: {result.returncode}")
        
        if result.stdout:
            print("  📄 Output:")
            for line in result.stdout.split('\n')[:10]:  # 显示前10行
                if line.strip():
                    print(f"    {line}")
            if len(result.stdout.split('\n')) > 10:
                print(f"    ... ({len(result.stdout.split('\n'))} total lines)")
        
        if result.stderr:
            print("  ⚠️ Errors:")
            for line in result.stderr.split('\n')[:5]:  # 显示前5行错误
                if line.strip():
                    print(f"    {line}")
        
        # 检查生成的文件
        results_dir = os.path.join(parent_dir, 'test_enhanced_results')
        if os.path.exists(results_dir):
            files = os.listdir(results_dir)
            print(f"  📂 Generated files ({len(files)}): {files}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("  ⏰ Script timeout (180s)")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False
    finally:
        os.chdir(original_dir)

def main():
    print("🎯 Real AI Agent Simulation Test")
    print("=" * 50)
    
    # 1. 测试模块导入
    imports_ok, enhanced_available = test_module_imports()
    
    # 2. 测试基础AI仿真
    basic_ok, basic_results = test_basic_ai_simulation()
    
    # 3. 测试真实AI脚本
    script_ok = run_real_ai_script_test()
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  Module Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"  Enhanced Available: {'✅ YES' if enhanced_available else '⚠️ NO'}")
    print(f"  Basic AI Simulation: {'✅ PASS' if basic_ok else '❌ FAIL'}")
    print(f"  Real AI Script: {'✅ PASS' if script_ok else '❌ FAIL'}")
    
    if imports_ok and basic_ok:
        print("\n✅ AI agent simulation is working!")
        if script_ok:
            print("🚀 Real AI enhanced simulation script is ready!")
        else:
            print("⚠️ Enhanced script needs fixing, but basic AI simulation works")
    else:
        print("\n❌ AI simulation has issues that need to be resolved")
    
    return imports_ok and (basic_ok or script_ok)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)