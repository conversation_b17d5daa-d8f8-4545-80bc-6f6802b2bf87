#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版API脚本
"""

import os
import sys
import json

# Add parent directory to path
sys.path.insert(0, '..')

try:
    # Test importing the API script
    from run_enhanced_demo_api import run_api_demo
    print("✅ Successfully imported run_enhanced_demo_api")
    
    # Test with minimal config
    print("🧪 Testing with minimal configuration...")
    
    config = {
        'start_year': 1920,
        'end_year': 1925,  # Very short simulation for testing
        'num_migrants': 50,
        'num_families': 50,
        'num_institutions': 3,
        'output_directory': 'test_enhanced_results'
    }
    
    result = run_api_demo(config)
    
    if result['success']:
        print("✅ Enhanced API test completed successfully!")
        print(f"📁 Results saved to: {result['output_directory']}")
        print(f"📄 Files generated: {len(result['files_generated'])}")
        for file in result['files_generated']:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} (missing)")
    else:
        print("❌ Enhanced API test failed!")
        print(f"Error: {result['error']}")
        if 'traceback' in result:
            print("Traceback:")
            print(result['traceback'])
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure all required modules are available in the parent directory")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()

input("\nPress Enter to continue...")