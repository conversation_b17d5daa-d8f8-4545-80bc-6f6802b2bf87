from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
import random
import json
import csv
from datetime import datetime
import os
import math
import statistics
from collections import defaultdict, Counter

from agents import MigrantAgent, FamilyAgent, InstitutionAgent, QiaoxiangRegion, Location, EconomicStatus, Occupation
from environment import Environment
from qiaopi_interactions import QiaopiInteractionProtocol, ChainMigrationLogic, QiaopiMessage


@dataclass
class SimulationConfig:
    """仿真配置参数"""
    start_year: int = 1900
    end_year: int = 1950
    steps_per_year: int = 12  # 月度仿真
    
    # 智能体数量
    num_migrants: int = 1000
    num_families: int = 800
    num_institutions: int = 20
    
    # 初始化参数
    initial_migrant_savings_range: Tuple[float, float] = (10.0, 100.0)
    initial_family_cash_range: Tuple[float, float] = (5.0, 50.0)
    initial_institution_liquidity_range: Tuple[float, float] = (500.0, 5000.0)
    
    # 网络参数
    kinship_network_size_range: Tuple[int, int] = (3, 15)
    migration_probability_per_step: float = 0.01
    
    # 输出设置
    output_directory: str = "simulation_results"
    save_detailed_logs: bool = True
    save_statistics_interval: int = 12  # 每年保存一次统计
    
    # 个性化与真实数据集成
    enable_personalization: bool = True
    personalization_from_data: bool = True
    data_file: Optional[str] = "data/standardized_qiaopi_data.pkl"
    # 货币系统配置（可选覆盖）
    base_currency: str = "CNY"
    custom_exchange_rates: Optional[Dict[str, float]] = None


class QiaopiSimulationEngine:
    """侨批网络仿真引擎"""
    
    def __init__(self, config: SimulationConfig):
        self.config = config
        self.environment = Environment(config.start_year, config.end_year)
        self.qiaopi_protocol = QiaopiInteractionProtocolFixed(self.environment, self)
        self.chain_migration = ChainMigrationLogic()
        
        # 智能体存储
        self.migrants: Dict[str, MigrantAgent] = {}
        self.families: Dict[str, FamilyAgent] = {}
        self.institutions: Dict[str, InstitutionAgent] = {}
        
        # 网络关系
        self.kinship_networks: Dict[str, List[str]] = {}  # network_id -> [agent_ids]
        self.migrant_family_pairs: Dict[str, str] = {}  # migrant_id -> family_id
        
        # 仿真状态
        self.current_step = 0
        self.is_running = False
        
        # 统计数据
        self.statistics_history: List[Dict[str, Any]] = []
        self.qiaopi_history: List[QiaopiMessage] = []

        # 初始家庭经济状态快照（用于流动性分析）
        self.initial_family_economic_status: Dict[str, EconomicStatus] = {}
        
        # 创建输出目录
        self._ensure_output_directory()
        
        # 初始化智能体
        self._initialize_agents()
        self._setup_networks()
        
        # 应用个性化与数据驱动参数
        if self.config.enable_personalization:
            try:
                self._apply_personalization()
            except Exception as e:
                print(f"个性化配置应用失败: {e}")
        # 覆盖环境货币配置
        try:
            if hasattr(self.environment, 'base_currency') and self.config.base_currency:
                self.environment.base_currency = self.config.base_currency
            if hasattr(self.environment, 'currency_exchange_rates') and self.config.custom_exchange_rates:
                self.environment.currency_exchange_rates.update(self.config.custom_exchange_rates)
        except Exception as e:
            print(f"应用货币系统配置失败: {e}")
    
    def _ensure_output_directory(self):
        """确保输出目录存在"""
        if not os.path.exists(self.config.output_directory):
            os.makedirs(self.config.output_directory)
    
    def _initialize_agents(self):
        """初始化所有智能体"""
        print(f"初始化 {self.config.num_migrants} 个移民智能体...")
        self._create_migrants()
        
        print(f"初始化 {self.config.num_families} 个家庭智能体...")
        self._create_families()
        
        print(f"初始化 {self.config.num_institutions} 个机构智能体...")
        self._create_institutions()
    
    def _create_migrants(self):
        """创建移民智能体"""
        for i in range(self.config.num_migrants):
            migrant = MigrantAgent(
                origin_qiaoxiang=random.choice(list(QiaoxiangRegion)),
                birth_year=random.randint(1870, 1920),
                location=random.choice(list(Location)),
                occupation=random.choice(list(Occupation)),
                income_level=0.0,  # 将在update中计算
                savings=random.uniform(*self.config.initial_migrant_savings_range),
                health_status=random.uniform(0.7, 1.0),
                years_abroad=random.randint(0, 20),
                social_capital=random.uniform(0.1, 0.5),
                obligation_level=random.uniform(0.3, 0.8),
                kinship_network_id=f"network_{i % (self.config.num_migrants // 5)}"  # 平均每个网络5人
            )
            self.migrants[migrant.agent_id] = migrant
    
    def _create_families(self):
        """创建家庭智能体"""
        for i in range(self.config.num_families):
            family = FamilyAgent(
                location_qiaoxiang=random.choice(list(QiaoxiangRegion)),
                family_size=random.randint(3, 8),
                economic_status=random.choice(list(EconomicStatus)),
                dependency_ratio=random.uniform(0.2, 0.7),
                urgent_need=False,
                assets={"land": random.uniform(0, 500), "house": random.uniform(0, 1000)},
                cash_reserves=random.uniform(*self.config.initial_family_cash_range),
                kinship_network_id=f"network_{i % (self.config.num_families // 4)}"  # 平均每个网络4个家庭
            )
            self.families[family.agent_id] = family
            # 记录初始经济状态
            self.initial_family_economic_status[family.agent_id] = family.economic_status
    
    def _create_institutions(self):
        """创建机构智能体"""
        major_hubs = [Location.SINGAPORE, Location.HONG_KONG, Location.MALAYSIA]
        
        for i in range(self.config.num_institutions):
            home_base = random.choice(major_hubs) if i < len(major_hubs) else random.choice(list(Location))
            
            # 网络覆盖范围基于总部位置
            if home_base == Location.SINGAPORE:
                network_reach = [Location.MALAYSIA, Location.THAILAND, Location.INDONESIA]
            elif home_base == Location.HONG_KONG:
                network_reach = [Location.SINGAPORE, Location.MALAYSIA]
            else:
                network_reach = random.sample(list(Location), k=random.randint(2, 4))
            
            institution = InstitutionAgent(
                home_base=home_base,
                network_reach=network_reach,
                trust_rating=random.uniform(0.6, 0.9),
                fee_structure={"base_fee": random.uniform(0.03, 0.08), "distance_multiplier": random.uniform(1.1, 1.5)},
                liquidity=random.uniform(*self.config.initial_institution_liquidity_range),
                operational_risk=random.uniform(0.05, 0.15)
            )
            self.institutions[institution.agent_id] = institution
    
    def _setup_networks(self):
        """建立网络关系"""
        # 建立亲属网络
        for migrant in self.migrants.values():
            network_id = migrant.kinship_network_id
            if network_id not in self.kinship_networks:
                self.kinship_networks[network_id] = []
            self.kinship_networks[network_id].append(migrant.agent_id)
            self.chain_migration.add_to_network(network_id, migrant.agent_id)
        
        for family in self.families.values():
            network_id = family.kinship_network_id
            if network_id not in self.kinship_networks:
                self.kinship_networks[network_id] = []
            self.kinship_networks[network_id].append(family.agent_id)
        
        # 建立移民-家庭配对关系
        migrant_ids = list(self.migrants.keys())
        family_ids = list(self.families.keys())
        
        for i, migrant_id in enumerate(migrant_ids):
            if i < len(family_ids):
                family_id = family_ids[i]
                self.migrant_family_pairs[migrant_id] = family_id
                
                # 确保同一亲属网络
                migrant = self.migrants[migrant_id]
                family = self.families[family_id]
                family.kinship_network_id = migrant.kinship_network_id

    def _apply_personalization(self):
        """为智能体应用个性化参数，包括：
        - 货币偏好分配
        - 外部冲击敏感度
        - 从真实侨批数据映射的行为参数
        """
        # 1) 货币偏好随机/按地理分布设置
        location_currency_pref = {
            Location.SINGAPORE: ["SGD", "HKD", "USD"],
            Location.MALAYSIA: ["MYR", "HKD"],
            Location.THAILAND: ["THB", "HKD"],
            Location.INDONESIA: ["IDR", "HKD"],
            Location.PHILIPPINES: ["PHP", "USD"],
            Location.HONG_KONG: ["HKD", "USD"],
        }
        for migrant in self.migrants.values():
            options = location_currency_pref.get(migrant.location, ["HKD"]) 
            migrant.preferred_currency = random.choice(options)
            # 默认个性化敏感度
            migrant.personalization_profile = {
                "war_sensitivity": random.uniform(0.8, 1.2),
                "economic_recession_sensitivity": random.uniform(0.8, 1.2),
                "policy_change_sensitivity": random.uniform(0.8, 1.2),
                "natural_disaster_sensitivity": random.uniform(0.8, 1.2),
                "economic_boom_sensitivity": random.uniform(0.8, 1.2),
            }
        
        for family in self.families.values():
            # 家庭对冲击的敏感度（如医疗事件、教育支出受经济周期波动影响）
            family.personalization_profile = {
                "economic_recession_sensitivity": random.uniform(0.9, 1.3),
                "war_sensitivity": random.uniform(0.9, 1.2),
            }

        # 2) 可选：从真实数据映射
        if self.config.personalization_from_data and self.config.data_file:
            try:
                from data_integration import HistoricalDataProcessor, SimulationCalibrator
                processor = HistoricalDataProcessor()
                # 自动识别格式（csv/json/pkl）
                if processor.load_qiaopi_data(self.config.data_file, format='auto') > 0:
                    processor.calibrate_simulation_parameters()
                    # 将季节性因子放到环境里
                    if hasattr(processor, "calibration_parameters"):
                        seasonal = processor.calibration_parameters.get("seasonal_factors")
                        if seasonal:
                            self.environment.seasonal_remittance_factors = seasonal
                    # 对收入、义务等进行微调
                    mean = processor.calibration_parameters.get("remittance_amount_distribution", {}).get("mean")
                    if mean:
                        target_mean_income = mean / 0.3
                        for migrant in self.migrants.values():
                            base_income = migrant._get_base_income()
                            adj = (target_mean_income / max(1e-6, base_income))
                            migrant.income_level = base_income * (0.5 + 0.5 * min(2.0, max(0.5, adj)))
                            # 义务水平跟随数据轻微调整
                            migrant.obligation_level = min(1.0, max(0.1, migrant.obligation_level * random.uniform(0.9, 1.1)))
            except Exception as e:
                print(f"基于数据的个性化映射失败: {e}")
    
    def run_simulation(self) -> Dict[str, Any]:
        """运行完整仿真"""
        print(f"开始仿真：{self.config.start_year}-{self.config.end_year}")
        print(f"智能体数量 - 移民: {len(self.migrants)}, 家庭: {len(self.families)}, 机构: {len(self.institutions)}")
        
        self.is_running = True
        total_steps = (self.config.end_year - self.config.start_year) * self.config.steps_per_year
        
        try:
            for step in range(total_steps):
                self.current_step = step
                self._run_single_step()
                
                # 定期保存统计数据
                if step % self.config.save_statistics_interval == 0:
                    self._save_statistics()
                
                # 进度报告
                if step % (total_steps // 10) == 0:
                    progress = (step / total_steps) * 100
                    current_year = self.environment.get_current_year()
                    print(f"进度: {progress:.1f}% - 当前年份: {current_year:.1f}")
        
        except KeyboardInterrupt:
            print("仿真被用户中断")
        finally:
            self.is_running = False
            print("仿真完成")
            return self._generate_final_report()
    
    def _run_single_step(self):
        """运行单个仿真步骤"""
        # 1. 更新环境
        self.environment.update()
        
        # 2. 更新所有智能体
        for migrant in self.migrants.values():
            migrant.update(self.environment)
        
        for family in self.families.values():
            family.update(self.environment)
        
        for institution in self.institutions.values():
            institution.update(self.environment)
        
        # 3. 处理侨批交互
        self._process_remittances()
        
        # 4. 处理链式移民
        self._process_chain_migration()
        
        # 5. 处理侨批递送
        delivered_qiaopi = self.qiaopi_protocol.process_qiaopi_delivery()
        self.qiaopi_history.extend(delivered_qiaopi)
    
    def _process_remittances(self):
        """处理汇款决策"""
        for migrant_id, family_id in self.migrant_family_pairs.items():
            migrant = self.migrants[migrant_id]
            family = self.families[family_id]
            
            # 选择机构（简化：随机选择信任度较高的机构）
            available_institutions = [
                inst for inst in self.institutions.values()
                if inst.trust_rating > 0.5 and inst.liquidity > migrant.savings * 0.1
            ]
            
            if available_institutions:
                institution = max(available_institutions, key=lambda x: x.trust_rating)
                
                # 尝试发起汇款
                qiaopi = self.qiaopi_protocol.initiate_remittance(migrant, family, institution)
                if qiaopi:
                    self.qiaopi_history.append(qiaopi)
    
    def _process_chain_migration(self):
        """处理链式移民逻辑"""
        # 简化实现：基于成功率更新迁移概率
        for migrant in self.migrants.values():
            if migrant.years_abroad > 2 and migrant.savings > 50:  # 成功移民的标准
                self.chain_migration.update_success_rate(
                    migrant.origin_qiaoxiang, 
                    migrant.location, 
                    True
                )
    
    def _save_statistics(self):
        """保存统计数据"""
        stats = self._calculate_current_statistics()
        self.statistics_history.append(stats)
        
        if self.config.save_detailed_logs:
            filename = f"{self.config.output_directory}/statistics_step_{self.current_step}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
    
    def _calculate_current_statistics(self) -> Dict[str, Any]:
        """计算当前统计数据"""
        # 基础统计
        total_migrant_savings = sum(m.savings for m in self.migrants.values())
        total_family_cash = sum(f.cash_reserves for f in self.families.values())
        avg_migrant_income = sum(m.income_level for m in self.migrants.values()) / len(self.migrants)
        
        # 侨批统计
        qiaopi_stats = self.qiaopi_protocol.get_statistics()
        
        # 地理分布
        migrant_distribution = {}
        for location in Location:
            count = sum(1 for m in self.migrants.values() if m.location == location)
            migrant_distribution[location.value] = count
        
        # 经济状况分布
        family_economic_distribution = {}
        for status in EconomicStatus:
            count = sum(1 for f in self.families.values() if f.economic_status == status)
            family_economic_distribution[status.value] = count
        
        return {
            "simulation_step": self.current_step,
            "current_year": self.environment.get_current_year(),
            "total_migrant_savings": total_migrant_savings,
            "total_family_cash": total_family_cash,
            "average_migrant_income": avg_migrant_income,
            "migrant_distribution": migrant_distribution,
            "family_economic_distribution": family_economic_distribution,
            "qiaopi_statistics": qiaopi_stats,
            "environment_status": self.environment.get_status_summary(),
            "active_kinship_networks": len(self.kinship_networks)
        }
    
    def _generate_final_report(self) -> Dict[str, Any]:
        """生成最终报告"""
        final_stats = self._calculate_current_statistics()
        
        # 历史趋势分析
        if len(self.statistics_history) > 1:
            initial_stats = self.statistics_history[0]
            
            savings_growth = (final_stats["total_migrant_savings"] - 
                            initial_stats["total_migrant_savings"]) / initial_stats["total_migrant_savings"]
            
            income_growth = (final_stats["average_migrant_income"] - 
                           initial_stats["average_migrant_income"]) / initial_stats["average_migrant_income"]
        else:
            savings_growth = 0
            income_growth = 0
        
        # 网络效应分析
        network_analysis = self._analyze_network_effects()
        # 高阶分析
        advanced_analysis = self._compute_advanced_analysis()
        
        report = {
            "simulation_summary": {
                "start_year": self.config.start_year,
                "end_year": self.config.end_year,
                "total_steps": self.current_step,
                "total_agents": len(self.migrants) + len(self.families) + len(self.institutions)
            },
            "final_statistics": final_stats,
            "growth_analysis": {
                "savings_growth_rate": savings_growth,
                "income_growth_rate": income_growth
            },
            "network_analysis": network_analysis,
            "advanced_analysis": advanced_analysis,
            "qiaopi_summary": {
                "total_qiaopi_processed": len(self.qiaopi_history),
                "successful_remittances": len(self.qiaopi_protocol.completed_qiaopi),
                "failed_remittances": len(self.qiaopi_protocol.failed_qiaopi)
            }
        }
        
        # 保存最终报告
        if self.config.save_detailed_logs:
            with open(f"{self.config.output_directory}/final_report.json", 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def _analyze_network_effects(self) -> Dict[str, Any]:
        """分析网络效应"""
        network_sizes = [len(members) for members in self.kinship_networks.values()]
        
        # 计算网络规模与成功率的关系
        network_success_correlation = {}
        for network_id, member_ids in self.kinship_networks.items():
            network_migrants = [self.migrants[mid] for mid in member_ids if mid in self.migrants]
            if network_migrants:
                avg_savings = sum(m.savings for m in network_migrants) / len(network_migrants)
                network_success_correlation[network_id] = {
                    "size": len(member_ids),
                    "avg_savings": avg_savings
                }
        
        return {
            "total_networks": len(self.kinship_networks),
            "average_network_size": sum(network_sizes) / len(network_sizes) if network_sizes else 0,
            "largest_network_size": max(network_sizes) if network_sizes else 0,
            "smallest_network_size": min(network_sizes) if network_sizes else 0,
            "network_success_correlation": network_success_correlation
        }

    # ================== 高阶分析与统计工具 ==================
    def _compute_advanced_analysis(self) -> Dict[str, Any]:
        """计算更复杂的分析指标，涵盖不平等、季节性、事件影响、机构表现等。"""
        analysis: Dict[str, Any] = {}

        # 1) 不平等与分布
        migrant_savings = [m.savings for m in self.migrants.values()]
        migrant_income = [m.income_level for m in self.migrants.values()]
        analysis["inequality_metrics"] = {
            "savings_gini": self._gini(migrant_savings),
            "income_gini": self._gini(migrant_income),
            "top10_savings_share": self._top_share(migrant_savings, 0.10),
            "savings_percentiles": {
                "p50": self._percentile(migrant_savings, 50),
                "p90": self._percentile(migrant_savings, 90),
                "p99": self._percentile(migrant_savings, 99),
            }
        }

        # 2) 侨批动态与延迟
        delivered_delays = [
            (q.delivery_time - q.send_time) for q in self.qiaopi_history if q.delivery_time is not None
        ]
        delay_stats = {
            "count": len(delivered_delays),
            "mean": (sum(delivered_delays) / len(delivered_delays)) if delivered_delays else 0,
            "min": (min(delivered_delays) if delivered_delays else 0),
            "q1": self._percentile(delivered_delays, 25),
            "median": (statistics.median(delivered_delays) if delivered_delays else 0),
            "q3": self._percentile(delivered_delays, 75),
            "max": (max(delivered_delays) if delivered_delays else 0),
            "p90": self._percentile(delivered_delays, 90),
            "p99": self._percentile(delivered_delays, 99),
        }

        # 月度季节性（按发送月份）
        steps_per_year = max(1, self.config.steps_per_year)
        month_counts = {m: 0 for m in range(1, steps_per_year + 1)}
        month_amounts = {m: 0.0 for m in range(1, steps_per_year + 1)}
        for q in self.qiaopi_history:
            month = (q.send_time % steps_per_year) + 1
            month_counts[month] += 1
            month_amounts[month] += q.amount
        seasonal_corr = None
        seasonal_factors = getattr(self.environment, "seasonal_remittance_factors", None)
        if seasonal_factors and len(seasonal_factors) == steps_per_year:
            counts_vec = [month_counts[m] for m in range(1, steps_per_year + 1)]
            seasonal_corr = self._pearson(counts_vec, list(seasonal_factors))

        analysis["remittance_dynamics"] = {
            "delivery_delay_stats": delay_stats,
            "monthly_counts": month_counts,
            "monthly_amounts": month_amounts,
            "seasonality_correlation": seasonal_corr,
            "overall_success_rate": (
                len(self.qiaopi_protocol.completed_qiaopi) /
                max(1, (len(self.qiaopi_protocol.completed_qiaopi) + len(self.qiaopi_protocol.failed_qiaopi)))
            )
        }

        # 3) 历史事件影响（按发送时间归属事件区间）
        try:
            from environment import EventType
            event_periods: Dict[EventType, List[Tuple[int, int]]] = defaultdict(list)
            for ev in self.environment.historical_events:
                event_periods[ev.event_type].append((ev.start_time, ev.start_time + ev.duration))

            def in_periods(step: int, periods: List[Tuple[int, int]]) -> bool:
                return any(start <= step < end for start, end in periods)

            by_event_type: Dict[str, Any] = {}
            for ev_type, periods in event_periods.items():
                group_within = [q for q in self.qiaopi_history if in_periods(q.send_time, periods)]
                group_outside = [q for q in self.qiaopi_history if not in_periods(q.send_time, periods)]

                def summarize(group: List[QiaopiMessage]) -> Dict[str, Any]:
                    delivered = [q for q in group if q.delivery_time is not None]
                    failed = [q for q in group if (q.delivery_time is None and q in self.qiaopi_protocol.failed_qiaopi) or (q.delivery_time is None and q not in self.qiaopi_protocol.completed_qiaopi)]
                    delays = [(q.delivery_time - q.send_time) for q in delivered]
                    return {
                        "count": len(group),
                        "success_rate": (len(delivered) / max(1, (len(delivered) + len(failed)))),
                        "avg_amount": (sum(q.amount for q in group) / len(group)) if group else 0,
                        "avg_delay": (sum(delays) / len(delays)) if delays else 0,
                    }

                by_event_type[ev_type.value] = {
                    "within_event": summarize(group_within),
                    "outside_event": summarize(group_outside),
                }
        except Exception:
            by_event_type = {}
        analysis["event_impact"] = by_event_type

        # 4) 通道流（origin -> destination）
        corridor_stats: Dict[Tuple[str, str], Dict[str, Any]] = defaultdict(lambda: {"count": 0, "amount": 0.0, "delivered": 0})
        for q in self.qiaopi_history:
            key = (q.origin.value, q.destination.value)
            data = corridor_stats[key]
            data["count"] += 1
            data["amount"] += q.amount
            if q.delivery_time is not None:
                data["delivered"] += 1
        for k, v in corridor_stats.items():
            total = v["count"]
            v["success_rate"] = v["delivered"] / max(1, total)
        top_by_count = sorted([
            {"origin": k[0], "destination": k[1], **v} for k, v in corridor_stats.items()
        ], key=lambda x: x["count"], reverse=True)[:5]
        top_by_amount = sorted([
            {"origin": k[0], "destination": k[1], **v} for k, v in corridor_stats.items()
        ], key=lambda x: x["amount"], reverse=True)[:5]
        analysis["corridor_flows"] = {
            "top_by_count": top_by_count,
            "top_by_amount": top_by_amount,
        }

        # 5) 机构表现
        institution_perf = {}
        for inst_id, inst in self.institutions.items():
            success_rate = inst.successful_deliveries / max(1, inst.total_deliveries)
            institution_perf[inst_id] = {
                "home_base": inst.home_base.value,
                "trust_rating": inst.trust_rating,
                "total_deliveries": inst.total_deliveries,
                "success_rate": success_rate,
                "liquidity": inst.liquidity,
            }
        # 机构费收入（已成功的）
        fee_by_institution: Dict[str, float] = defaultdict(float)
        for q in self.qiaopi_history:
            if q.delivery_time is not None:
                fee_by_institution[q.institution_id] += q.transaction_fee
        for inst_id, fee_sum in fee_by_institution.items():
            institution_perf.setdefault(inst_id, {})["collected_fees"] = fee_sum
        # 汇总统计
        if institution_perf:
            success_rates = [v.get("success_rate", 0) for v in institution_perf.values()]
            inst_summary = {
                "avg_success_rate": sum(success_rates) / len(success_rates),
                "max_success_rate": max(success_rates),
                "min_success_rate": min(success_rates),
            }
        else:
            inst_summary = {"avg_success_rate": 0, "max_success_rate": 0, "min_success_rate": 0}
        analysis["institution_performance"] = {
            "institutions": institution_perf,
            "summary": inst_summary,
        }

        # 6) 家庭经济流动性
        status_rank = {EconomicStatus.SURVIVAL: 0, EconomicStatus.STABLE: 1, EconomicStatus.WEALTHY: 2}
        improved = deteriorated = unchanged = 0
        for fid, family in self.families.items():
            initial = self.initial_family_economic_status.get(fid, family.economic_status)
            if status_rank[family.economic_status] > status_rank[initial]:
                improved += 1
            elif status_rank[family.economic_status] < status_rank[initial]:
                deteriorated += 1
            else:
                unchanged += 1
        total_families = max(1, len(self.families))
        analysis["family_mobility"] = {
            "improved": improved,
            "deteriorated": deteriorated,
            "unchanged": unchanged,
            "improved_rate": improved / total_families,
            "deteriorated_rate": deteriorated / total_families,
        }

        # 7) 货币偏好与汇率
        currency_counts: Counter = Counter([getattr(q, "currency_code", "HKD") for q in self.qiaopi_history])
        currency_avg_rate: Dict[str, float] = defaultdict(float)
        currency_amounts: Dict[str, float] = defaultdict(float)
        currency_totals: Dict[str, int] = defaultdict(int)
        for q in self.qiaopi_history:
            code = getattr(q, "currency_code", "HKD")
            currency_totals[code] += 1
            currency_avg_rate[code] += getattr(q, "exchange_rate", 1.0)
            currency_amounts[code] += q.amount
        currency_usage = {}
        for code, cnt in currency_totals.items():
            currency_usage[code] = {
                "count": cnt,
                "share": cnt / max(1, len(self.qiaopi_history)),
                "avg_exchange_rate": currency_avg_rate[code] / max(1, cnt),
                "total_amount": currency_amounts[code],
            }
        analysis["currency_usage"] = currency_usage

        # 8) 网络规模与财富的相关性
        sizes: List[int] = []
        avg_savings: List[float] = []
        for network_id, member_ids in self.kinship_networks.items():
            network_migrants = [self.migrants[mid] for mid in member_ids if mid in self.migrants]
            if network_migrants:
                sizes.append(len(member_ids))
                avg_savings.append(sum(m.savings for m in network_migrants) / len(network_migrants))
        analysis["network_correlations"] = {
            "size_avg_savings_pearson": self._pearson(sizes, avg_savings) if sizes and avg_savings else None,
            "size_distribution": {
                "mean": (sum(sizes) / len(sizes)) if sizes else 0,
                "median": (statistics.median(sizes) if sizes else 0),
            }
        }

        return analysis

    # ---------- 工具函数 ----------
    def _gini(self, values: List[float]) -> float:
        vals = [v for v in values if v is not None and v >= 0]
        n = len(vals)
        if n == 0:
            return 0.0
        vals.sort()
        cumvals = 0.0
        cumindex = 0.0
        for i, v in enumerate(vals, start=1):
            cumvals += v
            cumindex += i * v
        if cumvals == 0:
            return 0.0
        # Gini = (2*sum(i*v_i)/(n*sum) - (n+1)/n)
        return max(0.0, min(1.0, (2.0 * cumindex) / (n * cumvals) - (n + 1.0) / n))

    def _top_share(self, values: List[float], share: float) -> float:
        vals = [v for v in values if v is not None and v >= 0]
        if not vals:
            return 0.0
        vals.sort(reverse=True)
        k = max(1, int(len(vals) * share))
        top_sum = sum(vals[:k])
        total = sum(vals)
        return 0.0 if total == 0 else top_sum / total

    def _percentile(self, values: List[float], p: float) -> float:
        vals = sorted([v for v in values if v is not None])
        if not vals:
            return 0.0
        if p <= 0:
            return vals[0]
        if p >= 100:
            return vals[-1]
        k = (len(vals) - 1) * (p / 100.0)
        f = math.floor(k)
        c = math.ceil(k)
        if f == c:
            return vals[int(k)]
        d0 = vals[f] * (c - k)
        d1 = vals[c] * (k - f)
        return d0 + d1

    def _pearson(self, xs: List[float], ys: List[float]) -> Optional[float]:
        if not xs or not ys or len(xs) != len(ys) or len(xs) < 2:
            return None
        mean_x = sum(xs) / len(xs)
        mean_y = sum(ys) / len(ys)
        num = sum((x - mean_x) * (y - mean_y) for x, y in zip(xs, ys))
        den_x = math.sqrt(sum((x - mean_x) ** 2 for x in xs))
        den_y = math.sqrt(sum((y - mean_y) ** 2 for y in ys))
        den = den_x * den_y
        if den == 0:
            return 0.0
        return max(-1.0, min(1.0, num / den))
    
    def export_data(self, format: str = "csv"):
        """导出仿真数据"""
        if format == "csv":
            self._export_csv()
        elif format == "json":
            self._export_json()
    
    def _export_csv(self):
        """导出CSV格式数据"""
        # 导出移民数据
        with open(f"{self.config.output_directory}/migrants.csv", 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['agent_id', 'origin_qiaoxiang', 'location', 'occupation', 
                           'income_level', 'savings', 'years_abroad', 'kinship_network_id'])
            
            for migrant in self.migrants.values():
                writer.writerow([
                    migrant.agent_id, migrant.origin_qiaoxiang.value, migrant.location.value,
                    migrant.occupation.value, migrant.income_level, migrant.savings,
                    migrant.years_abroad, migrant.kinship_network_id
                ])
        
        # 导出侨批数据
        with open(f"{self.config.output_directory}/qiaopi.csv", 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['message_id', 'sender_id', 'recipient_id', 'amount', 'qiaopi_type',
                           'status', 'send_time', 'delivery_time', 'origin', 'destination', 'currency', 'exchange_rate'])
            
            for qiaopi in self.qiaopi_history:
                writer.writerow([
                    qiaopi.message_id, qiaopi.sender_id, qiaopi.recipient_id, qiaopi.amount,
                    qiaopi.qiaopi_type.value, qiaopi.status.value, qiaopi.send_time,
                    qiaopi.delivery_time, qiaopi.origin.value, qiaopi.destination.value,
                    getattr(qiaopi, 'currency_code', ''), getattr(qiaopi, 'exchange_rate', 1.0)
                ])
    
    def _export_json(self):
        """导出JSON格式数据"""
        data = {
            "migrants": [vars(m) for m in self.migrants.values()],
            "families": [vars(f) for f in self.families.values()],
            "institutions": [vars(i) for i in self.institutions.values()],
            "qiaopi_history": [vars(q) for q in self.qiaopi_history],
            "statistics_history": self.statistics_history
        }
        
        with open(f"{self.config.output_directory}/simulation_data.json", 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)


# 修复协议类中的方法引用
class QiaopiInteractionProtocolFixed(QiaopiInteractionProtocol):
    """修复版本的侨批交互协议"""
    
    def __init__(self, environment: Environment, simulation_engine: 'QiaopiSimulationEngine'):
        super().__init__(environment)
        self.simulation_engine = simulation_engine
    
    def _get_institution_by_id(self, institution_id: str) -> Optional[InstitutionAgent]:
        return self.simulation_engine.institutions.get(institution_id)
    
    def _get_family_by_id(self, family_id: str) -> Optional[FamilyAgent]:
        return self.simulation_engine.families.get(family_id)


def create_default_simulation() -> QiaopiSimulationEngine:
    """创建默认配置的仿真"""
    config = SimulationConfig(
        start_year=1900,
        end_year=1950,
        num_migrants=500,
        num_families=400,
        num_institutions=15
    )
    return QiaopiSimulationEngine(config)


if __name__ == "__main__":
    # 示例运行
    simulation = create_default_simulation()
    results = simulation.run_simulation()
    
    print("\n=== 仿真结果摘要 ===")
    print(f"总仿真步数: {results['simulation_summary']['total_steps']}")
    print(f"处理侨批总数: {results['qiaopi_summary']['total_qiaopi_processed']}")
    print(f"成功递送率: {results['final_statistics']['qiaopi_statistics']['success_rate']:.2%}")
    print(f"总汇款金额: {results['final_statistics']['qiaopi_statistics']['total_amount_remitted']:.2f}")
    
    # 导出数据
    simulation.export_data("csv")
    print(f"数据已导出到: {simulation.config.output_directory}/")
