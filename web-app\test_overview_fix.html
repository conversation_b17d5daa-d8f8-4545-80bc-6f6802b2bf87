<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总览页面修复测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 400px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-tachometer-alt"></i> 系统总览修复测试</h1>
        
        <!-- 统计卡片 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number">13,403</div>
                    <div class="stat-label">真实侨批记录</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number">7</div>
                    <div class="stat-label">仿真场景</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">完成仿真</div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="chart-container">
                    <h5>仿真分布</h5>
                    <canvas id="simulationDistributionChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h5>场景类型</h5>
                    <canvas id="scenarioTypeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bug"></i> 测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p><i class="fas fa-spinner fa-spin"></i> 正在测试图表功能...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导入图表管理器
        class TestChartManager {
            constructor() {
                this.charts = new Map();
                this.colors = [
                    '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                    '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#d35400'
                ];
            }

            destroyChart(canvasId) {
                const existingChart = this.charts.get(canvasId);
                if (existingChart) {
                    try {
                        existingChart.destroy();
                        this.charts.delete(canvasId);
                    } catch (error) {
                        console.error(`销毁图表 ${canvasId} 时出错:`, error);
                    }
                }
            }

            createDoughnutChart(canvasId, data, options = {}) {
                const canvas = document.getElementById(canvasId);
                if (!canvas) return null;
                
                this.destroyChart(canvasId);

                const chart = new Chart(canvas, {
                    type: 'doughnut',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        ...options
                    }
                });
                
                this.charts.set(canvasId, chart);
                return chart;
            }

            createPieChart(canvasId, data, options = {}) {
                const canvas = document.getElementById(canvasId);
                if (!canvas) return null;
                
                this.destroyChart(canvasId);

                const chart = new Chart(canvas, {
                    type: 'pie',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        ...options
                    }
                });
                
                this.charts.set(canvasId, chart);
                return chart;
            }

            getColors(count) {
                return this.colors.slice(0, count);
            }
        }

        // 测试数据
        const testSimulations = [
            { status: 'completed', name: '侨批网络基础仿真' },
            { status: 'completed', name: '网络韧性仿真' },
            { status: 'completed', name: '链式移民仿真' },
            { status: 'running', name: '新仿真测试' }
        ];

        const testScenarios = [
            { name: '网络韧性分析' },
            { name: '景观演化分析' },
            { name: '链式移民分析' },
            { name: '信任机制研究' },
            { name: '政策影响分析' }
        ];

        // 测试函数
        function testOverviewCharts() {
            const chartManager = new TestChartManager();
            const resultsDiv = document.getElementById('testResults');
            
            try {
                // 1. 测试仿真分布图表
                const statusCount = testSimulations.reduce((acc, sim) => {
                    acc[sim.status] = (acc[sim.status] || 0) + 1;
                    return acc;
                }, {});

                const statusLabels = Object.keys(statusCount).map(status => {
                    switch(status) {
                        case 'completed': return '已完成';
                        case 'running': return '运行中';
                        case 'pending': return '等待中';
                        case 'failed': return '失败';
                        default: return status;
                    }
                });

                chartManager.createDoughnutChart('simulationDistributionChart', {
                    labels: statusLabels,
                    datasets: [{
                        data: Object.values(statusCount),
                        backgroundColor: ['#2ecc71', '#3498db', '#f39c12', '#e74c3c'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                });

                resultsDiv.innerHTML = '<p><i class="fas fa-check text-success"></i> 仿真分布图表: 创建成功</p>';

                // 2. 测试场景类型图表
                const typeCount = {};
                testScenarios.forEach(scenario => {
                    let type = '其他';
                    if (scenario.name.includes('网络韧性')) type = '网络韧性';
                    else if (scenario.name.includes('景观演化')) type = '景观演化';
                    else if (scenario.name.includes('链式移民')) type = '链式移民';
                    else if (scenario.name.includes('信任机制')) type = '信任机制';
                    else if (scenario.name.includes('政策')) type = '政策影响';
                    
                    typeCount[type] = (typeCount[type] || 0) + 1;
                });

                chartManager.createPieChart('scenarioTypeChart', {
                    labels: Object.keys(typeCount),
                    datasets: [{
                        data: Object.values(typeCount),
                        backgroundColor: chartManager.getColors(Object.keys(typeCount).length),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                });

                resultsDiv.innerHTML += '<p><i class="fas fa-check text-success"></i> 场景类型图表: 创建成功</p>';
                resultsDiv.innerHTML += '<p class="text-success fw-bold"><i class="fas fa-thumbs-up"></i> 所有总览图表功能正常！</p>';

            } catch (error) {
                console.error('图表测试失败:', error);
                resultsDiv.innerHTML += `<p><i class="fas fa-times text-danger"></i> 错误: ${error.message}</p>`;
            }
        }

        // 页面加载后测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testOverviewCharts, 500);
        });
    </script>
</body>
</html>