<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析模块可视化 - 侨批网络</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js for advanced visualizations -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        body {
            background: #f5f7fa;
        }
        
        .analysis-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .analysis-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .module-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }
        
        .module-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }
        
        .icon-timeseries { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .icon-trend { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .icon-correlation { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .icon-cluster { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .icon-prediction { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .icon-network { background: linear-gradient(135deg, #30cfd0 0%, #330867 100%); }
        .icon-event { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-complete {
            background: #d4edda;
            color: #155724;
        }
        
        .status-no-data {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chart-container {
            min-height: 300px;
            margin-top: 1rem;
        }
        
        .metric-box {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            margin: 0.5rem;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .heatmap-container {
            width: 100%;
            height: 400px;
            margin-top: 1rem;
        }
        
        .network-container {
            width: 100%;
            height: 500px;
            margin-top: 1rem;
            background: #fafafa;
            border-radius: 8px;
        }
        
        .tab-content {
            padding-top: 1.5rem;
        }
        
        .legend-item {
            display: inline-flex;
            align-items: center;
            margin-right: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 0.5rem;
        }
        
        .detail-modal .modal-dialog {
            max-width: 900px;
        }
        
        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .tooltip-custom {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.85rem;
            pointer-events: none;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-network-wired"></i> 侨批网络仿真
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/enhanced-simulation.html">
                    <i class="fas fa-arrow-left"></i> 返回增强仿真
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container my-4">
        <h2 class="mb-4">
            <i class="fas fa-chart-pie me-2"></i>
            分析模块可视化详情
        </h2>

        <!-- Tabs Navigation -->
        <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#timeseries">多时间序列</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#trend">趋势分析</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#correlation">相关性分析</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#clustering">聚类分析</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#prediction">预测分析</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#network">网络拓扑</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#event">事件影响</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- 多时间序列分析 -->
            <div class="tab-pane fade show active" id="timeseries">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-timeseries">
                                <i class="fas fa-chart-line text-white"></i>
                            </div>
                            多时间序列分析
                        </div>
                        <span class="status-badge status-complete">8条序列</span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="chart-container">
                                <canvas id="multiTimeseriesChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">6</div>
                                <div class="metric-label">分析维度</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">21</div>
                                <div class="metric-label">时间点</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">16</div>
                                <div class="metric-label">子序列</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">5</div>
                                <div class="metric-label">指标类型</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势分析 -->
            <div class="tab-pane fade" id="trend">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-trend">
                                <i class="fas fa-trending-up text-white"></i>
                            </div>
                            趋势分析
                        </div>
                        <span class="status-badge status-complete">已完成</span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>趋势指标</h5>
                            <div class="metric-box">
                                <div class="metric-value">+12.5%</div>
                                <div class="metric-label">年均增长率</div>
                            </div>
                            <div class="metric-box">
                                <div class="metric-value">0.92</div>
                                <div class="metric-label">趋势强度</div>
                            </div>
                            <div class="metric-box">
                                <div class="metric-value">3</div>
                                <div class="metric-label">转折点</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相关性分析 -->
            <div class="tab-pane fade" id="correlation">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-correlation">
                                <i class="fas fa-project-diagram text-white"></i>
                            </div>
                            相关性分析
                        </div>
                        <span class="status-badge status-complete">已生成</span>
                    </div>
                    
                    <div class="heatmap-container" id="correlationHeatmap"></div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h5>强相关性发现</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>变量对</th>
                                            <th>相关系数</th>
                                            <th>显著性</th>
                                            <th>解释</th>
                                        </tr>
                                    </thead>
                                    <tbody id="correlationTable">
                                        <!-- 动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 聚类分析 -->
            <div class="tab-pane fade" id="clustering">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-cluster">
                                <i class="fas fa-sitemap text-white"></i>
                            </div>
                            聚类分析
                        </div>
                        <span class="status-badge status-complete">3个群体</span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <canvas id="clusterChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h5>群体特征</h5>
                            <div id="clusterProfiles">
                                <!-- 动态生成群体描述 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div id="clusterScatter" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预测分析 -->
            <div class="tab-pane fade" id="prediction">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-prediction">
                                <i class="fas fa-crystal-ball text-white"></i>
                            </div>
                            预测分析
                        </div>
                        <span class="status-badge status-complete">5年预测</span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="chart-container">
                                <canvas id="predictionChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="metric-box">
                                <div class="metric-value">21,000</div>
                                <div class="metric-label">5年后预测值</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-box">
                                <div class="metric-value">85%</div>
                                <div class="metric-label">置信度</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-box">
                                <div class="metric-value">±1,500</div>
                                <div class="metric-label">预测区间</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h5>情景预测</h5>
                            <div id="scenarioChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 网络拓扑分析 -->
            <div class="tab-pane fade" id="network">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-network">
                                <i class="fas fa-network-wired text-white"></i>
                            </div>
                            网络拓扑分析
                        </div>
                        <span class="status-badge status-complete">已分析</span>
                    </div>
                    
                    <div class="network-container" id="networkGraph"></div>
                    
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">0.45</div>
                                <div class="metric-label">网络密度</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">5</div>
                                <div class="metric-label">中心节点</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">3.2</div>
                                <div class="metric-label">平均路径长度</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-box">
                                <div class="metric-value">0.68</div>
                                <div class="metric-label">聚集系数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 事件影响分析 -->
            <div class="tab-pane fade" id="event">
                <div class="analysis-card">
                    <div class="module-header">
                        <div class="module-title">
                            <div class="module-icon icon-event">
                                <i class="fas fa-exclamation-triangle text-white"></i>
                            </div>
                            事件影响分析
                        </div>
                        <span class="status-badge status-complete">已生成</span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div id="eventImpactChart" style="height: 400px;"></div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h5>重大事件影响</h5>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>事件</th>
                                            <th>时间</th>
                                            <th>影响范围</th>
                                            <th>影响程度</th>
                                            <th>恢复时间</th>
                                        </tr>
                                    </thead>
                                    <tbody id="eventTable">
                                        <!-- 动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detail Modal -->
    <div class="modal fade detail-modal" id="detailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">详细分析结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="exportData()">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Visualization Scripts -->
    <script src="js/analysis-visualizations.js"></script>
</body>
</html>