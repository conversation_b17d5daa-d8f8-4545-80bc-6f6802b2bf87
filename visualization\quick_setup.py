#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Setup Script - Install all dependencies
快速安装脚本 - 安装所有依赖
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except:
        return False

def main():
    print("=" * 60)
    print("Qiaopi Visualization System - Quick Setup")
    print("侨批可视化系统 - 快速安装")
    print("=" * 60)
    print()
    
    # Essential packages
    packages = [
        ("dash", "Dashboard framework"),
        ("plotly", "Plotting library"),
        ("dash-bootstrap-components", "Bootstrap components"),
        ("pandas", "Data analysis"),
        ("numpy", "Numerical computing"),
        ("flask", "Web framework"),
        ("flask-socketio", "WebSocket support"),
        ("python-socketio", "SocketIO client"),
        ("networkx", "Network analysis"),
        ("scikit-learn", "Machine learning"),
        ("scipy", "Scientific computing"),
        ("statsmodels", "Statistical models"),
        ("psutil", "System monitoring"),
        ("aiohttp", "Async HTTP"),
        ("aiohttp-cors", "CORS support"),
        ("dash-cytoscape", "Network visualization"),
    ]
    
    print("Installing required packages...")
    print("This may take a few minutes...\n")
    
    failed = []
    
    for package, description in packages:
        print(f"Installing {package} ({description})...", end=" ")
        if install_package(package):
            print("✓")
        else:
            print("✗")
            failed.append(package)
    
    print("\n" + "=" * 60)
    
    if failed:
        print(f"⚠️  Some packages failed to install: {', '.join(failed)}")
        print("\nTry installing manually:")
        print(f"pip install {' '.join(failed)}")
    else:
        print("✅ All packages installed successfully!")
        print("\nYou can now run the visualization system:")
        print("python run_visualization.py")
    
    print("=" * 60)
    
    return 0 if not failed else 1

if __name__ == "__main__":
    sys.exit(main())