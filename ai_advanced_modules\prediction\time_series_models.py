"""
时间序列预测模型
Time Series Prediction Models

实现了专门用于侨批网络预测的各种时间序列模型：
- Transformer预测器
- LSTM预测器
- 多尺度预测器
- 传统统计模型
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from dataclasses import dataclass
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PredictionConfig:
    """预测配置类"""
    # 模型参数
    hidden_dim: int = 256
    num_layers: int = 4
    num_heads: int = 8
    dropout: float = 0.1
    
    # 时间序列参数
    sequence_length: int = 60  # 历史序列长度
    prediction_horizon: int = 12  # 预测步长
    
    # 训练参数
    learning_rate: float = 1e-3
    batch_size: int = 32
    epochs: int = 100
    
    # 数据参数
    feature_dim: int = 20
    target_dim: int = 1
    
    # 预测类型
    prediction_type: str = "multi_step"  # "single_step", "multi_step"


class MultiScalePredictor(nn.Module):
    """多尺度时间序列预测器"""
    
    def __init__(self, config: PredictionConfig):
        super().__init__()
        self.config = config
        
        # 多尺度特征提取器
        self.scale_extractors = nn.ModuleDict({
            'short_term': self._create_scale_extractor(window_size=7),
            'medium_term': self._create_scale_extractor(window_size=30),
            'long_term': self._create_scale_extractor(window_size=90)
        })
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(config.hidden_dim * 3, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.hidden_dim)
        )
        
        # 预测头
        prediction_heads = {}
        for horizon in [1, 3, 6, 12, 24]:  # 多个预测时间跨度
            prediction_heads[f'horizon_{horizon}'] = nn.Sequential(
                nn.Linear(config.hidden_dim, config.hidden_dim // 2),
                nn.ReLU(),
                nn.Dropout(config.dropout),
                nn.Linear(config.hidden_dim // 2, horizon * config.target_dim)
            )
        self.prediction_heads = nn.ModuleDict(prediction_heads)
        
        logger.info(f"初始化多尺度预测器，特征维度: {config.feature_dim}")
    
    def _create_scale_extractor(self, window_size: int) -> nn.Module:
        """创建特定尺度的特征提取器"""
        return nn.Sequential(
            nn.Conv1d(self.config.feature_dim, self.config.hidden_dim, 
                     kernel_size=min(window_size, self.config.sequence_length)),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten()
        )
    
    def forward(self, x: torch.Tensor, prediction_horizon: int = 12) -> Dict[str, torch.Tensor]:
        """前向传播"""
        # x shape: (batch_size, sequence_length, feature_dim)
        x = x.transpose(1, 2)  # (batch_size, feature_dim, sequence_length)
        
        # 多尺度特征提取
        scale_features = []
        for scale_name, extractor in self.scale_extractors.items():
            try:
                scale_feature = extractor(x)
                scale_features.append(scale_feature)
            except:
                # 如果序列太短，用零填充
                scale_feature = torch.zeros(x.size(0), self.config.hidden_dim, device=x.device)
                scale_features.append(scale_feature)
        
        # 特征融合
        fused_features = torch.cat(scale_features, dim=-1)
        fused_features = self.feature_fusion(fused_features)
        
        # 多时间跨度预测
        predictions = {}
        for horizon_key, predictor in self.prediction_heads.items():
            horizon = int(horizon_key.split('_')[1])
            if horizon <= prediction_horizon:
                pred = predictor(fused_features)
                predictions[f'{horizon}_step'] = pred.view(-1, horizon, self.config.target_dim)
        
        return predictions


class TransformerPredictor(nn.Module):
    """基于Transformer的预测器"""
    
    def __init__(self, config: PredictionConfig):
        super().__init__()
        self.config = config
        
        # 输入投影
        self.input_projection = nn.Linear(config.feature_dim, config.hidden_dim)
        
        # 位置编码
        self.positional_encoding = self._create_positional_encoding(
            config.sequence_length, config.hidden_dim
        )
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.hidden_dim,
            nhead=config.num_heads,
            dim_feedforward=config.hidden_dim * 4,
            dropout=config.dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, config.num_layers)
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, config.prediction_horizon * config.target_dim)
        )
        
        logger.info(f"初始化Transformer预测器，序列长度: {config.sequence_length}")
    
    def _create_positional_encoding(self, max_len: int, d_model: int) -> torch.Tensor:
        """创建位置编码"""
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return pe.unsqueeze(0)  # (1, max_len, d_model)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # x shape: (batch_size, sequence_length, feature_dim)
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.positional_encoding[:, :seq_len, :].to(x.device)
        
        # Transformer编码
        encoded = self.transformer(x)
        
        # 使用最后时刻的表示进行预测
        last_hidden = encoded[:, -1, :]
        
        # 预测
        prediction = self.prediction_head(last_hidden)
        prediction = prediction.view(batch_size, self.config.prediction_horizon, 
                                   self.config.target_dim)
        
        return prediction


class LSTMPredictor(nn.Module):
    """LSTM预测器"""
    
    def __init__(self, config: PredictionConfig):
        super().__init__()
        self.config = config
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=config.feature_dim,
            hidden_size=config.hidden_dim,
            num_layers=config.num_layers,
            dropout=config.dropout if config.num_layers > 1 else 0,
            batch_first=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=config.hidden_dim,
            num_heads=config.num_heads,
            dropout=config.dropout,
            batch_first=True
        )
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, config.prediction_horizon * config.target_dim)
        )
        
        logger.info(f"初始化LSTM预测器，隐藏维度: {config.hidden_dim}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # x shape: (batch_size, sequence_length, feature_dim)
        
        # LSTM编码
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 注意力机制
        attended_out, attention_weights = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 使用最后时刻的输出
        last_output = attended_out[:, -1, :]
        
        # 预测
        prediction = self.prediction_head(last_output)
        prediction = prediction.view(-1, self.config.prediction_horizon, 
                                   self.config.target_dim)
        
        return prediction


class PredictionEvaluator:
    """预测评估器"""
    
    def __init__(self):
        self.metrics = {}
    
    def evaluate(self, predictions: np.ndarray, targets: np.ndarray, 
                metrics: List[str] = None) -> Dict[str, float]:
        """评估预测结果"""
        if metrics is None:
            metrics = ['mse', 'mae', 'rmse', 'mape', 'r2']
        
        results = {}
        
        # 展平预测和目标
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()
        
        for metric in metrics:
            if metric == 'mse':
                results[metric] = mean_squared_error(target_flat, pred_flat)
            elif metric == 'mae':
                results[metric] = mean_absolute_error(target_flat, pred_flat)
            elif metric == 'rmse':
                results[metric] = np.sqrt(mean_squared_error(target_flat, pred_flat))
            elif metric == 'mape':
                results[metric] = np.mean(np.abs((target_flat - pred_flat) / 
                                                np.maximum(np.abs(target_flat), 1e-8))) * 100
            elif metric == 'r2':
                results[metric] = r2_score(target_flat, pred_flat)
        
        return results


# 使用示例
def main():
    """主函数示例"""
    # 创建配置
    config = PredictionConfig(
        hidden_dim=128,
        sequence_length=60,
        prediction_horizon=12,
        feature_dim=10,
        target_dim=1
    )
    
    # 创建模型
    model = MultiScalePredictor(config)
    
    # 创建示例数据
    batch_size = 16
    x = torch.randn(batch_size, config.sequence_length, config.feature_dim)
    
    # 前向传播
    predictions = model(x, prediction_horizon=12)
    
    for horizon, pred in predictions.items():
        print(f"{horizon} 预测形状: {pred.shape}")


if __name__ == "__main__":
    main()
