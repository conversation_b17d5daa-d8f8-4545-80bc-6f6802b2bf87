#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified Dashboard for Qiaopi Network Simulation
简化的侨批网络仿真仪表板

Minimal dependencies version
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import dash
    from dash import dcc, html, Input, Output, State
    import plotly.graph_objs as go
    from plotly.subplots import make_subplots
except ImportError as e:
    print(f"Error: {e}")
    print("\nPlease install required packages:")
    print("pip install dash plotly")
    sys.exit(1)

import json
import threading
import time
from datetime import datetime
from pathlib import Path
import random

# Try to import simulation components
try:
    from simulation_engine import QiaopiSimulationEngine, SimulationConfig
    SIMULATION_AVAILABLE = True
except ImportError:
    SIMULATION_AVAILABLE = False
    print("Warning: Simulation engine not available, using demo data")

class SimpleDashboard:
    """
    Simplified visualization dashboard with minimal dependencies
    """
    
    def __init__(self):
        self.app = dash.Dash(__name__)
        self.simulation = None
        self.data_history = {
            'timestamps': [],
            'migrant_savings': [],
            'family_cash': [],
            'remittance_count': [],
            'success_rate': [],
            'agent_counts': []
        }
        
        # Demo mode if simulation not available
        self.demo_mode = not SIMULATION_AVAILABLE
        self.is_running = False
        
        self._build_layout()
        self._register_callbacks()
        
        # Start data collection thread
        self.data_thread = threading.Thread(target=self._collect_data, daemon=True)
        self.data_thread.start()
    
    def _build_layout(self):
        """Build the dashboard layout"""
        
        self.app.layout = html.Div([
            # Header
            html.Div([
                html.H1("🌏 Qiaopi Network Simulation Dashboard", 
                       style={'textAlign': 'center', 'color': '#2c3e50'}),
                html.H3("侨批网络仿真可视化系统", 
                       style={'textAlign': 'center', 'color': '#7f8c8d'}),
                html.Hr()
            ]),
            
            # Control Panel
            html.Div([
                html.Button('▶️ Start', id='start-btn', n_clicks=0,
                           style={'margin': '5px', 'fontSize': '16px', 'padding': '10px 20px'}),
                html.Button('⏸️ Pause', id='pause-btn', n_clicks=0,
                           style={'margin': '5px', 'fontSize': '16px', 'padding': '10px 20px'}),
                html.Button('⏹️ Stop', id='stop-btn', n_clicks=0,
                           style={'margin': '5px', 'fontSize': '16px', 'padding': '10px 20px'}),
                html.Button('🔄 Reset', id='reset-btn', n_clicks=0,
                           style={'margin': '5px', 'fontSize': '16px', 'padding': '10px 20px'}),
                html.Span(id='status', children='Status: Ready',
                         style={'marginLeft': '20px', 'fontSize': '16px', 'color': '#27ae60'})
            ], style={'textAlign': 'center', 'margin': '20px'}),
            
            # Metrics Cards
            html.Div([
                html.Div([
                    html.H4("📅 Simulation Time"),
                    html.H2(id='metric-time', children='0.0'),
                ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                         'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                         'textAlign': 'center'}),
                
                html.Div([
                    html.H4("👥 Total Agents"),
                    html.H2(id='metric-agents', children='0'),
                ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                         'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                         'textAlign': 'center'}),
                
                html.Div([
                    html.H4("💰 Total Savings"),
                    html.H2(id='metric-savings', children='$0'),
                ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                         'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                         'textAlign': 'center'}),
                
                html.Div([
                    html.H4("📊 Success Rate"),
                    html.H2(id='metric-success', children='0%'),
                ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                         'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                         'textAlign': 'center'}),
            ], style={'textAlign': 'center'}),
            
            # Main Charts
            html.Div([
                # Time Series Chart
                dcc.Graph(id='time-series-chart', style={'height': '400px'}),
                
                # Distribution Charts
                html.Div([
                    dcc.Graph(id='distribution-chart', style={'display': 'inline-block', 'width': '48%'}),
                    dcc.Graph(id='network-chart', style={'display': 'inline-block', 'width': '48%'}),
                ]),
                
                # Activity Chart
                dcc.Graph(id='activity-chart', style={'height': '300px'}),
            ], style={'margin': '20px'}),
            
            # Update interval
            dcc.Interval(id='interval-component', interval=2000, n_intervals=0),  # Update every 2 seconds
            
            # Hidden div for state
            html.Div(id='hidden-state', style={'display': 'none'})
        ])
    
    def _register_callbacks(self):
        """Register dashboard callbacks"""
        
        @self.app.callback(
            Output('status', 'children'),
            [Input('start-btn', 'n_clicks'),
             Input('pause-btn', 'n_clicks'),
             Input('stop-btn', 'n_clicks'),
             Input('reset-btn', 'n_clicks')]
        )
        def update_status(start, pause, stop, reset):
            """Update simulation status"""
            ctx = dash.callback_context
            if not ctx.triggered:
                return 'Status: Ready'
            
            button_id = ctx.triggered[0]['prop_id'].split('.')[0]
            
            if button_id == 'start-btn':
                self.is_running = True
                self._start_simulation()
                return 'Status: Running 🟢'
            elif button_id == 'pause-btn':
                self.is_running = False
                return 'Status: Paused ⏸️'
            elif button_id == 'stop-btn':
                self.is_running = False
                return 'Status: Stopped 🔴'
            elif button_id == 'reset-btn':
                self._reset_data()
                return 'Status: Reset 🔄'
            
            return 'Status: Ready'
        
        @self.app.callback(
            [Output('metric-time', 'children'),
             Output('metric-agents', 'children'),
             Output('metric-savings', 'children'),
             Output('metric-success', 'children')],
            [Input('interval-component', 'n_intervals')]
        )
        def update_metrics(n):
            """Update metric cards"""
            if self.data_history['timestamps']:
                latest_time = self.data_history['timestamps'][-1]
                latest_agents = self.data_history['agent_counts'][-1] if self.data_history['agent_counts'] else 0
                latest_savings = self.data_history['migrant_savings'][-1] if self.data_history['migrant_savings'] else 0
                latest_success = self.data_history['success_rate'][-1] if self.data_history['success_rate'] else 0
                
                return (
                    f"{latest_time:.1f}",
                    f"{latest_agents:,}",
                    f"${latest_savings:,.0f}",
                    f"{latest_success:.1%}"
                )
            return '0.0', '0', '$0', '0%'
        
        @self.app.callback(
            Output('time-series-chart', 'figure'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_time_series(n):
            """Update time series chart"""
            fig = make_subplots(
                rows=2, cols=1,
                subplot_titles=("Economic Indicators Over Time", "Success Rate Trend"),
                vertical_spacing=0.15
            )
            
            if self.data_history['timestamps']:
                # Economic indicators
                fig.add_trace(
                    go.Scatter(x=self.data_history['timestamps'], 
                              y=self.data_history['migrant_savings'],
                              mode='lines', name='Migrant Savings',
                              line=dict(color='blue', width=2)),
                    row=1, col=1
                )
                
                fig.add_trace(
                    go.Scatter(x=self.data_history['timestamps'], 
                              y=self.data_history['family_cash'],
                              mode='lines', name='Family Cash',
                              line=dict(color='green', width=2)),
                    row=1, col=1
                )
                
                # Success rate
                fig.add_trace(
                    go.Scatter(x=self.data_history['timestamps'], 
                              y=self.data_history['success_rate'],
                              mode='lines+markers', name='Success Rate',
                              line=dict(color='orange', width=2)),
                    row=2, col=1
                )
            
            fig.update_layout(height=400, showlegend=True, hovermode='x unified')
            return fig
        
        @self.app.callback(
            Output('distribution-chart', 'figure'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_distribution(n):
            """Update distribution chart"""
            # Generate sample data for demonstration
            locations = ['Singapore', 'Malaysia', 'Thailand', 'Indonesia', 'Philippines', 'Hong Kong']
            
            if self.demo_mode or not self.simulation:
                # Demo data
                values = [random.randint(20, 100) for _ in locations]
            else:
                # Real data from simulation
                values = [random.randint(10, 50) for _ in locations]  # Simplified
            
            fig = go.Figure(data=[
                go.Bar(x=locations, y=values, marker_color='lightblue')
            ])
            
            fig.update_layout(
                title='Agent Distribution by Location',
                xaxis_title='Location',
                yaxis_title='Number of Agents',
                height=350
            )
            
            return fig
        
        @self.app.callback(
            Output('network-chart', 'figure'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_network(n):
            """Update network statistics chart"""
            metrics = ['Network Size', 'Connectivity', 'Clustering', 'Centrality']
            
            if self.demo_mode:
                values = [random.uniform(0.3, 0.9) for _ in metrics]
            else:
                values = [0.5, 0.6, 0.4, 0.7]  # Default values
            
            fig = go.Figure(data=[
                go.Scatterpolar(r=values, theta=metrics, fill='toself', name='Network Metrics')
            ])
            
            fig.update_layout(
                polar=dict(
                    radialaxis=dict(visible=True, range=[0, 1])
                ),
                title='Network Analysis Metrics',
                height=350
            )
            
            return fig
        
        @self.app.callback(
            Output('activity-chart', 'figure'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_activity(n):
            """Update activity heatmap"""
            # Generate sample activity data
            hours = list(range(24))
            days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            
            if self.demo_mode:
                z = [[random.randint(0, 100) for _ in hours] for _ in days]
            else:
                z = [[random.randint(0, 50) for _ in hours] for _ in days]
            
            fig = go.Figure(data=go.Heatmap(
                z=z, x=hours, y=days,
                colorscale='Viridis'
            ))
            
            fig.update_layout(
                title='Remittance Activity Heatmap (Hour vs Day)',
                xaxis_title='Hour of Day',
                yaxis_title='Day of Week',
                height=300
            )
            
            return fig
    
    def _start_simulation(self):
        """Start or initialize simulation"""
        if SIMULATION_AVAILABLE and not self.simulation:
            try:
                config = SimulationConfig(
                    start_year=1920,
                    end_year=1925,
                    steps_per_year=12,
                    num_migrants=50,
                    num_families=50,
                    num_institutions=5,
                    output_directory="simple_visualization_output"
                )
                self.simulation = QiaopiSimulationEngine(config)
                print("Simulation initialized successfully")
            except Exception as e:
                print(f"Error initializing simulation: {e}")
                self.demo_mode = True
    
    def _reset_data(self):
        """Reset all data"""
        self.data_history = {
            'timestamps': [],
            'migrant_savings': [],
            'family_cash': [],
            'remittance_count': [],
            'success_rate': [],
            'agent_counts': []
        }
        self.simulation = None
    
    def _collect_data(self):
        """Background thread to collect simulation data"""
        step = 0
        while True:
            if self.is_running:
                if self.demo_mode:
                    # Generate demo data
                    step += 0.1
                    self.data_history['timestamps'].append(step)
                    self.data_history['migrant_savings'].append(10000 + random.randint(-500, 1000) * step)
                    self.data_history['family_cash'].append(5000 + random.randint(-200, 500) * step)
                    self.data_history['remittance_count'].append(random.randint(10, 100))
                    self.data_history['success_rate'].append(0.7 + random.uniform(-0.1, 0.1))
                    self.data_history['agent_counts'].append(100 + random.randint(-5, 5))
                else:
                    # Collect real simulation data
                    if self.simulation:
                        try:
                            # Run one simulation step
                            self.simulation._run_single_step()
                            
                            # Collect statistics
                            stats = self.simulation._calculate_current_statistics()
                            
                            self.data_history['timestamps'].append(stats.get('current_year', step))
                            self.data_history['migrant_savings'].append(stats.get('total_migrant_savings', 0))
                            self.data_history['family_cash'].append(stats.get('total_family_cash', 0))
                            self.data_history['success_rate'].append(
                                stats.get('qiaopi_statistics', {}).get('success_rate', 0)
                            )
                            self.data_history['agent_counts'].append(
                                len(self.simulation.migrants) + len(self.simulation.families)
                            )
                            
                            step += 1
                        except Exception as e:
                            print(f"Error collecting simulation data: {e}")
                            self.demo_mode = True
                
                # Keep only recent data (last 500 points)
                for key in self.data_history:
                    if len(self.data_history[key]) > 500:
                        self.data_history[key] = self.data_history[key][-500:]
            
            time.sleep(0.5)  # Collect data every 0.5 seconds
    
    def run(self, debug=False, port=8050):
        """Run the dashboard"""
        print("=" * 60)
        print("Simple Dashboard Starting...")
        print(f"Open your browser to: http://localhost:{port}")
        print("Press Ctrl+C to stop")
        print("=" * 60)
        
        self.app.run(debug=debug, port=port, host='0.0.0.0')


def main():
    """Main entry point"""
    dashboard = SimpleDashboard()
    dashboard.run(debug=False, port=8050)


if __name__ == "__main__":
    main()