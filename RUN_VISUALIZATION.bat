@echo off
echo ========================================
echo Qiaopi Visualization System
echo 侨批可视化系统
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed
    echo Please install Python from https://www.python.org
    pause
    exit /b 1
)

echo [1] Installing required packages...
echo.

REM Install basic requirements
pip install dash plotly pandas numpy

if %errorlevel% neq 0 (
    echo.
    echo [WARNING] Some packages may have failed to install
    echo Continuing anyway...
    echo.
)

echo.
echo [2] Starting visualization dashboard...
echo.
echo Dashboard will open at: http://localhost:8050
echo Press Ctrl+C to stop
echo.
echo ========================================
echo.

REM Try to run advanced dashboard first
if exist "visualization\simple_dashboard.py" (
    python visualization\simple_dashboard.py
) else (
    REM Run the installation script
    python install_and_run.py
)

pause