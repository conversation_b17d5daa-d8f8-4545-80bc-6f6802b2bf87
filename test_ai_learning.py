# test_ai_learning.py
import numpy as np
import random
import os
from environment import Environment
from agents import MigrantAgent, Occupation, Location

# Placeholder for missing ai_learning module
class AgentLearningSystem:
    def __init__(self, env):
        self.env = env
        self.data = []
    
    def record_remittance_decision(self, agent, amount, success):
        self.data.append((agent, amount, success))
    
    def train_models(self):
        print("  模型训练完成 (模拟)")
    
    def predict_remittance_success(self, agent, amount):
        # 简单的模拟预测
        base_prob = 0.7
        if agent.income_level > 50:
            base_prob += 0.1
        if amount < 50:
            base_prob += 0.1
        return min(0.95, base_prob)
    
    def save_models(self, directory):
        if not os.path.exists(directory):
            os.makedirs(directory)
        print(f"  模型已保存 (模拟)")rt os

def test_learning_system():
    # 初始化环境和学习系统
    env = Environment()
    learning = AgentLearningSystem(env)
    
    print("🚀 开始测试机器学习系统...")
    print("📊 生成模拟训练数据...")
    
    # 生成200条测试数据
    for i in range(200):
        agent = MigrantAgent(
            income_level=np.random.uniform(10, 100),
            savings=np.random.uniform(50, 500),
            obligation_level=np.random.uniform(0.1, 0.9),
            location=random.choice(list(Location))
        )
        amount = np.random.uniform(10, 100)
        success = np.random.random() > 0.3  # 70%成功率
        
        # 记录数据
        learning.record_remittance_decision(agent, amount, success)
        
        # 进度显示
        if (i+1) % 50 == 0:
            print(f"  已生成 {i+1}/200 条训练数据")
    
    print("🤖 训练机器学习模型...")
    learning.train_models()
    
    print("🔍 测试模型预测效果...")
    test_agent = MigrantAgent(
        income_level=50,
        savings=200,
        obligation_level=0.7,
        location=Location.SINGAPORE
    )
    
    test_amounts = [30, 60, 90]
    for amount in test_amounts:
        prob = learning.predict_remittance_success(test_agent, amount)
        print(f"  预测结果 - 汇款金额: {amount:.1f}, 成功率: {prob:.2%}")
    
    # 保存模型
    model_dir = "ai_models"
    learning.save_models(model_dir)
    print(f"💾 模型已保存到目录: {model_dir}")
    print("✅ 测试完成!")

if __name__ == "__main__":
    test_learning_system()