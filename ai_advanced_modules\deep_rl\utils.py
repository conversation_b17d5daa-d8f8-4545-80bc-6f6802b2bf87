"""
深度强化学习实用工具
Deep Reinforcement Learning Utilities

提供支持强化学习训练和评估的实用工具，包括：
- 状态编码器和解码器
- 数据预处理工具
- 可视化工具
- 性能评估指标
- 模型分析工具
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, List, Tuple, Any, Optional, Union
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import logging
from dataclasses import dataclass
import pickle
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class StateEncoding:
    """状态编码配置"""
    economic_features: List[str]
    social_features: List[str] 
    environmental_features: List[str]
    historical_features: List[str]
    personal_features: List[str]
    normalization_method: str = "standard"  # "standard", "minmax", "none"


class AgentStateEncoder:
    """智能体状态编码器"""
    
    def __init__(self, state_config: StateEncoding):
        self.config = state_config
        self.feature_names = self._get_all_feature_names()
        self.scalers = {}
        self._initialize_scalers()
        
    def _get_all_feature_names(self) -> List[str]:
        """获取所有特征名称"""
        all_features = []
        all_features.extend(self.config.economic_features)
        all_features.extend(self.config.social_features)
        all_features.extend(self.config.environmental_features)
        all_features.extend(self.config.historical_features)
        all_features.extend(self.config.personal_features)
        return all_features
    
    def _initialize_scalers(self):
        """初始化标准化器"""
        if self.config.normalization_method == "standard":
            for feature_group in ["economic", "social", "environmental", "historical", "personal"]:
                self.scalers[feature_group] = StandardScaler()
        elif self.config.normalization_method == "minmax":
            for feature_group in ["economic", "social", "environmental", "historical", "personal"]:
                self.scalers[feature_group] = MinMaxScaler()
    
    def encode_state(self, raw_state: Dict[str, Any], agent_type: str = "migrant") -> np.ndarray:
        """编码原始状态为向量"""
        encoded_features = []
        
        # 经济特征编码
        economic_values = []
        for feature in self.config.economic_features:
            value = raw_state.get(feature, 0.0)
            if isinstance(value, (list, tuple)):
                economic_values.extend(value)
            else:
                economic_values.append(float(value))
        encoded_features.extend(economic_values)
        
        # 社会特征编码
        social_values = []
        for feature in self.config.social_features:
            value = raw_state.get(feature, 0.0)
            if isinstance(value, (list, tuple)):
                social_values.extend(value)
            else:
                social_values.append(float(value))
        encoded_features.extend(social_values)
        
        # 环境特征编码
        env_values = []
        for feature in self.config.environmental_features:
            value = raw_state.get(feature, 0.0)
            if isinstance(value, (list, tuple)):
                env_values.extend(value)
            else:
                env_values.append(float(value))
        encoded_features.extend(env_values)
        
        # 历史特征编码
        hist_values = []
        for feature in self.config.historical_features:
            value = raw_state.get(feature, 0.0)
            if isinstance(value, (list, tuple)):
                hist_values.extend(value)
            else:
                hist_values.append(float(value))
        encoded_features.extend(hist_values)
        
        # 个人特征编码
        personal_values = []
        for feature in self.config.personal_features:
            value = raw_state.get(feature, 0.0)
            if isinstance(value, (list, tuple)):
                personal_values.extend(value)
            else:
                personal_values.append(float(value))
        encoded_features.extend(personal_values)
        
        # 智能体类型编码（one-hot）
        agent_type_encoding = self._encode_agent_type(agent_type)
        encoded_features.extend(agent_type_encoding)
        
        return np.array(encoded_features, dtype=np.float32)
    
    def _encode_agent_type(self, agent_type: str) -> List[float]:
        """编码智能体类型"""
        type_mapping = {
            "migrant": [1.0, 0.0, 0.0],
            "family": [0.0, 1.0, 0.0], 
            "institution": [0.0, 0.0, 1.0]
        }
        return type_mapping.get(agent_type, [0.0, 0.0, 0.0])
    
    def fit_scalers(self, state_data: List[Dict[str, Any]]):
        """拟合标准化器"""
        if self.config.normalization_method == "none":
            return
        
        # 收集各类特征数据
        feature_groups = {
            "economic": [],
            "social": [],
            "environmental": [],
            "historical": [],
            "personal": []
        }
        
        for state in state_data:
            # 经济特征
            economic_values = [state.get(f, 0.0) for f in self.config.economic_features]
            feature_groups["economic"].append(economic_values)
            
            # 社会特征
            social_values = [state.get(f, 0.0) for f in self.config.social_features]
            feature_groups["social"].append(social_values)
            
            # 环境特征
            env_values = [state.get(f, 0.0) for f in self.config.environmental_features]
            feature_groups["environmental"].append(env_values)
            
            # 历史特征
            hist_values = [state.get(f, 0.0) for f in self.config.historical_features]
            feature_groups["historical"].append(hist_values)
            
            # 个人特征
            personal_values = [state.get(f, 0.0) for f in self.config.personal_features]
            feature_groups["personal"].append(personal_values)
        
        # 拟合标准化器
        for group_name, data in feature_groups.items():
            if data and group_name in self.scalers:
                self.scalers[group_name].fit(np.array(data))
        
        logger.info("状态编码器的标准化器已拟合")
    
    def normalize_features(self, features: np.ndarray, feature_group: str) -> np.ndarray:
        """标准化特征"""
        if (self.config.normalization_method == "none" or 
            feature_group not in self.scalers):
            return features
        
        scaler = self.scalers[feature_group]
        if hasattr(scaler, 'transform'):
            return scaler.transform(features.reshape(1, -1)).flatten()
        
        return features
    
    def save_scalers(self, filepath: str):
        """保存标准化器"""
        with open(filepath, 'wb') as f:
            pickle.dump(self.scalers, f)
        logger.info(f"状态编码器已保存到 {filepath}")
    
    def load_scalers(self, filepath: str):
        """加载标准化器"""
        with open(filepath, 'rb') as f:
            self.scalers = pickle.load(f)
        logger.info(f"状态编码器已从 {filepath} 加载")


class ActionDecoder:
    """动作解码器"""
    
    def __init__(self, agent_type: str):
        self.agent_type = agent_type
        self.action_mappings = self._initialize_action_mappings()
    
    def _initialize_action_mappings(self) -> Dict[int, Dict[str, Any]]:
        """初始化动作映射"""
        if self.agent_type == "migrant":
            return {
                0: {"type": "no_remittance", "amount": 0.0, "priority": "none"},
                1: {"type": "small_remittance", "amount": "low", "priority": "routine"},
                2: {"type": "medium_remittance", "amount": "medium", "priority": "routine"},
                3: {"type": "large_remittance", "amount": "high", "priority": "routine"},
                4: {"type": "emergency_remittance", "amount": "emergency", "priority": "urgent"},
                5: {"type": "investment_remittance", "amount": "investment", "priority": "strategic"},
                6: {"type": "festival_remittance", "amount": "festival", "priority": "cultural"},
                7: {"type": "change_institution", "amount": "previous", "priority": "strategic"},
                8: {"type": "explore_new_institution", "amount": "small", "priority": "exploratory"},
                9: {"type": "wait_and_observe", "amount": 0.0, "priority": "cautious"}
            }
        elif self.agent_type == "family":
            return {
                0: {"type": "save_money", "allocation": {"savings": 0.8, "consumption": 0.2}},
                1: {"type": "invest_education", "allocation": {"education": 0.6, "other": 0.4}},
                2: {"type": "invest_property", "allocation": {"property": 0.7, "other": 0.3}},
                3: {"type": "start_business", "allocation": {"business": 0.8, "reserve": 0.2}},
                4: {"type": "emergency_use", "allocation": {"emergency": 1.0}},
                5: {"type": "festival_celebration", "allocation": {"festival": 0.5, "other": 0.5}},
                6: {"type": "community_investment", "allocation": {"community": 0.4, "family": 0.6}},
                7: {"type": "request_more_remittance", "urgency": "high"}
            }
        elif self.agent_type == "institution":
            return {
                0: {"type": "maintain_status", "changes": {}},
                1: {"type": "reduce_fees", "fee_change": -0.005, "target": "market_share"},
                2: {"type": "increase_fees", "fee_change": 0.003, "target": "profit"},
                3: {"type": "improve_service", "investment": "service_quality", "amount": 0.1},
                4: {"type": "expand_network", "investment": "infrastructure", "amount": 0.15},
                5: {"type": "enhance_security", "investment": "security", "amount": 0.08},
                6: {"type": "marketing_campaign", "investment": "marketing", "amount": 0.05},
                7: {"type": "technology_upgrade", "investment": "technology", "amount": 0.12},
                8: {"type": "risk_management", "focus": "risk_reduction", "intensity": "high"},
                9: {"type": "partnership", "action": "form_alliance", "scope": "regional"},
                10: {"type": "differentiation", "strategy": "premium_service", "target": "high_value"},
                11: {"type": "cost_optimization", "focus": "efficiency", "target_saving": 0.07}
            }
        else:
            return {}
    
    def decode_action(self, action: Union[int, np.ndarray]) -> Dict[str, Any]:
        """解码动作"""
        if isinstance(action, np.ndarray):
            action = int(action.item())
        elif isinstance(action, torch.Tensor):
            action = int(action.item())
        
        return self.action_mappings.get(action, self.action_mappings.get(0, {}))
    
    def encode_action(self, action_dict: Dict[str, Any]) -> int:
        """编码动作（反向操作）"""
        for action_id, mapping in self.action_mappings.items():
            if mapping.get("type") == action_dict.get("type"):
                return action_id
        return 0
    
    def get_action_space_size(self) -> int:
        """获取动作空间大小"""
        return len(self.action_mappings)
    
    def get_action_description(self, action: int) -> str:
        """获取动作描述"""
        action_info = self.action_mappings.get(action, {})
        action_type = action_info.get("type", "unknown")
        
        if self.agent_type == "migrant":
            amount = action_info.get("amount", "")
            priority = action_info.get("priority", "")
            return f"{action_type} (金额: {amount}, 优先级: {priority})"
        elif self.agent_type == "family":
            allocation = action_info.get("allocation", {})
            return f"{action_type} (分配: {allocation})"
        elif self.agent_type == "institution":
            investment = action_info.get("investment", "")
            return f"{action_type} (投资: {investment})"
        
        return action_type


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.metrics_history = []
        self.episode_data = []
    
    def add_episode_data(self, episode_data: Dict[str, Any]):
        """添加回合数据"""
        self.episode_data.append(episode_data)
    
    def calculate_metrics(self, 
                         rewards: List[float], 
                         actions: List[int],
                         states: List[np.ndarray],
                         agent_type: str = "migrant") -> Dict[str, float]:
        """计算性能指标"""
        metrics = {}
        
        # 基本统计指标
        metrics['mean_reward'] = np.mean(rewards)
        metrics['std_reward'] = np.std(rewards)
        metrics['max_reward'] = np.max(rewards)
        metrics['min_reward'] = np.min(rewards)
        metrics['total_reward'] = np.sum(rewards)
        
        # 动作分布分析
        action_counts = np.bincount(actions)
        total_actions = len(actions)
        
        metrics['action_entropy'] = self._calculate_entropy(action_counts / total_actions)
        metrics['most_frequent_action'] = np.argmax(action_counts)
        metrics['action_diversity'] = len(np.unique(actions))
        
        # 奖励稳定性
        if len(rewards) > 1:
            reward_changes = np.diff(rewards)
            metrics['reward_volatility'] = np.std(reward_changes)
            metrics['reward_trend'] = np.polyfit(range(len(rewards)), rewards, 1)[0]
        
        # 学习效率
        if len(rewards) >= 10:
            early_rewards = rewards[:len(rewards)//2]
            late_rewards = rewards[len(rewards)//2:]
            metrics['learning_improvement'] = np.mean(late_rewards) - np.mean(early_rewards)
        
        # 状态空间探索
        if states:
            state_matrix = np.array(states)
            metrics['state_variance'] = np.mean(np.var(state_matrix, axis=0))
            
            # 使用PCA分析状态空间覆盖
            if len(states) > 10:
                pca = PCA(n_components=min(5, state_matrix.shape[1]))
                pca_states = pca.fit_transform(state_matrix)
                metrics['state_space_coverage'] = np.mean(np.var(pca_states, axis=0))
        
        # 智能体特定指标
        if agent_type == "migrant":
            # 汇款相关指标
            remittance_actions = [a for a in actions if a in [1, 2, 3, 4, 5, 6]]
            metrics['remittance_frequency'] = len(remittance_actions) / total_actions
            
            # 探索vs利用
            exploratory_actions = [a for a in actions if a in [7, 8, 9]]
            metrics['exploration_rate'] = len(exploratory_actions) / total_actions
            
        elif agent_type == "family":
            # 投资相关指标
            investment_actions = [a for a in actions if a in [1, 2, 3, 6]]
            metrics['investment_rate'] = len(investment_actions) / total_actions
            
        elif agent_type == "institution":
            # 策略调整指标
            strategy_actions = [a for a in actions if a in [1, 2, 3, 4, 7, 9, 10, 11]]
            metrics['strategy_adjustment_rate'] = len(strategy_actions) / total_actions
        
        return metrics
    
    def _calculate_entropy(self, probabilities: np.ndarray) -> float:
        """计算熵"""
        # 避免log(0)
        probabilities = probabilities[probabilities > 0]
        return -np.sum(probabilities * np.log(probabilities))
    
    def analyze_convergence(self, metrics_sequence: List[Dict[str, float]]) -> Dict[str, Any]:
        """分析收敛性"""
        convergence_analysis = {}
        
        if len(metrics_sequence) < 10:
            return convergence_analysis
        
        # 分析每个指标的收敛性
        for metric_name in metrics_sequence[0].keys():
            values = [m[metric_name] for m in metrics_sequence]
            
            # 计算移动平均
            window_size = min(10, len(values) // 4)
            moving_avg = np.convolve(values, np.ones(window_size)/window_size, mode='valid')
            
            # 检查是否收敛（最后25%的数据方差是否小于阈值）
            recent_data = moving_avg[-len(moving_avg)//4:]
            is_converged = np.var(recent_data) < 0.01
            
            convergence_analysis[metric_name] = {
                'converged': is_converged,
                'final_value': values[-1],
                'variance': np.var(recent_data),
                'trend': np.polyfit(range(len(values)), values, 1)[0]
            }
        
        return convergence_analysis
    
    def generate_performance_report(self, agent_id: str, save_path: Optional[str] = None) -> Dict[str, Any]:
        """生成性能报告"""
        if not self.episode_data:
            logger.warning("没有回合数据可用于生成报告")
            return {}
        
        # 汇总所有回合的数据
        all_rewards = []
        all_actions = []
        all_states = []
        
        for episode in self.episode_data:
            all_rewards.extend(episode.get('rewards', []))
            all_actions.extend(episode.get('actions', []))
            all_states.extend(episode.get('states', []))
        
        # 计算综合指标
        overall_metrics = self.calculate_metrics(all_rewards, all_actions, all_states)
        
        # 分析趋势
        episode_rewards = [np.sum(ep.get('rewards', [])) for ep in self.episode_data]
        episode_lengths = [len(ep.get('rewards', [])) for ep in self.episode_data]
        
        # 生成报告
        report = {
            'agent_id': agent_id,
            'total_episodes': len(self.episode_data),
            'total_steps': len(all_rewards),
            'overall_metrics': overall_metrics,
            'episode_statistics': {
                'mean_episode_reward': np.mean(episode_rewards),
                'std_episode_reward': np.std(episode_rewards),
                'mean_episode_length': np.mean(episode_lengths),
                'best_episode_reward': np.max(episode_rewards),
                'worst_episode_reward': np.min(episode_rewards)
            },
            'convergence_analysis': self.analyze_convergence(self.metrics_history),
            'learning_curve': {
                'episode_rewards': episode_rewards,
                'episode_lengths': episode_lengths
            }
        }
        
        # 保存报告
        if save_path:
            with open(save_path, 'w') as f:
                import json
                json.dump(report, f, indent=2, default=str)
            logger.info(f"性能报告已保存到 {save_path}")
        
        return report


class VisualizationTools:
    """可视化工具"""
    
    @staticmethod
    def plot_learning_curve(episode_rewards: List[float], 
                           window_size: int = 100,
                           title: str = "学习曲线",
                           save_path: Optional[str] = None):
        """绘制学习曲线"""
        plt.figure(figsize=(12, 6))
        
        # 原始奖励
        plt.subplot(1, 2, 1)
        plt.plot(episode_rewards, alpha=0.3, color='blue', label='原始奖励')
        
        # 移动平均
        if len(episode_rewards) >= window_size:
            moving_avg = np.convolve(episode_rewards, 
                                   np.ones(window_size)/window_size, 
                                   mode='valid')
            plt.plot(range(window_size-1, len(episode_rewards)), 
                    moving_avg, color='red', linewidth=2, 
                    label=f'{window_size}回合移动平均')
        
        plt.xlabel('回合')
        plt.ylabel('奖励')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 奖励分布
        plt.subplot(1, 2, 2)
        plt.hist(episode_rewards, bins=50, alpha=0.7, color='green')
        plt.xlabel('奖励')
        plt.ylabel('频率')
        plt.title('奖励分布')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    @staticmethod
    def plot_action_distribution(actions: List[int], 
                                action_decoder: ActionDecoder,
                                title: str = "动作分布",
                                save_path: Optional[str] = None):
        """绘制动作分布"""
        action_counts = np.bincount(actions)
        action_names = [action_decoder.get_action_description(i) 
                       for i in range(len(action_counts))]
        
        plt.figure(figsize=(15, 8))
        
        # 条形图
        plt.subplot(1, 2, 1)
        bars = plt.bar(range(len(action_counts)), action_counts)
        plt.xlabel('动作ID')
        plt.ylabel('频率')
        plt.title(title)
        plt.xticks(range(len(action_counts)))
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height}', ha='center', va='bottom')
        
        # 饼图
        plt.subplot(1, 2, 2)
        # 只显示使用过的动作
        used_actions = [(i, count, name) for i, (count, name) 
                       in enumerate(zip(action_counts, action_names)) if count > 0]
        
        if used_actions:
            labels = [f"动作{i}: {name[:30]}..." if len(name) > 30 else f"动作{i}: {name}" 
                     for i, count, name in used_actions]
            sizes = [count for i, count, name in used_actions]
            
            plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
            plt.title('动作比例分布')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    @staticmethod
    def plot_state_space_exploration(states: List[np.ndarray],
                                   title: str = "状态空间探索",
                                   save_path: Optional[str] = None):
        """绘制状态空间探索"""
        if len(states) < 10:
            logger.warning("状态数据不足，无法绘制状态空间探索图")
            return
        
        state_matrix = np.array(states)
        
        plt.figure(figsize=(15, 10))
        
        # PCA降维可视化
        plt.subplot(2, 2, 1)
        pca = PCA(n_components=2)
        pca_states = pca.fit_transform(state_matrix)
        
        plt.scatter(pca_states[:, 0], pca_states[:, 1], 
                   c=range(len(pca_states)), cmap='viridis', alpha=0.6)
        plt.colorbar(label='时间步')
        plt.xlabel(f'PC1 (解释方差: {pca.explained_variance_ratio_[0]:.2%})')
        plt.ylabel(f'PC2 (解释方差: {pca.explained_variance_ratio_[1]:.2%})')
        plt.title('PCA状态空间探索')
        
        # t-SNE降维可视化
        plt.subplot(2, 2, 2)
        if len(states) >= 50:  # t-SNE需要足够的数据点
            tsne = TSNE(n_components=2, random_state=42)
            tsne_states = tsne.fit_transform(state_matrix)
            
            plt.scatter(tsne_states[:, 0], tsne_states[:, 1],
                       c=range(len(tsne_states)), cmap='plasma', alpha=0.6)
            plt.colorbar(label='时间步')
            plt.xlabel('t-SNE 1')
            plt.ylabel('t-SNE 2')
            plt.title('t-SNE状态空间探索')
        
        # 特征重要性
        plt.subplot(2, 2, 3)
        feature_variance = np.var(state_matrix, axis=0)
        top_features = np.argsort(feature_variance)[-10:]  # 前10个最重要的特征
        
        plt.bar(range(len(top_features)), feature_variance[top_features])
        plt.xlabel('特征索引')
        plt.ylabel('方差')
        plt.title('特征重要性（方差）')
        plt.xticks(range(len(top_features)), top_features, rotation=45)
        
        # 状态轨迹
        plt.subplot(2, 2, 4)
        # 选择前3个最重要的特征绘制轨迹
        top_3_features = np.argsort(feature_variance)[-3:]
        time_steps = range(len(states))
        
        for i, feature_idx in enumerate(top_3_features):
            plt.plot(time_steps, state_matrix[:, feature_idx], 
                    label=f'特征 {feature_idx}', alpha=0.8)
        
        plt.xlabel('时间步')
        plt.ylabel('特征值')
        plt.title('关键特征轨迹')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    @staticmethod
    def plot_multi_agent_comparison(agent_reports: Dict[str, Dict],
                                   metric: str = "mean_reward",
                                   title: str = "多智能体性能对比",
                                   save_path: Optional[str] = None):
        """绘制多智能体性能对比"""
        agent_names = list(agent_reports.keys())
        metric_values = [report['overall_metrics'].get(metric, 0) 
                        for report in agent_reports.values()]
        
        plt.figure(figsize=(12, 6))
        
        # 条形图对比
        plt.subplot(1, 2, 1)
        bars = plt.bar(agent_names, metric_values)
        plt.xlabel('智能体')
        plt.ylabel(metric)
        plt.title(f'{title} - {metric}')
        plt.xticks(rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, metric_values):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height(),
                    f'{value:.3f}', ha='center', va='bottom')
        
        # 学习曲线对比
        plt.subplot(1, 2, 2)
        for agent_name, report in agent_reports.items():
            episode_rewards = report.get('learning_curve', {}).get('episode_rewards', [])
            if episode_rewards:
                plt.plot(episode_rewards, label=agent_name, alpha=0.7)
        
        plt.xlabel('回合')
        plt.ylabel('回合奖励')
        plt.title('学习曲线对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


# 数据预处理工具
class DataPreprocessor:
    """数据预处理工具"""
    
    @staticmethod
    def normalize_rewards(rewards: List[float], method: str = "z_score") -> List[float]:
        """标准化奖励"""
        rewards = np.array(rewards)
        
        if method == "z_score":
            mean_reward = np.mean(rewards)
            std_reward = np.std(rewards)
            if std_reward > 0:
                return ((rewards - mean_reward) / std_reward).tolist()
            else:
                return rewards.tolist()
        
        elif method == "min_max":
            min_reward = np.min(rewards)
            max_reward = np.max(rewards)
            if max_reward > min_reward:
                return ((rewards - min_reward) / (max_reward - min_reward)).tolist()
            else:
                return rewards.tolist()
        
        elif method == "robust":
            median_reward = np.median(rewards)
            mad = np.median(np.abs(rewards - median_reward))
            if mad > 0:
                return ((rewards - median_reward) / mad).tolist()
            else:
                return rewards.tolist()
        
        return rewards.tolist()
    
    @staticmethod
    def smooth_trajectory(values: List[float], window_size: int = 5) -> List[float]:
        """平滑轨迹数据"""
        if len(values) < window_size:
            return values
        
        smoothed = np.convolve(values, np.ones(window_size)/window_size, mode='same')
        return smoothed.tolist()
    
    @staticmethod
    def detect_outliers(values: List[float], method: str = "iqr") -> List[bool]:
        """检测异常值"""
        values = np.array(values)
        
        if method == "iqr":
            q1 = np.percentile(values, 25)
            q3 = np.percentile(values, 75)
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = (values < lower_bound) | (values > upper_bound)
            
        elif method == "z_score":
            mean_val = np.mean(values)
            std_val = np.std(values)
            
            z_scores = np.abs((values - mean_val) / std_val)
            outliers = z_scores > 3
        
        else:
            outliers = np.zeros(len(values), dtype=bool)
        
        return outliers.tolist()


# 使用示例
def main():
    """主函数示例"""
    # 创建状态编码配置
    state_config = StateEncoding(
        economic_features=['income_level', 'savings', 'debt_level'],
        social_features=['social_capital', 'network_size'],
        environmental_features=['political_stability', 'economic_growth'],
        historical_features=['success_rate', 'avg_remittance'],
        personal_features=['risk_tolerance', 'obligation_level']
    )
    
    # 创建状态编码器
    encoder = AgentStateEncoder(state_config)
    
    # 示例状态
    test_state = {
        'income_level': 100.0,
        'savings': 500.0,
        'social_capital': 0.7,
        'political_stability': 0.8,
        'success_rate': 0.85
    }
    
    # 编码状态
    encoded_state = encoder.encode_state(test_state, "migrant")
    print(f"编码后状态维度: {encoded_state.shape}")
    
    # 创建动作解码器
    action_decoder = ActionDecoder("migrant")
    
    # 解码动作
    action = 3
    decoded_action = action_decoder.decode_action(action)
    print(f"动作 {action} 解码结果: {decoded_action}")
    
    # 性能分析示例
    analyzer = PerformanceAnalyzer()
    
    # 模拟一些数据
    rewards = [0.1, 0.3, 0.5, 0.7, 0.9, 0.8, 0.6, 0.4, 0.2, 0.8]
    actions = [1, 2, 3, 1, 2, 1, 3, 2, 1, 3]
    states = [np.random.randn(20) for _ in range(10)]
    
    # 计算指标
    metrics = analyzer.calculate_metrics(rewards, actions, states, "migrant")
    print(f"性能指标: {metrics}")
    
    # 可视化工具示例
    VisualizationTools.plot_learning_curve(rewards, window_size=3, title="示例学习曲线")


if __name__ == "__main__":
    main()
