// AI智能体调试工具
console.log('🔧 AI智能体调试工具加载中...');

function debugAgentsSection() {
    console.log('\n🔍 开始调试AI智能体页面...');
    
    // 1. 检查HTML元素是否存在
    const agentsSection = document.getElementById('agents-section');
    console.log('1️⃣ agents-section元素:', agentsSection);
    
    if (!agentsSection) {
        console.error('❌ 未找到agents-section元素');
        return false;
    }
    
    // 2. 检查当前显示状态
    const currentDisplay = window.getComputedStyle(agentsSection).display;
    const currentVisibility = window.getComputedStyle(agentsSection).visibility;
    console.log('2️⃣ 当前显示状态:', { display: currentDisplay, visibility: currentVisibility });
    
    // 3. 检查内容是否存在
    const hasContent = agentsSection.innerHTML.trim().length > 0;
    console.log('3️⃣ 是否有内容:', hasContent);
    console.log('3️⃣ 内容长度:', agentsSection.innerHTML.length);
    
    // 4. 强制显示
    agentsSection.style.display = 'block';
    agentsSection.style.visibility = 'visible';
    agentsSection.style.opacity = '1';
    console.log('4️⃣ 已强制设置为可见');
    
    // 5. 检查子元素
    const templates = document.getElementById('agentTemplatesGrid');
    const myAgents = document.getElementById('myAgentsGrid');
    console.log('5️⃣ 子元素:', { templates, myAgents });
    
    // 6. 显示调试信息到页面
    showDebugInfo(agentsSection);
    
    return true;
}

function showDebugInfo(agentsSection) {
    // 创建调试信息显示
    const debugDiv = document.createElement('div');
    debugDiv.id = 'debug-info';
    debugDiv.className = 'alert alert-warning';
    debugDiv.innerHTML = `
        <h6><i class="fas fa-bug me-2"></i>调试信息</h6>
        <p><strong>AI智能体页面调试状态:</strong></p>
        <ul>
            <li>✅ agents-section元素已找到</li>
            <li>✅ 已强制设置为可见状态</li>
            <li>✅ 内容长度: ${agentsSection.innerHTML.length} 字符</li>
            <li>✅ 当前时间: ${new Date().toLocaleString()}</li>
        </ul>
        <button class="btn btn-primary btn-sm" onclick="removeDebugInfo()">移除调试信息</button>
    `;
    
    // 插入到agents-section的最前面
    agentsSection.insertBefore(debugDiv, agentsSection.firstChild);
}

function removeDebugInfo() {
    const debugDiv = document.getElementById('debug-info');
    if (debugDiv) {
        debugDiv.remove();
    }
}

function forceShowAgents() {
    console.log('💪 强制显示AI智能体页面...');
    
    // 隐藏所有其他section
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示agents-section
    const agentsSection = document.getElementById('agents-section');
    if (agentsSection) {
        agentsSection.style.display = 'block';
        agentsSection.style.visibility = 'visible';
        agentsSection.style.opacity = '1';
        
        // 确保Bootstrap类没有冲突
        agentsSection.classList.remove('d-none', 'hidden');
        
        console.log('✅ AI智能体页面已强制显示');
        
        // 更新导航状态
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('onclick') && link.getAttribute('onclick').includes("'agents'")) {
                link.classList.add('active');
            }
        });
        
        // 显示成功提示
        if (window.uiManager) {
            window.uiManager.showNotification('✅ AI智能体页面已显示！如果还看不到内容，请检查浏览器开发者工具。', 'success');
        } else {
            alert('✅ AI智能体页面已显示！');
        }
        
        return true;
    } else {
        console.error('❌ 未找到agents-section元素');
        alert('❌ 错误：未找到AI智能体页面元素');
        return false;
    }
}

// 添加调试按钮到页面
function addDebugButton() {
    const debugButton = document.createElement('button');
    debugButton.innerHTML = '<i class="fas fa-bug"></i> 调试AI智能体';
    debugButton.className = 'btn btn-danger btn-sm position-fixed';
    debugButton.style.cssText = 'top: 10px; right: 10px; z-index: 9999;';
    debugButton.onclick = () => {
        debugAgentsSection();
        forceShowAgents();
    };
    
    document.body.appendChild(debugButton);
    console.log('🐛 调试按钮已添加到页面右上角');
}

// 页面加载后自动运行调试
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 页面加载完成，开始调试...');
    
    setTimeout(() => {
        debugAgentsSection();
        addDebugButton();
        
        // 如果用户点击了AI智能体但看不到内容，自动尝试修复
        const agentsLink = document.querySelector('[onclick*="showSection(\'agents\')"]');
        if (agentsLink) {
            const originalOnclick = agentsLink.onclick;
            agentsLink.onclick = function(e) {
                e.preventDefault();
                console.log('🖱️ 用户点击了AI智能体链接');
                
                if (originalOnclick) {
                    originalOnclick.call(this, e);
                }
                
                // 额外的强制显示逻辑
                setTimeout(() => {
                    const agentsSection = document.getElementById('agents-section');
                    if (agentsSection && window.getComputedStyle(agentsSection).display === 'none') {
                        console.log('🔧 检测到AI智能体页面未显示，自动修复...');
                        forceShowAgents();
                    }
                }, 100);
            };
        }
    }, 1000);
});

// 导出函数供外部调用
window.debugAgentsSection = debugAgentsSection;
window.forceShowAgents = forceShowAgents;