#!/usr/bin/env node

// Simple server startup script for enhanced simulation
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const bodyParser = require('body-parser');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3508;

// Middleware
app.use(cors());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static('public'));

// Data directories
const ROOT_DIR = path.resolve(__dirname, '..');
const ENHANCED_RESULTS_DIR = path.join(ROOT_DIR, 'enhanced_results');

// Ensure directories exist
fs.ensureDirSync(ENHANCED_RESULTS_DIR);

// Health check
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        version: '1.0.0' 
    });
});

// Enhanced simulation endpoint
app.post('/api/enhanced/run', async (req, res) => {
    console.log('🚀 Enhanced simulation requested');
    
    try {
        const { config } = req.body;
        
        // Python script path
        const pythonScript = path.join(ROOT_DIR, 'run_enhanced_demo_api.py');
        
        // Check if script exists
        if (!fs.existsSync(pythonScript)) {
            console.error('❌ Python script not found:', pythonScript);
            return res.status(500).json({ 
                error: 'Enhanced demo script not found',
                path: pythonScript
            });
        }
        
        // Prepare arguments
        const args = [
            '--start-year', String(config?.startYear || 1920),
            '--end-year', String(config?.endYear || 1940),
            '--migrants', String(config?.migrants || 300),
            '--families', String(config?.families || 300),
            '--institutions', String(config?.institutions || 10),
            '--output-dir', 'enhanced_results'
        ];
        
        console.log('🐍 Running Python script:', pythonScript);
        console.log('📋 Arguments:', args);
        
        // Return immediate response
        res.json({ 
            success: true, 
            message: '增强仿真已启动，正在后台运行...',
            jobId: Date.now(),
            status: 'running'
        });
        
        // Run Python script asynchronously
        const python = spawn('python', [pythonScript, ...args], {
            cwd: ROOT_DIR,
            stdio: 'pipe'
        });
        
        let output = '';
        let error = '';
        
        python.stdout.on('data', (data) => {
            const chunk = data.toString();
            output += chunk;
            console.log('📊 Python output:', chunk.trim());
        });
        
        python.stderr.on('data', (data) => {
            const chunk = data.toString();
            error += chunk;
            console.error('⚠️ Python error:', chunk.trim());
        });
        
        python.on('close', (code) => {
            if (code === 0) {
                console.log('✅ Enhanced simulation completed successfully');
            } else {
                console.error('❌ Enhanced simulation failed with code:', code);
                console.error('Error output:', error);
            }
        });
        
        python.on('error', (err) => {
            console.error('❌ Failed to start Python process:', err);
        });
        
    } catch (error) {
        console.error('❌ Enhanced simulation error:', error);
        res.status(500).json({ 
            error: 'Failed to start enhanced simulation',
            details: error.message 
        });
    }
});

// Get enhanced results
app.get('/api/enhanced/results', async (req, res) => {
    try {
        console.log('📋 Results requested for enhanced simulation');
        
        const resultsPath = path.join(ENHANCED_RESULTS_DIR, 'enhanced_analysis.json');
        const multiTsPath = path.join(ENHANCED_RESULTS_DIR, 'multi_timeseries.json');
        const vizPath = path.join(ENHANCED_RESULTS_DIR, 'visualization_data.json');
        const reportPath = path.join(ENHANCED_RESULTS_DIR, 'final_report.json');
        
        // Check if any results exist
        if (!fs.existsSync(resultsPath)) {
            console.log('📂 No enhanced results found yet');
            return res.json({ 
                available: false, 
                message: '尚未运行增强分析或分析仍在进行中' 
            });
        }
        
        console.log('✅ Loading enhanced results');
        
        const results = {};
        
        // Load each result file if it exists
        if (fs.existsSync(resultsPath)) {
            results.enhanced_analysis = await fs.readJson(resultsPath);
        }
        
        if (fs.existsSync(multiTsPath)) {
            results.multi_timeseries = await fs.readJson(multiTsPath);
        }
        
        if (fs.existsSync(vizPath)) {
            results.visualization_data = await fs.readJson(vizPath);
        }
        
        if (fs.existsSync(reportPath)) {
            results.final_report = await fs.readJson(reportPath);
        }
        
        res.json({ 
            available: true, 
            results, 
            timestamp: new Date().toISOString() 
        });
        
    } catch (error) {
        console.error('❌ Error loading enhanced results:', error);
        res.status(500).json({ 
            error: 'Failed to load enhanced results',
            details: error.message 
        });
    }
});

// Get timeseries data
app.get('/api/enhanced/timeseries', async (req, res) => {
    try {
        const { dimension, granularity } = req.query;
        
        const timeseriesPath = path.join(ENHANCED_RESULTS_DIR, 'multi_timeseries.json');
        if (!fs.existsSync(timeseriesPath)) {
            return res.status(404).json({ error: '多时间序列数据不存在' });
        }
        
        const timeseries = await fs.readJson(timeseriesPath);
        
        let responseData = timeseries;
        
        if (dimension && timeseries[dimension]) {
            responseData = { [dimension]: timeseries[dimension] };
        }
        
        res.json(responseData);
        
    } catch (error) {
        console.error('Error loading timeseries data:', error);
        res.status(500).json({ error: 'Failed to load timeseries data' });
    }
});

// Get visualization data
app.get('/api/enhanced/visualization', async (req, res) => {
    try {
        const vizPath = path.join(ENHANCED_RESULTS_DIR, 'visualization_data.json');
        if (!fs.existsSync(vizPath)) {
            return res.status(404).json({ error: '可视化数据不存在' });
        }
        
        const vizData = await fs.readJson(vizPath);
        res.json(vizData);
        
    } catch (error) {
        console.error('Error loading visualization data:', error);
        res.status(500).json({ error: 'Failed to load visualization data' });
    }
});

// Static file serving
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/enhanced-simulation.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'enhanced-simulation.html'));
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// Error handler
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Enhanced Qiaopi Simulation Server');
    console.log(`📊 Server running on port ${PORT}`);
    console.log(`🌐 Dashboard: http://localhost:${PORT}`);
    console.log(`🔬 Enhanced Simulation: http://localhost:${PORT}/enhanced-simulation.html`);
    console.log(`📡 API Health: http://localhost:${PORT}/api/health`);
    console.log('');
    console.log('🎯 Enhanced simulation functionality enabled!');
    console.log('✨ Ready to process simulation requests...');
});

module.exports = app;