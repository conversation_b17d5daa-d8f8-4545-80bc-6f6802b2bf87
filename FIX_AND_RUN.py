#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Fix and Run Script
快速修复并运行脚本
"""

import subprocess
import sys
import os
import time

def test_imports():
    """Test if basic imports work"""
    try:
        import dash
        import plotly
        print("✅ Dash and Plotly are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        return False

def run_simple_dashboard():
    """Run the simple dashboard with error handling"""
    print("\n" + "="*60)
    print("Starting Simple Dashboard...")
    print("="*60)
    
    dashboard_code = '''
import dash
from dash import dcc, html, Input, Output
import plotly.graph_objs as go
import random
import threading
import time
from datetime import datetime

app = dash.Dash(__name__)

# Simple data storage
data = {
    'time': [],
    'value1': [],
    'value2': []
}

# Data generation thread
def generate_data():
    step = 0
    while True:
        data['time'].append(step)
        data['value1'].append(random.randint(50, 150) + step * 0.5)
        data['value2'].append(random.randint(30, 80) + step * 0.3)
        
        # Keep only last 100 points
        if len(data['time']) > 100:
            for key in data:
                data[key] = data[key][-100:]
        
        step += 1
        time.sleep(1)

# Start data thread
data_thread = threading.Thread(target=generate_data, daemon=True)
data_thread.start()

# Layout
app.layout = html.Div([
    html.H1('🌏 Qiaopi Network Simulation', style={'textAlign': 'center'}),
    html.H3('侨批网络仿真 - Demo Dashboard', style={'textAlign': 'center', 'color': 'gray'}),
    html.Hr(),
    
    # Metrics
    html.Div([
        html.Div([
            html.H4('Total Agents'),
            html.H2(id='metric1', children='150')
        ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                  'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                  'textAlign': 'center'}),
        
        html.Div([
            html.H4('Active Remittances'),
            html.H2(id='metric2', children='42')
        ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                  'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                  'textAlign': 'center'}),
        
        html.Div([
            html.H4('Success Rate'),
            html.H2(id='metric3', children='85%')
        ], style={'display': 'inline-block', 'margin': '20px', 'padding': '20px',
                  'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'width': '200px',
                  'textAlign': 'center'}),
    ], style={'textAlign': 'center'}),
    
    # Chart
    dcc.Graph(id='live-graph'),
    
    # Interval for updates
    dcc.Interval(id='graph-update', interval=2000),
])

# Callback for updating graph
@app.callback(Output('live-graph', 'figure'),
              [Input('graph-update', 'n_intervals')])
def update_graph(n):
    fig = go.Figure()
    
    if data['time']:
        fig.add_trace(go.Scatter(
            x=data['time'],
            y=data['value1'],
            mode='lines',
            name='Migrant Savings',
            line=dict(color='blue', width=2)
        ))
        
        fig.add_trace(go.Scatter(
            x=data['time'],
            y=data['value2'],
            mode='lines',
            name='Family Cash',
            line=dict(color='green', width=2)
        ))
    
    fig.update_layout(
        title='Real-time Simulation Data',
        xaxis_title='Time Steps',
        yaxis_title='Value',
        hovermode='x unified'
    )
    
    return fig

# Callback for updating metrics
@app.callback([Output('metric1', 'children'),
               Output('metric2', 'children'),
               Output('metric3', 'children')],
              [Input('graph-update', 'n_intervals')])
def update_metrics(n):
    agents = 150 + random.randint(-5, 5)
    remittances = 42 + random.randint(-10, 10)
    success = 85 + random.randint(-5, 5)
    return f'{agents}', f'{remittances}', f'{success}%'

if __name__ == '__main__':
    print("Dashboard running at: http://localhost:8050")
    print("Press Ctrl+C to stop")
    app.run(debug=False, port=8050, host='0.0.0.0')
'''
    
    # Save and run
    with open('temp_dashboard.py', 'w') as f:
        f.write(dashboard_code)
    
    try:
        subprocess.run([sys.executable, 'temp_dashboard.py'])
    except KeyboardInterrupt:
        print("\n✅ Dashboard stopped")
    finally:
        # Clean up
        if os.path.exists('temp_dashboard.py'):
            os.remove('temp_dashboard.py')

def main():
    print("""
╔════════════════════════════════════════════════════════════╗
║             QIAOPI VISUALIZATION - QUICK FIX              ║
║             侨批可视化 - 快速修复                            ║
╚════════════════════════════════════════════════════════════╝
    """)
    
    # Test imports
    if not test_imports():
        print("\nInstalling required packages...")
        subprocess.run([sys.executable, "-m", "pip", "install", "dash", "plotly"])
    
    # Run dashboard
    try:
        run_simple_dashboard()
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTrying minimal fallback dashboard...")
        
        # Minimal fallback
        print("\nYou can also try:")
        print("1. python -m pip install dash plotly")
        print("2. python visualization/simple_dashboard.py")

if __name__ == "__main__":
    main()