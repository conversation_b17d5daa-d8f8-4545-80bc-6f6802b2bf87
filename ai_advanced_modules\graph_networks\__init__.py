"""
图神经网络分析模块
Graph Neural Networks Analysis Module

这个模块专门用于分析侨批网络的复杂社会关系和网络动力学，包括：
- 社会网络结构分析
- 网络传播模型
- 社区检测和演化
- 网络影响力分析
- 动态网络预测

主要组件：
- QiaopiNetworkGNN: 侨批网络图神经网络模型
- NetworkAnalyzer: 网络结构分析器
- CommunityDetector: 社区检测器
- InfluenceAnalyzer: 影响力分析器
- NetworkPredictor: 网络预测器
- DynamicGraphProcessor: 动态图处理器
"""

from .graph_models import (
    QiaopiNetworkGNN,
    TemporalGraphNetwork,
    AttentionGraphNetwork,
    GraphConfig,
    create_qiaopi_gnn
)

from .network_analyzer import (
    NetworkAnalyzer,
    NetworkMetrics
)

# 其他模块将在后续版本中实现
# from .community_detection import CommunityDetector
# from .influence_analysis import InfluenceAnalyzer
# from .network_prediction import NetworkPredictor
# from .dynamic_graphs import DynamicGraphProcessor
# from .utils import GraphDataLoader

__version__ = "1.0.0"
__author__ = "Qiaopi Research Team"

__all__ = [
    # 核心图神经网络模型
    "QiaopiNetworkGNN",
    "TemporalGraphNetwork", 
    "AttentionGraphNetwork",
    "GraphConfig",
    "create_qiaopi_gnn",
    
    # 网络分析工具
    "NetworkAnalyzer",
    "NetworkMetrics"
]
