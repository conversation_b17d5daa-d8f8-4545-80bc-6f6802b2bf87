// 测试服务器集成的脚本
const express = require('express');
const http = require('http');

// 简单测试服务器是否能启动
function testServerBasics() {
    console.log('🧪 Testing server integration...');
    
    try {
        // 尝试require server.js
        const server = require('./server.js');
        console.log('✅ server.js loads successfully');
        
        // 测试基本的API端点
        console.log('📡 Server should be running on port 3508');
        console.log('🌐 Test URLs:');
        console.log('   Health check: http://localhost:3508/api/health');
        console.log('   Enhanced page: http://localhost:3508/enhanced-simulation.html');
        console.log('   Main page: http://localhost:3508/');
        
        return true;
    } catch (error) {
        console.error('❌ Error loading server:', error);
        return false;
    }
}

// 测试Python脚本是否存在
function testPythonScripts() {
    const fs = require('fs-extra');
    const path = require('path');
    
    console.log('\n🐍 Checking Python scripts...');
    
    const ROOT_DIR = path.resolve(__dirname, '..');
    const scripts = [
        { name: 'Full Enhanced Script', path: path.join(ROOT_DIR, 'run_enhanced_demo_api.py') },
        { name: 'Simple Test Script', path: path.join(ROOT_DIR, 'test_api_simple.py') }
    ];
    
    let hasScript = false;
    
    for (const script of scripts) {
        if (fs.existsSync(script.path)) {
            console.log(`✅ ${script.name}: ${script.path}`);
            hasScript = true;
        } else {
            console.log(`❌ ${script.name}: ${script.path} (not found)`);
        }
    }
    
    return hasScript;
}

// 主测试函数
function runTests() {
    console.log('🎯 Enhanced Simulation Server Integration Test');
    console.log('='.repeat(50));
    
    const serverOk = testServerBasics();
    const scriptsOk = testPythonScripts();
    
    console.log('\n📊 Test Results:');
    console.log(`   Server Loading: ${serverOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Python Scripts: ${scriptsOk ? '✅ PASS' : '❌ FAIL'}`);
    
    if (serverOk && scriptsOk) {
        console.log('\n🎉 Integration test PASSED! Server should work correctly.');
        console.log('\n🚀 To start the server, run: node server.js');
    } else {
        console.log('\n❌ Integration test FAILED! Please check the issues above.');
    }
    
    return serverOk && scriptsOk;
}

// 如果直接运行此脚本
if (require.main === module) {
    runTests();
}

module.exports = { runTests, testServerBasics, testPythonScripts };