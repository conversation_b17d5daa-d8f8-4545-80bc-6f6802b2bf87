// 重新生成完整的演示数据（包含所有分析模块）

const http = require('http');

console.log('🔧 重新生成完整的演示数据...\n');

const options = {
    hostname: 'localhost',
    port: 3508,
    path: '/api/enhanced/generate-demo',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    }
};

const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        try {
            const result = JSON.parse(data);
            
            if (result.success) {
                console.log('✅ 演示数据生成成功！\n');
                console.log('📊 数据已包含以下分析模块：');
                console.log('  1. ✅ 多时间序列分析 (6个维度，16个子序列)');
                console.log('  2. ✅ 趋势分析 (整体趋势指标)');
                console.log('  3. ✅ 相关性分析 (5x5相关性矩阵)');
                console.log('  4. ✅ 聚类分析 (3个行为群体)');
                console.log('  5. ✅ 预测分析 (5年预测+情景分析)');
                console.log('  6. ✅ 网络拓扑分析 (网络密度、中心性等)');
                console.log('  7. ✅ 事件影响分析 (3个重大事件)');
                console.log('\n📌 现在您可以：');
                console.log('  1. 访问 http://localhost:3508/enhanced-simulation.html');
                console.log('  2. 查看分析模块表格，所有模块应显示"完成"状态');
                console.log('  3. 点击各模块的"详情"按钮查看可视化');
                console.log('\n🎨 或直接访问完整可视化页面：');
                console.log('  http://localhost:3508/analysis-visualizations.html');
            } else {
                console.error('❌ 生成失败:', result.error || '未知错误');
                console.log('\n请确保服务器正在运行:');
                console.log('  npm start');
            }
        } catch (error) {
            console.error('❌ 解析响应失败:', error);
            console.log('响应内容:', data);
        }
    });
});

req.on('error', (error) => {
    console.error('❌ 请求失败:', error.message);
    console.log('\n请确保服务器正在运行:');
    console.log('  cd "Qiaopi-agent - 副本/web-app"');
    console.log('  npm start');
});

req.end();