@echo off
REM Qiaopi Network Visualization System Setup and Run Script (Windows)
REM 侨批网络可视化系统安装运行脚本 (Windows版本)

echo ==========================================
echo Qiaopi Network Visualization System Setup
echo 侨批网络可视化系统安装
echo ==========================================

REM Check Python version
echo Checking Python version...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Create virtual environment
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    echo [OK] Virtual environment created
) else (
    echo [OK] Virtual environment exists
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip >nul 2>&1

REM Install requirements
echo Installing requirements...
echo This may take a few minutes...

REM Core requirements
pip install plotly dash dash-bootstrap-components >nul 2>&1
echo   [OK] Dashboard libraries installed

pip install pandas numpy scipy scikit-learn >nul 2>&1
echo   [OK] Data science libraries installed

pip install flask flask-socketio python-socketio >nul 2>&1
echo   [OK] Real-time streaming libraries installed

pip install networkx statsmodels >nul 2>&1
echo   [OK] Analytics libraries installed

REM Optional but recommended
pip install psutil structlog >nul 2>&1
echo   [OK] Optional libraries installed

REM Create necessary directories
echo Creating directories...
if not exist "visualization_output" mkdir visualization_output
if not exist "logs" mkdir logs
if not exist "data" mkdir data
echo [OK] Directories created

echo.
echo ==========================================
echo Setup complete!
echo ==========================================
echo.
echo Starting Qiaopi Visualization System...
echo.
echo Dashboard will open at: http://localhost:8050
echo WebSocket server at: ws://localhost:8081
echo.
echo Press Ctrl+C to stop
echo.

REM Run the visualization system
python run_visualization.py

pause