#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');

// 测试增强仿真图表功能

console.log('🔧 测试增强仿真图表功能...\n');

// 首先生成演示数据
function generateDemoData() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3508,
            path: '/api/enhanced/generate-demo',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    if (result.success) {
                        console.log('✅ 演示数据生成成功');
                        resolve(result);
                    } else {
                        console.error('❌ 生成失败:', result.error);
                        reject(new Error(result.error));
                    }
                } catch (error) {
                    console.error('❌ 解析响应失败:', error);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 请求失败:', error.message);
            reject(error);
        });

        req.end();
    });
}

// 获取时间序列数据
function getTimeseriesData(dimension) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3508,
            path: `/api/enhanced/timeseries?dimension=${dimension}`,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve(result);
                } catch (error) {
                    console.error('❌ 解析时间序列数据失败:', error);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 获取时间序列数据失败:', error.message);
            reject(error);
        });

        req.end();
    });
}

// 验证数据结构
function validateDataStructure(data, dimension) {
    console.log(`\n📊 验证 ${dimension} 维度数据:`);
    
    if (!data[dimension]) {
        console.error(`  ❌ 缺少 ${dimension} 数据`);
        return false;
    }
    
    const series = data[dimension];
    const seriesKeys = Object.keys(series);
    
    console.log(`  ✅ 找到 ${seriesKeys.length} 个子系列:`, seriesKeys.join(', '));
    
    // 验证每个系列的数据结构
    for (const key of seriesKeys) {
        const seriesData = series[key];
        if (!Array.isArray(seriesData)) {
            console.error(`  ❌ ${key} 不是数组`);
            return false;
        }
        
        if (seriesData.length === 0) {
            console.error(`  ❌ ${key} 是空数组`);
            return false;
        }
        
        // 检查第一个数据点的结构
        const firstPoint = seriesData[0];
        const requiredFields = ['year', 'flow_count', 'flow_amount', 'success_rate'];
        const missingFields = requiredFields.filter(field => !(field in firstPoint));
        
        if (missingFields.length > 0) {
            console.error(`  ❌ ${key} 缺少字段:`, missingFields.join(', '));
            return false;
        }
        
        console.log(`  ✅ ${key}: ${seriesData.length} 个数据点`);
    }
    
    return true;
}

// 主测试函数
async function runTest() {
    try {
        // 1. 生成演示数据
        console.log('步骤 1: 生成演示数据');
        await generateDemoData();
        
        // 等待一下让文件写入完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 2. 测试各个维度的数据
        const dimensions = ['overall', 'by_region', 'by_currency', 'by_institution', 'by_event', 'by_corridor'];
        
        console.log('\n步骤 2: 验证各维度数据结构');
        
        for (const dimension of dimensions) {
            const data = await getTimeseriesData(dimension);
            const isValid = validateDataStructure(data, dimension);
            
            if (!isValid) {
                console.error(`\n❌ ${dimension} 维度验证失败`);
            }
        }
        
        // 3. 检查生成的文件
        console.log('\n步骤 3: 检查生成的文件');
        const filesDir = path.join(__dirname, '..', 'enhanced_results');
        const requiredFiles = [
            'enhanced_analysis.json',
            'multi_timeseries.json',
            'visualization_data.json',
            'final_report.json'
        ];
        
        for (const file of requiredFiles) {
            const filePath = path.join(filesDir, file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                console.log(`  ✅ ${file}: ${stats.size} bytes`);
            } else {
                console.error(`  ❌ ${file}: 文件不存在`);
            }
        }
        
        console.log('\n✅ 测试完成！');
        console.log('\n📌 现在您可以:');
        console.log('1. 访问 http://localhost:3508/enhanced-simulation.html');
        console.log('2. 点击"查看现有结果"按钮');
        console.log('3. 在"多时间序列分析"部分选择不同的维度和子系列');
        console.log('4. 图表应该能正常显示数据');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        console.log('\n请确保服务器正在运行:');
        console.log('  cd web-app');
        console.log('  npm start');
    }
}

// 运行测试
runTest();