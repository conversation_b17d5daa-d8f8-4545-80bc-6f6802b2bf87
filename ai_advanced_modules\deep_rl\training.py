"""
强化学习训练管理器
Reinforcement Learning Training Manager

负责智能体的训练、评估和模型管理，包括：
- 多智能体协同训练
- 超参数优化
- 训练过程监控
- 模型版本管理
- 性能评估
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Callable
import logging
from pathlib import Path
import json
import pickle
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass, asdict
import wandb  # Weights & Biases for experiment tracking
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.utils import set_random_seed
import optuna  # 超参数优化

from .environment import QiaopiNetworkEnvironment, EnvironmentConfig
from .rl_agents import DeepRLMigrantAgent, DeepRLFamilyAgent, DeepRLInstitutionAgent

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TrainingConfig:
    """训练配置类"""
    # 基础配置
    total_timesteps: int = 1000000
    eval_freq: int = 10000
    n_eval_episodes: int = 10
    
    # 算法配置
    algorithm: str = "PPO"
    learning_rate: float = 3e-4
    batch_size: int = 64
    n_steps: int = 2048
    
    # 环境配置
    n_envs: int = 4  # 并行环境数量
    env_config: EnvironmentConfig = None
    
    # 训练策略
    curriculum_learning: bool = True
    multi_agent_training: bool = True
    self_play: bool = False
    
    # 优化配置
    use_hyperparameter_optimization: bool = False
    n_trials: int = 100
    
    # 监控和保存
    experiment_name: str = "qiaopi_rl_training"
    save_freq: int = 50000
    log_interval: int = 1000
    use_wandb: bool = True
    
    # 评估配置
    evaluation_scenarios: List[str] = None
    
    def __post_init__(self):
        if self.env_config is None:
            self.env_config = EnvironmentConfig()
        
        if self.evaluation_scenarios is None:
            self.evaluation_scenarios = [
                "normal_conditions",
                "economic_crisis", 
                "war_scenario",
                "technology_disruption"
            ]


class TrainingCallback(BaseCallback):
    """自定义训练回调"""
    
    def __init__(self, training_manager: 'TrainingManager', verbose: int = 0):
        super().__init__(verbose)
        self.training_manager = training_manager
        self.episode_rewards = []
        self.episode_lengths = []
        
    def _on_step(self) -> bool:
        # 收集训练指标
        if len(self.locals.get('infos', [])) > 0:
            for info in self.locals['infos']:
                if 'episode' in info:
                    episode_reward = info['episode']['r']
                    episode_length = info['episode']['l']
                    
                    self.episode_rewards.append(episode_reward)
                    self.episode_lengths.append(episode_length)
                    
                    # 记录到wandb
                    if self.training_manager.config.use_wandb:
                        wandb.log({
                            'episode_reward': episode_reward,
                            'episode_length': episode_length,
                            'timestep': self.num_timesteps
                        })
        
        # 定期保存模型
        if self.num_timesteps % self.training_manager.config.save_freq == 0:
            self.training_manager.save_models(f"checkpoint_{self.num_timesteps}")
        
        return True
    
    def _on_training_end(self) -> None:
        # 训练结束时的处理
        logger.info("训练完成，保存最终模型...")
        self.training_manager.save_models("final_model")


class CurriculumLearningManager:
    """课程学习管理器"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.current_level = 0
        self.levels = self._define_curriculum_levels()
        self.performance_threshold = 0.7
        
    def _define_curriculum_levels(self) -> List[Dict[str, Any]]:
        """定义课程学习等级"""
        return [
            {
                "name": "basic_operations",
                "description": "基础汇款操作",
                "env_config": {
                    "num_migrants": 10,
                    "num_families": 8,
                    "num_institutions": 2,
                    "max_steps": 100,
                    "economic_volatility": 0.05
                },
                "success_criteria": {"avg_reward": 0.5, "success_rate": 0.6}
            },
            {
                "name": "moderate_complexity",
                "description": "中等复杂度环境",
                "env_config": {
                    "num_migrants": 25,
                    "num_families": 20,
                    "num_institutions": 5,
                    "max_steps": 200,
                    "economic_volatility": 0.1
                },
                "success_criteria": {"avg_reward": 0.6, "success_rate": 0.7}
            },
            {
                "name": "high_complexity",
                "description": "高复杂度环境",
                "env_config": {
                    "num_migrants": 50,
                    "num_families": 40,
                    "num_institutions": 8,
                    "max_steps": 500,
                    "economic_volatility": 0.15
                },
                "success_criteria": {"avg_reward": 0.7, "success_rate": 0.75}
            },
            {
                "name": "full_complexity",
                "description": "完整复杂度环境",
                "env_config": {
                    "num_migrants": 100,
                    "num_families": 80,
                    "num_institutions": 10,
                    "max_steps": 1000,
                    "economic_volatility": 0.2
                },
                "success_criteria": {"avg_reward": 0.75, "success_rate": 0.8}
            }
        ]
    
    def get_current_level_config(self) -> Dict[str, Any]:
        """获取当前等级配置"""
        if self.current_level >= len(self.levels):
            return self.levels[-1]
        return self.levels[self.current_level]
    
    def should_advance(self, performance_metrics: Dict[str, float]) -> bool:
        """判断是否应该进入下一等级"""
        if self.current_level >= len(self.levels) - 1:
            return False
        
        current_level = self.levels[self.current_level]
        criteria = current_level["success_criteria"]
        
        for metric, threshold in criteria.items():
            if performance_metrics.get(metric, 0) < threshold:
                return False
        
        return True
    
    def advance_level(self):
        """进入下一等级"""
        if self.current_level < len(self.levels) - 1:
            self.current_level += 1
            logger.info(f"课程学习进入等级 {self.current_level}: {self.levels[self.current_level]['name']}")


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.study = None
        
    def create_study(self, study_name: str = None):
        """创建优化研究"""
        if study_name is None:
            study_name = f"qiaopi_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.study = optuna.create_study(
            direction="maximize",
            study_name=study_name
        )
    
    def objective(self, trial: optuna.Trial) -> float:
        """优化目标函数"""
        # 定义超参数搜索空间
        hyperparams = {
            'learning_rate': trial.suggest_loguniform('learning_rate', 1e-5, 1e-2),
            'batch_size': trial.suggest_categorical('batch_size', [32, 64, 128, 256]),
            'n_steps': trial.suggest_categorical('n_steps', [512, 1024, 2048, 4096]),
            'gamma': trial.suggest_uniform('gamma', 0.9, 0.999),
            'gae_lambda': trial.suggest_uniform('gae_lambda', 0.8, 0.99),
            'clip_range': trial.suggest_uniform('clip_range', 0.1, 0.4),
            'ent_coef': trial.suggest_loguniform('ent_coef', 1e-8, 1e-1)
        }
        
        # 使用超参数训练模型
        trainer = TrainingManager(self.config)
        trainer.config.learning_rate = hyperparams['learning_rate']
        trainer.config.batch_size = hyperparams['batch_size']
        trainer.config.n_steps = hyperparams['n_steps']
        
        # 运行简化训练
        performance = trainer.train_single_trial(
            timesteps=50000,  # 较短的训练用于优化
            hyperparams=hyperparams
        )
        
        return performance['avg_reward']
    
    def optimize(self, n_trials: int = None):
        """执行超参数优化"""
        if n_trials is None:
            n_trials = self.config.n_trials
        
        if self.study is None:
            self.create_study()
        
        logger.info(f"开始超参数优化，{n_trials} 个试验...")
        self.study.optimize(self.objective, n_trials=n_trials)
        
        best_params = self.study.best_params
        best_value = self.study.best_value
        
        logger.info(f"最佳超参数: {best_params}")
        logger.info(f"最佳性能: {best_value}")
        
        return best_params, best_value


class TrainingManager:
    """训练管理器主类"""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.models = {}
        self.training_history = []
        self.evaluation_results = {}
        
        # 创建保存目录
        self.experiment_dir = Path(f"experiments/{config.experiment_name}")
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化wandb
        if config.use_wandb:
            wandb.init(
                project="qiaopi-rl",
                name=config.experiment_name,
                config=asdict(config)
            )
        
        # 课程学习管理器
        if config.curriculum_learning:
            self.curriculum_manager = CurriculumLearningManager(config)
        
        # 超参数优化器
        if config.use_hyperparameter_optimization:
            self.hyperparameter_optimizer = HyperparameterOptimizer(config)
        
        logger.info(f"训练管理器初始化完成，实验目录: {self.experiment_dir}")
    
    def create_environment(self, env_config: EnvironmentConfig = None) -> QiaopiNetworkEnvironment:
        """创建训练环境"""
        if env_config is None:
            env_config = self.config.env_config
        
        env = QiaopiNetworkEnvironment(env_config)
        return env
    
    def create_vectorized_env(self, n_envs: int = None, env_config: EnvironmentConfig = None):
        """创建向量化环境"""
        if n_envs is None:
            n_envs = self.config.n_envs
        
        if env_config is None:
            env_config = self.config.env_config
        
        def make_env(rank: int):
            def _init():
                env = self.create_environment(env_config)
                env = Monitor(env, str(self.experiment_dir / f"monitor_{rank}"))
                return env
            set_random_seed(rank)
            return _init
        
        # 创建并行环境
        if n_envs == 1:
            env = DummyVecEnv([make_env(0)])
        else:
            env = SubprocVecEnv([make_env(i) for i in range(n_envs)])
        
        return env
    
    def initialize_agents(self, env_config: EnvironmentConfig = None):
        """初始化智能体"""
        if env_config is None:
            env_config = self.config.env_config
        
        # 创建环境用于获取空间信息
        temp_env = self.create_environment(env_config)
        
        # 初始化移民智能体
        migrant_agents = []
        for i in range(env_config.num_migrants):
            agent = DeepRLMigrantAgent(
                agent_id=f"migrant_{i}",
                learning_algorithm=self.config.algorithm
            )
            migrant_agents.append(agent)
        
        # 初始化家庭智能体
        family_agents = []
        for i in range(env_config.num_families):
            agent = DeepRLFamilyAgent(
                agent_id=f"family_{i}",
                learning_algorithm=self.config.algorithm
            )
            family_agents.append(agent)
        
        # 初始化机构智能体
        institution_agents = []
        for i in range(env_config.num_institutions):
            agent = DeepRLInstitutionAgent(
                agent_id=f"institution_{i}",
                learning_algorithm=self.config.algorithm
            )
            institution_agents.append(agent)
        
        self.models = {
            'migrants': migrant_agents,
            'families': family_agents,
            'institutions': institution_agents
        }
        
        logger.info(f"初始化了 {len(migrant_agents)} 个移民智能体")
        logger.info(f"初始化了 {len(family_agents)} 个家庭智能体")
        logger.info(f"初始化了 {len(institution_agents)} 个机构智能体")
    
    def train(self):
        """执行完整训练流程"""
        logger.info("开始训练流程...")
        
        # 超参数优化（如果启用）
        if self.config.use_hyperparameter_optimization:
            best_params, best_value = self.hyperparameter_optimizer.optimize()
            self._update_config_with_best_params(best_params)
        
        # 课程学习训练
        if self.config.curriculum_learning:
            self._train_with_curriculum()
        else:
            self._train_standard()
        
        # 最终评估
        self._final_evaluation()
        
        logger.info("训练流程完成")
    
    def _train_with_curriculum(self):
        """课程学习训练"""
        logger.info("开始课程学习训练...")
        
        while True:
            # 获取当前等级配置
            level_config = self.curriculum_manager.get_current_level_config()
            env_config = EnvironmentConfig(**level_config["env_config"])
            
            logger.info(f"训练等级: {level_config['name']} - {level_config['description']}")
            
            # 在当前等级训练
            performance = self._train_on_level(env_config, level_config)
            
            # 检查是否应该进入下一等级
            if self.curriculum_manager.should_advance(performance):
                self.curriculum_manager.advance_level()
                
                # 如果已经是最后一个等级，退出
                if self.curriculum_manager.current_level >= len(self.curriculum_manager.levels) - 1:
                    break
            else:
                # 如果没有达到标准，继续在当前等级训练
                logger.info(f"未达到进入下一等级的标准，继续在当前等级训练...")
    
    def _train_on_level(self, env_config: EnvironmentConfig, level_config: Dict) -> Dict[str, float]:
        """在特定等级训练"""
        # 创建环境
        vec_env = self.create_vectorized_env(env_config=env_config)
        
        # 初始化智能体（如果尚未初始化）
        if not self.models:
            self.initialize_agents(env_config)
        
        # 创建回调
        callback = TrainingCallback(self)
        
        # 训练步数根据等级调整
        timesteps = min(self.config.total_timesteps // len(self.curriculum_manager.levels), 200000)
        
        # 执行训练
        if self.config.multi_agent_training:
            performance = self._train_multi_agent(vec_env, timesteps, callback)
        else:
            performance = self._train_single_agent(vec_env, timesteps, callback)
        
        # 评估性能
        eval_results = self.evaluate_agents(vec_env, n_episodes=self.config.n_eval_episodes)
        
        performance.update(eval_results)
        
        return performance
    
    def _train_standard(self):
        """标准训练流程"""
        logger.info("开始标准训练...")
        
        # 创建环境
        vec_env = self.create_vectorized_env()
        
        # 初始化智能体
        self.initialize_agents()
        
        # 创建回调
        callback = TrainingCallback(self)
        
        # 执行训练
        if self.config.multi_agent_training:
            self._train_multi_agent(vec_env, self.config.total_timesteps, callback)
        else:
            self._train_single_agent(vec_env, self.config.total_timesteps, callback)
    
    def _train_multi_agent(self, vec_env, timesteps: int, callback) -> Dict[str, float]:
        """多智能体协同训练"""
        logger.info("开始多智能体协同训练...")
        
        # 这里实现多智能体训练逻辑
        # 由于Stable Baselines3主要针对单智能体，我们需要自定义训练循环
        
        performance_metrics = {}
        
        for step in range(0, timesteps, 1000):
            # 收集经验
            experiences = self._collect_multi_agent_experiences(vec_env, 1000)
            
            # 更新智能体
            for agent_type, agents in self.models.items():
                for agent in agents:
                    if hasattr(agent, 'learn_from_experience'):
                        # 从经验中学习
                        agent_experiences = experiences.get(agent.agent_id, [])
                        for exp in agent_experiences:
                            agent.learn_from_experience(
                                exp['reward'], 
                                exp['next_state'], 
                                exp['done']
                            )
            
            # 定期评估
            if step % self.config.eval_freq == 0:
                eval_results = self.evaluate_agents(vec_env, n_episodes=5)
                performance_metrics.update(eval_results)
                
                # 记录到wandb
                if self.config.use_wandb:
                    wandb.log(eval_results, step=step)
            
            # 执行回调
            if callback:
                callback.num_timesteps = step
                callback._on_step()
        
        return performance_metrics
    
    def _collect_multi_agent_experiences(self, vec_env, n_steps: int) -> Dict[str, List[Dict]]:
        """收集多智能体经验"""
        experiences = {agent.agent_id: [] for agents in self.models.values() for agent in agents}
        
        obs = vec_env.reset()
        
        for step in range(n_steps):
            # 获取所有智能体的动作
            actions = self._get_multi_agent_actions(obs)
            
            # 执行环境步骤
            next_obs, rewards, dones, infos = vec_env.step(actions)
            
            # 存储经验
            for i, info in enumerate(infos):
                if 'agent_rewards' in info:
                    for agent_id, reward in info['agent_rewards'].items():
                        experiences[agent_id].append({
                            'state': obs[i],
                            'action': actions[i],
                            'reward': reward,
                            'next_state': next_obs[i],
                            'done': dones[i]
                        })
            
            obs = next_obs
            
            # 重置环境（如果需要）
            if np.any(dones):
                obs = vec_env.reset()
        
        return experiences
    
    def _get_multi_agent_actions(self, observations) -> Dict[str, np.ndarray]:
        """获取多智能体动作"""
        actions = {
            'migrants': [],
            'families': [],
            'institutions': []
        }
        
        # 简化实现：随机动作
        for obs in observations:
            actions['migrants'].append(np.random.randint(0, 10, size=len(self.models['migrants'])))
            actions['families'].append(np.random.randint(0, 8, size=len(self.models['families'])))
            actions['institutions'].append(np.random.randint(0, 12, size=len(self.models['institutions'])))
        
        return actions
    
    def _train_single_agent(self, vec_env, timesteps: int, callback) -> Dict[str, float]:
        """单智能体训练"""
        logger.info("开始单智能体训练...")
        
        # 选择一个代表性智能体进行训练
        representative_agent = self.models['migrants'][0]
        
        if hasattr(representative_agent.model, 'learn'):
            representative_agent.model.set_env(vec_env)
            representative_agent.model.learn(
                total_timesteps=timesteps,
                callback=callback,
                log_interval=self.config.log_interval
            )
        
        return {'training_completed': True}
    
    def evaluate_agents(self, vec_env, n_episodes: int = 10) -> Dict[str, float]:
        """评估智能体性能"""
        logger.info(f"评估智能体性能，{n_episodes} 个回合...")
        
        episode_rewards = []
        episode_lengths = []
        success_rates = []
        
        for episode in range(n_episodes):
            obs = vec_env.reset()
            episode_reward = 0
            episode_length = 0
            
            while True:
                # 获取动作
                actions = self._get_multi_agent_actions(obs)
                
                # 执行步骤
                obs, rewards, dones, infos = vec_env.step(actions)
                
                episode_reward += np.mean(rewards)
                episode_length += 1
                
                if np.all(dones):
                    break
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            
            # 计算成功率
            success_rate = sum(1 for info in infos if info.get('success', False)) / len(infos)
            success_rates.append(success_rate)
        
        results = {
            'avg_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'avg_length': np.mean(episode_lengths),
            'success_rate': np.mean(success_rates)
        }
        
        logger.info(f"评估结果: {results}")
        return results
    
    def train_single_trial(self, timesteps: int, hyperparams: Dict[str, Any]) -> Dict[str, float]:
        """单次试验训练（用于超参数优化）"""
        # 创建临时环境
        env_config = EnvironmentConfig(max_steps=100, num_migrants=10, num_families=8, num_institutions=2)
        vec_env = self.create_vectorized_env(n_envs=1, env_config=env_config)
        
        # 初始化临时智能体
        temp_agent = DeepRLMigrantAgent("temp_migrant", learning_algorithm=self.config.algorithm)
        
        # 应用超参数
        if hasattr(temp_agent.model, 'learning_rate'):
            temp_agent.model.learning_rate = hyperparams['learning_rate']
        
        # 快速训练
        if hasattr(temp_agent.model, 'learn'):
            temp_agent.model.set_env(vec_env)
            temp_agent.model.learn(total_timesteps=timesteps, log_interval=None)
        
        # 评估
        performance = self.evaluate_agents(vec_env, n_episodes=3)
        
        return performance
    
    def _final_evaluation(self):
        """最终评估"""
        logger.info("进行最终评估...")
        
        # 在不同场景下评估
        for scenario in self.config.evaluation_scenarios:
            env_config = self._get_scenario_config(scenario)
            vec_env = self.create_vectorized_env(n_envs=1, env_config=env_config)
            
            results = self.evaluate_agents(vec_env, n_episodes=20)
            self.evaluation_results[scenario] = results
            
            logger.info(f"场景 {scenario} 评估结果: {results}")
    
    def _get_scenario_config(self, scenario: str) -> EnvironmentConfig:
        """获取特定场景的环境配置"""
        base_config = self.config.env_config
        
        if scenario == "economic_crisis":
            base_config.economic_volatility = 0.3
            base_config.political_stability_base = 0.5
        elif scenario == "war_scenario":
            base_config.political_stability_base = 0.2
            base_config.communication_cost_base = 0.15
        elif scenario == "technology_disruption":
            base_config.communication_cost_base = 0.01
        
        return base_config
    
    def save_models(self, checkpoint_name: str):
        """保存模型"""
        checkpoint_dir = self.experiment_dir / "checkpoints" / checkpoint_name
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存智能体模型
        for agent_type, agents in self.models.items():
            for i, agent in enumerate(agents):
                if hasattr(agent, 'save_model'):
                    model_path = checkpoint_dir / f"{agent_type}_{i}.zip"
                    agent.save_model(str(model_path))
        
        # 保存训练配置
        config_path = checkpoint_dir / "config.json"
        with open(config_path, 'w') as f:
            json.dump(asdict(self.config), f, indent=2)
        
        # 保存训练历史
        history_path = checkpoint_dir / "training_history.pkl"
        with open(history_path, 'wb') as f:
            pickle.dump(self.training_history, f)
        
        logger.info(f"模型已保存到 {checkpoint_dir}")
    
    def load_models(self, checkpoint_name: str):
        """加载模型"""
        checkpoint_dir = self.experiment_dir / "checkpoints" / checkpoint_name
        
        if not checkpoint_dir.exists():
            logger.error(f"检查点目录不存在: {checkpoint_dir}")
            return
        
        # 加载智能体模型
        for agent_type, agents in self.models.items():
            for i, agent in enumerate(agents):
                model_path = checkpoint_dir / f"{agent_type}_{i}.zip"
                if model_path.exists() and hasattr(agent, 'load_model'):
                    agent.load_model(str(model_path))
        
        # 加载训练历史
        history_path = checkpoint_dir / "training_history.pkl"
        if history_path.exists():
            with open(history_path, 'rb') as f:
                self.training_history = pickle.load(f)
        
        logger.info(f"模型已从 {checkpoint_dir} 加载")
    
    def generate_training_report(self) -> Dict[str, Any]:
        """生成训练报告"""
        report = {
            'experiment_name': self.config.experiment_name,
            'training_config': asdict(self.config),
            'training_history': self.training_history,
            'evaluation_results': self.evaluation_results,
            'model_statistics': self._get_model_statistics(),
            'performance_summary': self._get_performance_summary()
        }
        
        # 保存报告
        report_path = self.experiment_dir / "training_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"训练报告已保存到 {report_path}")
        return report
    
    def _get_model_statistics(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        stats = {}
        
        for agent_type, agents in self.models.items():
            stats[agent_type] = []
            for agent in agents:
                if hasattr(agent, 'get_statistics'):
                    stats[agent_type].append(agent.get_statistics())
        
        return stats
    
    def _get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.evaluation_results:
            return {}
        
        summary = {}
        for scenario, results in self.evaluation_results.items():
            summary[scenario] = {
                'avg_reward': results.get('avg_reward', 0),
                'success_rate': results.get('success_rate', 0)
            }
        
        return summary
    
    def _update_config_with_best_params(self, best_params: Dict[str, Any]):
        """使用最佳超参数更新配置"""
        for param, value in best_params.items():
            if hasattr(self.config, param):
                setattr(self.config, param, value)
        
        logger.info(f"配置已更新为最佳超参数: {best_params}")
    
    def visualize_training_progress(self):
        """可视化训练进度"""
        if not self.training_history:
            logger.warning("没有训练历史数据可供可视化")
            return
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'训练进度 - {self.config.experiment_name}')
        
        # 奖励曲线
        if 'rewards' in self.training_history[0]:
            rewards = [h['rewards'] for h in self.training_history]
            axes[0, 0].plot(rewards)
            axes[0, 0].set_title('训练奖励')
            axes[0, 0].set_xlabel('回合')
            axes[0, 0].set_ylabel('奖励')
        
        # 成功率曲线
        if 'success_rate' in self.training_history[0]:
            success_rates = [h['success_rate'] for h in self.training_history]
            axes[0, 1].plot(success_rates)
            axes[0, 1].set_title('成功率')
            axes[0, 1].set_xlabel('回合')
            axes[0, 1].set_ylabel('成功率')
        
        # 评估结果比较
        if self.evaluation_results:
            scenarios = list(self.evaluation_results.keys())
            avg_rewards = [self.evaluation_results[s]['avg_reward'] for s in scenarios]
            
            axes[1, 0].bar(scenarios, avg_rewards)
            axes[1, 0].set_title('不同场景下的平均奖励')
            axes[1, 0].set_ylabel('平均奖励')
            plt.setp(axes[1, 0].xaxis.get_majorticklabels(), rotation=45)
        
        # 模型性能分布
        if self.models:
            # 简化示例：显示智能体数量分布
            agent_counts = [len(agents) for agents in self.models.values()]
            agent_types = list(self.models.keys())
            
            axes[1, 1].pie(agent_counts, labels=agent_types, autopct='%1.1f%%')
            axes[1, 1].set_title('智能体类型分布')
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = self.experiment_dir / "training_progress.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        logger.info(f"训练进度图表已保存到 {plot_path}")


# 使用示例
def main():
    """主函数示例"""
    # 创建训练配置
    env_config = EnvironmentConfig(
        max_steps=500,
        num_migrants=50,
        num_families=40,
        num_institutions=8
    )
    
    training_config = TrainingConfig(
        total_timesteps=500000,
        algorithm="PPO",
        env_config=env_config,
        curriculum_learning=True,
        multi_agent_training=True,
        use_hyperparameter_optimization=False,
        experiment_name="qiaopi_advanced_training"
    )
    
    # 创建训练管理器
    trainer = TrainingManager(training_config)
    
    # 执行训练
    trainer.train()
    
    # 生成报告
    report = trainer.generate_training_report()
    
    # 可视化结果
    trainer.visualize_training_progress()
    
    print("训练完成！")


if __name__ == "__main__":
    main()
