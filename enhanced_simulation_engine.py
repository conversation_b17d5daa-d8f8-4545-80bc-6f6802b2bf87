#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版侨批网络仿真引擎
Enhanced Qiaopi Network Simulation Engine
支持多时间序列生成和高级分析
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any, Union
import random
import json
import csv
import numpy as np
from datetime import datetime
import os
import math
import statistics
from collections import defaultdict, Counter
from concurrent.futures import ThreadPoolExecutor
import threading

# 导入基础模块
from simulation_engine import SimulationConfig, QiaopiSimulationEngine
from agents import MigrantAgent, FamilyAgent, InstitutionAgent, QiaoxiangRegion, Location, EconomicStatus, Occupation
from environment import Environment, EventType


@dataclass
class EnhancedAnalysisConfig:
    """增强分析配置"""
    # 时间序列分析
    enable_multi_timeseries: bool = True
    timeseries_granularities: List[str] = field(default_factory=lambda: ['monthly', 'quarterly', 'yearly'])
    
    # 多维度分析
    enable_geographical_analysis: bool = True
    enable_currency_analysis: bool = True
    enable_network_analysis: bool = True
    enable_event_analysis: bool = True
    
    # 高级分析
    enable_trend_analysis: bool = True
    enable_correlation_analysis: bool = True
    enable_clustering_analysis: bool = True
    enable_prediction_analysis: bool = True
    
    # 对比分析
    enable_scenario_comparison: bool = True
    comparison_scenarios: List[Dict[str, Any]] = field(default_factory=list)
    
    # 输出控制
    generate_detailed_reports: bool = True
    export_visualization_data: bool = True


class EnhancedQiaopiSimulationEngine(QiaopiSimulationEngine):
    """增强版侨批仿真引擎"""
    
    def __init__(self, config: SimulationConfig, analysis_config: EnhancedAnalysisConfig = None):
        super().__init__(config)
        self.analysis_config = analysis_config or EnhancedAnalysisConfig()
        
        # 多维度时间序列存储
        self.multi_timeseries: Dict[str, List[Dict[str, Any]]] = {
            'overall': [],
            'by_region': {},
            'by_currency': {},
            'by_institution': {},
            'by_event': {},
            'by_corridor': {}
        }
        
        # 高级分析缓存
        self.trend_analysis_cache: Dict[str, Any] = {}
        self.correlation_cache: Dict[str, Any] = {}
        self.clustering_cache: Dict[str, Any] = {}
        
        # 预测模型
        self.prediction_models: Dict[str, Any] = {}
        
    def run_enhanced_simulation(self) -> Dict[str, Any]:
        """运行增强版仿真"""
        print(f"🚀 开始增强版仿真：{self.config.start_year}-{self.config.end_year}")
        print(f"📊 启用的分析功能: {sum([
            self.analysis_config.enable_multi_timeseries,
            self.analysis_config.enable_geographical_analysis,
            self.analysis_config.enable_currency_analysis,
            self.analysis_config.enable_network_analysis,
            self.analysis_config.enable_event_analysis,
            self.analysis_config.enable_trend_analysis,
            self.analysis_config.enable_correlation_analysis,
            self.analysis_config.enable_clustering_analysis,
            self.analysis_config.enable_prediction_analysis
        ])} 项")
        
        # 运行基础仿真
        results = self.run_simulation()
        
        # 增强分析
        enhanced_results = self._generate_enhanced_analysis()
        results.update(enhanced_results)
        
        # 保存增强结果
        self._save_enhanced_results(results)
        
        return results
    
    def _run_single_step(self):
        """重写单步运行，添加多维度统计"""
        # 调用父类方法
        super()._run_single_step()
        
        # 增强统计收集
        if self.analysis_config.enable_multi_timeseries:
            self._collect_multi_dimensional_stats()
    
    def _collect_multi_dimensional_stats(self):
        """收集多维度统计数据"""
        current_stats = self._calculate_current_statistics()
        
        # 1. 整体时间序列
        self.multi_timeseries['overall'].append({
            'step': self.current_step,
            'year': self.environment.get_current_year(),
            **current_stats
        })
        
        # 2. 按地区分组统计
        if self.analysis_config.enable_geographical_analysis:
            self._collect_regional_stats()
        
        # 3. 按货币分组统计
        if self.analysis_config.enable_currency_analysis:
            self._collect_currency_stats()
        
        # 4. 按机构分组统计
        self._collect_institution_stats()
        
        # 5. 按事件分组统计
        if self.analysis_config.enable_event_analysis:
            self._collect_event_stats()
        
        # 6. 按通道分组统计
        self._collect_corridor_stats()
    
    def _collect_regional_stats(self):
        """收集地区维度统计"""
        for region in Location:
            region_key = region.value
            if region_key not in self.multi_timeseries['by_region']:
                self.multi_timeseries['by_region'][region_key] = []
            
            # 该地区的移民统计
            region_migrants = [m for m in self.migrants.values() if m.location == region]
            region_qiaopi = [q for q in self.qiaopi_history if q.origin == region]
            
            regional_stats = {
                'step': self.current_step,
                'year': self.environment.get_current_year(),
                'migrant_count': len(region_migrants),
                'total_savings': sum(m.savings for m in region_migrants),
                'avg_income': sum(m.income_level for m in region_migrants) / max(1, len(region_migrants)),
                'qiaopi_sent': len([q for q in region_qiaopi if q.send_time == self.current_step]),
                'qiaopi_delivered': len([q for q in region_qiaopi if q.delivery_time == self.current_step]),
                'economic_factor': self.environment.economic_factors.get(region, 1.0),
                'remittance_volume': sum(q.amount for q in region_qiaopi if q.send_time == self.current_step)
            }
            
            self.multi_timeseries['by_region'][region_key].append(regional_stats)
    
    def _collect_currency_stats(self):
        """收集货币维度统计"""
        currencies = set()
        for q in self.qiaopi_history:
            currency = getattr(q, 'currency_code', 'HKD')
            currencies.add(currency)
        
        for currency in currencies:
            if currency not in self.multi_timeseries['by_currency']:
                self.multi_timeseries['by_currency'][currency] = []
            
            # 该货币的侨批统计
            currency_qiaopi = [q for q in self.qiaopi_history 
                             if getattr(q, 'currency_code', 'HKD') == currency]
            current_step_qiaopi = [q for q in currency_qiaopi if q.send_time == self.current_step]
            
            currency_stats = {
                'step': self.current_step,
                'year': self.environment.get_current_year(),
                'qiaopi_count': len(current_step_qiaopi),
                'total_amount': sum(q.amount for q in current_step_qiaopi),
                'avg_amount': sum(q.amount for q in current_step_qiaopi) / max(1, len(current_step_qiaopi)),
                'exchange_rate': getattr(current_step_qiaopi[0], 'exchange_rate', 1.0) if current_step_qiaopi else 1.0,
                'success_rate': len([q for q in current_step_qiaopi if q.delivery_time is not None]) / max(1, len(current_step_qiaopi))
            }
            
            self.multi_timeseries['by_currency'][currency].append(currency_stats)
    
    def _collect_institution_stats(self):
        """收集机构维度统计"""
        for inst_id, institution in self.institutions.items():
            if inst_id not in self.multi_timeseries['by_institution']:
                self.multi_timeseries['by_institution'][inst_id] = []
            
            # 该机构的侨批统计
            inst_qiaopi = [q for q in self.qiaopi_history if q.institution_id == inst_id]
            current_step_qiaopi = [q for q in inst_qiaopi if q.send_time == self.current_step]
            
            inst_stats = {
                'step': self.current_step,
                'year': self.environment.get_current_year(),
                'processed_count': len(current_step_qiaopi),
                'success_rate': institution.successful_deliveries / max(1, institution.total_deliveries),
                'liquidity': institution.liquidity,
                'trust_rating': institution.trust_rating,
                'total_fees': sum(q.transaction_fee for q in inst_qiaopi if q.delivery_time is not None),
                'home_base': institution.home_base.value,
                'network_reach_size': len(institution.network_reach)
            }
            
            self.multi_timeseries['by_institution'][inst_id].append(inst_stats)
    
    def _collect_event_stats(self):
        """收集事件维度统计"""
        active_events = self.environment.get_active_events()
        
        for event in active_events:
            event_key = f"{event.event_type.value}_{event.start_time}"
            if event_key not in self.multi_timeseries['by_event']:
                self.multi_timeseries['by_event'][event_key] = []
            
            # 事件期间的侨批统计
            event_qiaopi = [q for q in self.qiaopi_history 
                           if event.start_time <= q.send_time < event.start_time + event.duration]
            current_step_qiaopi = [q for q in event_qiaopi if q.send_time == self.current_step]
            
            event_stats = {
                'step': self.current_step,
                'year': self.environment.get_current_year(),
                'event_type': event.event_type.value,
                'event_intensity': event.intensity,
                'affected_regions': [r.value for r in event.affected_regions],
                'qiaopi_count': len(current_step_qiaopi),
                'avg_amount': sum(q.amount for q in current_step_qiaopi) / max(1, len(current_step_qiaopi)),
                'success_rate': len([q for q in current_step_qiaopi if q.delivery_time is not None]) / max(1, len(current_step_qiaopi))
            }
            
            self.multi_timeseries['by_event'][event_key].append(event_stats)
    
    def _collect_corridor_stats(self):
        """收集通道维度统计"""
        # 统计主要通道
        corridors = set()
        for q in self.qiaopi_history:
            corridor = f"{q.origin.value}→{q.destination.value}"
            corridors.add(corridor)
        
        for corridor in corridors:
            if corridor not in self.multi_timeseries['by_corridor']:
                self.multi_timeseries['by_corridor'][corridor] = []
            
            origin_str, dest_str = corridor.split('→')
            corridor_qiaopi = [q for q in self.qiaopi_history 
                             if q.origin.value == origin_str and q.destination.value == dest_str]
            current_step_qiaopi = [q for q in corridor_qiaopi if q.send_time == self.current_step]
            
            corridor_stats = {
                'step': self.current_step,
                'year': self.environment.get_current_year(),
                'origin': origin_str,
                'destination': dest_str,
                'flow_count': len(current_step_qiaopi),
                'flow_amount': sum(q.amount for q in current_step_qiaopi),
                'success_rate': len([q for q in current_step_qiaopi if q.delivery_time is not None]) / max(1, len(current_step_qiaopi)),
                'avg_delay': np.mean([q.delivery_time - q.send_time for q in current_step_qiaopi if q.delivery_time is not None]) if current_step_qiaopi else 0
            }
            
            self.multi_timeseries['by_corridor'][corridor].append(corridor_stats)
    
    def _generate_enhanced_analysis(self) -> Dict[str, Any]:
        """生成增强分析报告"""
        enhanced_analysis = {
            'multi_timeseries_analysis': {},
            'trend_analysis': {},
            'correlation_analysis': {},
            'clustering_analysis': {},
            'prediction_analysis': {},
            'network_topology_analysis': {},
            'event_impact_analysis': {},
            'scenario_comparison': {}
        }
        
        # 1. 多时间序列分析
        if self.analysis_config.enable_multi_timeseries:
            enhanced_analysis['multi_timeseries_analysis'] = self._analyze_multi_timeseries()
        
        # 2. 趋势分析
        if self.analysis_config.enable_trend_analysis:
            enhanced_analysis['trend_analysis'] = self._analyze_trends()
        
        # 3. 相关性分析
        if self.analysis_config.enable_correlation_analysis:
            enhanced_analysis['correlation_analysis'] = self._analyze_correlations()
        
        # 4. 聚类分析
        if self.analysis_config.enable_clustering_analysis:
            enhanced_analysis['clustering_analysis'] = self._analyze_clustering()
        
        # 5. 预测分析
        if self.analysis_config.enable_prediction_analysis:
            enhanced_analysis['prediction_analysis'] = self._analyze_predictions()
        
        # 6. 网络拓扑分析
        if self.analysis_config.enable_network_analysis:
            enhanced_analysis['network_topology_analysis'] = self._analyze_network_topology()
        
        # 7. 事件影响分析
        enhanced_analysis['event_impact_analysis'] = self._analyze_event_impact()
        
        # 8. 场景对比分析
        if self.analysis_config.enable_scenario_comparison:
            enhanced_analysis['scenario_comparison'] = self._analyze_scenario_comparison()
        
        return enhanced_analysis
    
    def _analyze_multi_timeseries(self) -> Dict[str, Any]:
        """分析多时间序列"""
        analysis = {}
        
        # 整体时间序列统计
        overall_ts = self.multi_timeseries['overall']
        if overall_ts:
            analysis['overall_series'] = {
                'length': len(overall_ts),
                'time_range': (overall_ts[0]['year'], overall_ts[-1]['year']),
                'key_metrics_trends': self._extract_metric_trends(overall_ts, [
                    'total_migrant_savings', 'average_migrant_income', 
                    'qiaopi_statistics.success_rate', 'qiaopi_statistics.total_amount_remitted'
                ])
            }
        
        # 地区时间序列
        analysis['regional_series'] = {}
        for region, series in self.multi_timeseries['by_region'].items():
            if series:
                analysis['regional_series'][region] = {
                    'series_length': len(series),
                    'peak_activity_year': max(series, key=lambda x: x['flow_count'])['year'] if series else None,
                    'economic_resilience': self._calculate_resilience_score(series, 'economic_factor'),
                    'remittance_volatility': self._calculate_volatility(series, 'remittance_volume')
                }
        
        # 货币时间序列
        analysis['currency_series'] = {}
        for currency, series in self.multi_timeseries['by_currency'].items():
            if series:
                analysis['currency_series'][currency] = {
                    'adoption_timeline': self._analyze_adoption_timeline(series),
                    'exchange_rate_volatility': self._calculate_volatility(series, 'exchange_rate'),
                    'market_share_trend': self._calculate_market_share_trend(series)
                }
        
        return analysis
    
    def _analyze_trends(self) -> Dict[str, Any]:
        """趋势分析"""
        trends = {}
        
        # 基于整体时间序列的趋势检测
        overall_ts = self.multi_timeseries['overall']
        if len(overall_ts) > 10:  # 足够的数据点
            trends['overall_trends'] = {
                'savings_trend': self._detect_trend([d['total_migrant_savings'] for d in overall_ts]),
                'income_trend': self._detect_trend([d['average_migrant_income'] for d in overall_ts]),
                'success_rate_trend': self._detect_trend([d['qiaopi_statistics']['success_rate'] for d in overall_ts]),
                'volume_trend': self._detect_trend([d['qiaopi_statistics']['total_amount_remitted'] for d in overall_ts])
            }
        
        # 地区趋势对比
        trends['regional_trends'] = {}
        for region, series in self.multi_timeseries['by_region'].items():
            if len(series) > 5:
                trends['regional_trends'][region] = {
                    'activity_trend': self._detect_trend([d['flow_count'] for d in series]),
                    'volume_trend': self._detect_trend([d['flow_amount'] for d in series]),
                    'efficiency_trend': self._detect_trend([d['success_rate'] for d in series])
                }
        
        # 季节性分析
        trends['seasonality'] = self._analyze_seasonality()
        
        # 周期性检测
        trends['cyclical_patterns'] = self._detect_cyclical_patterns()
        
        return trends
    
    def _analyze_correlations(self) -> Dict[str, Any]:
        """相关性分析"""
        correlations = {}
        
        # 整体指标相关性
        overall_ts = self.multi_timeseries['overall']
        if len(overall_ts) > 3:
            # 提取关键指标
            savings = [d['total_migrant_savings'] for d in overall_ts]
            income = [d['average_migrant_income'] for d in overall_ts]
            success_rate = [d['qiaopi_statistics']['success_rate'] for d in overall_ts]
            volume = [d['qiaopi_statistics']['total_amount_remitted'] for d in overall_ts]
            
            correlations['key_metrics'] = {
                'savings_income': self._pearson(savings, income),
                'income_success_rate': self._pearson(income, success_rate),
                'success_rate_volume': self._pearson(success_rate, volume),
                'savings_volume': self._pearson(savings, volume)
            }
        
        # 地区间相关性
        correlations['inter_regional'] = self._analyze_regional_correlations()
        
        # 事件与指标相关性
        correlations['event_impact'] = self._analyze_event_correlations()
        
        return correlations
    
    def _analyze_clustering(self) -> Dict[str, Any]:
        """聚类分析"""
        clustering = {}
        
        # 移民行为聚类
        migrant_features = []
        migrant_ids = []
        for migrant in self.migrants.values():
            features = [
                migrant.savings,
                migrant.income_level,
                migrant.years_abroad,
                migrant.social_capital,
                migrant.obligation_level,
                len([q for q in self.qiaopi_history if q.sender_id == migrant.agent_id])
            ]
            migrant_features.append(features)
            migrant_ids.append(migrant.agent_id)
        
        if migrant_features:
            migrant_clusters = self._simple_kmeans(migrant_features, k=5)
            clustering['migrant_behavior_clusters'] = {
                'cluster_assignments': {mid: cluster for mid, cluster in zip(migrant_ids, migrant_clusters)},
                'cluster_profiles': self._analyze_cluster_profiles(migrant_features, migrant_clusters)
            }
        
        # 网络聚类
        clustering['network_clusters'] = self._analyze_network_clusters()
        
        # 地区聚类
        clustering['regional_clusters'] = self._analyze_regional_clusters()
        
        return clustering
    
    def _analyze_predictions(self) -> Dict[str, Any]:
        """预测分析"""
        predictions = {}
        
        # 简单线性趋势外推
        overall_ts = self.multi_timeseries['overall']
        if len(overall_ts) > 10:
            # 预测未来5年的主要指标
            years = [d['year'] for d in overall_ts]
            savings = [d['total_migrant_savings'] for d in overall_ts]
            income = [d['average_migrant_income'] for d in overall_ts]
            
            future_years = [max(years) + i for i in range(1, 6)]
            
            predictions['5_year_forecast'] = {
                'future_years': future_years,
                'predicted_savings': self._linear_extrapolate(years, savings, future_years),
                'predicted_income': self._linear_extrapolate(years, income, future_years),
                'confidence_level': 0.7  # 简化置信度
            }
        
        # 情景预测
        predictions['scenario_forecasts'] = {
            'optimistic': self._generate_scenario_prediction('optimistic'),
            'baseline': self._generate_scenario_prediction('baseline'), 
            'pessimistic': self._generate_scenario_prediction('pessimistic')
        }
        
        return predictions
    
    def _analyze_network_topology(self) -> Dict[str, Any]:
        """网络拓扑分析"""
        topology = {}
        
        # 网络密度分析
        total_possible_connections = len(self.migrants) * (len(self.migrants) - 1)
        actual_connections = sum(len(members) * (len(members) - 1) for members in self.kinship_networks.values())
        
        topology['network_density'] = actual_connections / max(1, total_possible_connections)
        
        # 中心性分析
        topology['centrality_analysis'] = self._analyze_network_centrality()
        
        # 社群检测
        topology['community_detection'] = self._detect_communities()
        
        # 网络演化
        topology['network_evolution'] = self._analyze_network_evolution()
        
        return topology
    
    def _analyze_event_impact(self) -> Dict[str, Any]:
        """事件影响分析"""
        impact_analysis = {}
        
        # 各类事件的深度影响分析
        event_types = [EventType.WAR, EventType.ECONOMIC_RECESSION, EventType.NATURAL_DISASTER, 
                      EventType.POLICY_CHANGE, EventType.ECONOMIC_BOOM]
        
        for event_type in event_types:
            impact_analysis[event_type.value] = self._analyze_single_event_impact(event_type)
        
        # 事件组合效应
        impact_analysis['compound_effects'] = self._analyze_compound_event_effects()
        
        # 恢复力分析
        impact_analysis['resilience_metrics'] = self._analyze_system_resilience()
        
        return impact_analysis
    
    def _analyze_scenario_comparison(self) -> Dict[str, Any]:
        """场景对比分析"""
        # 如果没有预设场景，创建对比场景
        if not self.analysis_config.comparison_scenarios:
            return {'message': '没有配置对比场景'}
        
        comparison = {}
        
        # 运行对比场景
        for i, scenario_config in enumerate(self.analysis_config.comparison_scenarios):
            scenario_name = f"scenario_{i+1}"
            print(f"运行对比场景: {scenario_name}")
            
            # 这里简化为配置对比，实际应该运行独立仿真
            comparison[scenario_name] = {
                'config_diff': scenario_config,
                'expected_impact': self._estimate_scenario_impact(scenario_config)
            }
        
        return comparison
    
    # ===== 辅助分析方法 =====
    
    def _extract_metric_trends(self, timeseries: List[Dict], metrics: List[str]) -> Dict[str, Any]:
        """提取指标趋势"""
        trends = {}
        for metric in metrics:
            values = []
            for data_point in timeseries:
                try:
                    # 支持嵌套键访问，如 'qiaopi_statistics.success_rate'
                    if '.' in metric:
                        keys = metric.split('.')
                        value = data_point
                        for key in keys:
                            value = value[key]
                    else:
                        value = data_point[metric]
                    values.append(value)
                except (KeyError, TypeError):
                    continue
            
            if values:
                trends[metric] = {
                    'trend_direction': self._detect_trend(values),
                    'volatility': self._calculate_volatility(values),
                    'growth_rate': (values[-1] - values[0]) / max(abs(values[0]), 1e-10) if len(values) > 1 else 0
                }
        
        return trends
    
    def _detect_trend(self, values: List[float]) -> str:
        """检测趋势方向"""
        if len(values) < 3:
            return 'insufficient_data'
        
        # 简单线性回归斜率
        n = len(values)
        x = list(range(n))
        slope = (n * sum(i * v for i, v in zip(x, values)) - sum(x) * sum(values)) / (n * sum(i**2 for i in x) - sum(x)**2)
        
        if slope > 0.01:
            return 'increasing'
        elif slope < -0.01:
            return 'decreasing'
        else:
            return 'stable'
    
    def _calculate_volatility(self, values: List[float], metric_name: str = None) -> float:
        """计算波动率"""
        if len(values) < 2:
            return 0.0
        
        if isinstance(values[0], dict) and metric_name:
            values = [d.get(metric_name, 0) for d in values]
        
        returns = [(values[i] - values[i-1]) / max(abs(values[i-1]), 1e-10) for i in range(1, len(values))]
        return statistics.stdev(returns) if len(returns) > 1 else 0.0
    
    def _calculate_resilience_score(self, series: List[Dict], metric: str) -> float:
        """计算弹性分数"""
        values = [d.get(metric, 0) for d in series]
        if len(values) < 3:
            return 0.5
        
        # 计算从最低点恢复的能力
        min_value = min(values)
        min_index = values.index(min_value)
        
        if min_index == len(values) - 1:
            return 0.3  # 最后还是最低点，弹性较差
        
        recovery_values = values[min_index+1:]
        recovery_rate = (max(recovery_values) - min_value) / max(abs(min_value), 1e-10) if recovery_values else 0
        
        return min(1.0, max(0.0, 0.5 + recovery_rate * 0.5))
    
    def _analyze_adoption_timeline(self, series: List[Dict]) -> Dict[str, Any]:
        """分析货币采用时间线"""
        if not series:
            return {}
        
        total_usage = [d['qiaopi_count'] for d in series]
        peak_index = total_usage.index(max(total_usage)) if total_usage else 0
        
        return {
            'adoption_start': series[0]['year'] if series else None,
            'peak_usage_year': series[peak_index]['year'] if series else None,
            'adoption_speed': self._calculate_adoption_speed(series),
            'usage_stability': 1 - self._calculate_volatility([d['qiaopi_count'] for d in series])
        }
    
    def _calculate_adoption_speed(self, series: List[Dict]) -> float:
        """计算采用速度"""
        if len(series) < 3:
            return 0.0
        
        usage = [d['qiaopi_count'] for d in series]
        max_usage = max(usage)
        
        # 找到达到50%最大使用量的时间
        half_max = max_usage * 0.5
        for i, val in enumerate(usage):
            if val >= half_max:
                return (i / len(series))  # 归一化的时间
        
        return 1.0  # 缓慢采用
    
    def _calculate_market_share_trend(self, series: List[Dict]) -> str:
        """计算市场份额趋势"""
        if len(series) < 3:
            return 'stable'
        
        # 简化：基于使用量变化
        usage_trend = self._detect_trend([d['qiaopi_count'] for d in series])
        return usage_trend
    
    def _analyze_regional_correlations(self) -> Dict[str, float]:
        """分析地区间相关性"""
        correlations = {}
        
        regions = list(self.multi_timeseries['by_region'].keys())
        for i, region1 in enumerate(regions):
            for region2 in regions[i+1:]:
                series1 = self.multi_timeseries['by_region'][region1]
                series2 = self.multi_timeseries['by_region'][region2]
                
                if len(series1) == len(series2) and len(series1) > 3:
                    values1 = [d['remittance_volume'] for d in series1]
                    values2 = [d['remittance_volume'] for d in series2]
                    
                    corr = self._pearson(values1, values2)
                    correlations[f"{region1}_{region2}"] = corr
        
        return correlations
    
    def _analyze_event_correlations(self) -> Dict[str, Any]:
        """分析事件与指标的相关性"""
        event_correlations = {}
        
        # 为每种事件类型分析
        for event_type in EventType:
            # 找到该类型事件的时间区间
            event_periods = []
            for event in self.environment.historical_events:
                if event.event_type == event_type:
                    event_periods.append((event.start_time, event.start_time + event.duration))
            
            if event_periods:
                # 分析事件期间与非事件期间的指标差异
                event_correlations[event_type.value] = self._compare_event_periods(event_periods)
        
        return event_correlations
    
    def _simple_kmeans(self, features: List[List[float]], k: int = 3) -> List[int]:
        """简单K-means聚类"""
        if len(features) < k:
            return list(range(len(features)))
        
        # 标准化特征
        features_array = np.array(features)
        features_normalized = (features_array - features_array.mean(axis=0)) / (features_array.std(axis=0) + 1e-10)
        
        # 随机初始化中心点
        centroids = features_normalized[np.random.choice(len(features_normalized), k, replace=False)]
        
        # 迭代聚类
        for _ in range(10):  # 最多10次迭代
            # 分配点到最近的中心
            distances = np.sqrt(((features_normalized[:, None, :] - centroids[None, :, :])**2).sum(axis=2))
            labels = np.argmin(distances, axis=1)
            
            # 更新中心点
            new_centroids = np.array([features_normalized[labels == i].mean(axis=0) if np.any(labels == i) 
                                    else centroids[i] for i in range(k)])
            
            # 检查收敛
            if np.allclose(centroids, new_centroids, atol=1e-4):
                break
                
            centroids = new_centroids
        
        return labels.tolist()
    
    def _analyze_cluster_profiles(self, features: List[List[float]], labels: List[int]) -> Dict[str, Any]:
        """分析聚类特征"""
        profiles = {}
        
        features_array = np.array(features)
        feature_names = ['savings', 'income', 'years_abroad', 'social_capital', 'obligation', 'qiaopi_sent']
        
        for cluster_id in set(labels):
            cluster_mask = np.array(labels) == cluster_id
            cluster_features = features_array[cluster_mask]
            
            if len(cluster_features) > 0:
                profiles[f'cluster_{cluster_id}'] = {
                    'size': len(cluster_features),
                    'characteristics': {
                        name: {
                            'mean': float(cluster_features[:, i].mean()),
                            'std': float(cluster_features[:, i].std())
                        } for i, name in enumerate(feature_names)
                    },
                    'description': self._generate_cluster_description(cluster_features, feature_names)
                }
        
        return profiles
    
    def _generate_cluster_description(self, cluster_features: np.ndarray, feature_names: List[str]) -> str:
        """生成聚类描述"""
        means = cluster_features.mean(axis=0)
        
        # 找到最显著的特征
        normalized_means = (means - means.mean()) / (means.std() + 1e-10)
        top_features = np.argsort(np.abs(normalized_means))[-2:]  # 前两个最显著特征
        
        descriptions = []
        for idx in top_features:
            feature_name = feature_names[idx]
            value = normalized_means[idx]
            if value > 0:
                descriptions.append(f"高{feature_name}")
            else:
                descriptions.append(f"低{feature_name}")
        
        return "、".join(descriptions) + "群体"
    
    def _linear_extrapolate(self, x: List[float], y: List[float], x_new: List[float]) -> List[float]:
        """线性外推"""
        if len(x) < 2 or len(y) < 2:
            return [y[-1] if y else 0] * len(x_new)
        
        # 简单线性回归
        n = len(x)
        slope = (n * sum(xi * yi for xi, yi in zip(x, y)) - sum(x) * sum(y)) / (n * sum(xi**2 for xi in x) - sum(x)**2)
        intercept = (sum(y) - slope * sum(x)) / n
        
        return [slope * xi + intercept for xi in x_new]
    
    def _analyze_seasonality(self) -> Dict[str, Any]:
        """分析季节性模式"""
        seasonality = {}
        
        # 按月份统计
        monthly_stats = defaultdict(list)
        for data_point in self.multi_timeseries['overall']:
            month = int((data_point['step'] % self.config.steps_per_year) + 1)
            monthly_stats[month].append(data_point['qiaopi_statistics']['total_amount_remitted'])
        
        # 计算月度平均值
        monthly_averages = {month: np.mean(values) for month, values in monthly_stats.items() if values}
        
        seasonality['monthly_patterns'] = monthly_averages
        seasonality['peak_month'] = max(monthly_averages.items(), key=lambda x: x[1])[0] if monthly_averages else None
        seasonality['trough_month'] = min(monthly_averages.items(), key=lambda x: x[1])[0] if monthly_averages else None
        
        # 季节性强度
        if monthly_averages:
            avg_all = np.mean(list(monthly_averages.values()))
            seasonality['seasonality_strength'] = np.std(list(monthly_averages.values())) / max(avg_all, 1e-10)
        
        return seasonality
    
    def _detect_cyclical_patterns(self) -> Dict[str, Any]:
        """检测周期性模式"""
        cyclical = {}
        
        overall_ts = self.multi_timeseries['overall']
        if len(overall_ts) > 24:  # 需要足够的数据点
            # 简化的周期检测：计算自相关
            values = [d['qiaopi_statistics']['total_amount_remitted'] for d in overall_ts]
            autocorrelations = []
            
            for lag in range(1, min(len(values)//4, 12)):
                corr = self._pearson(values[:-lag], values[lag:])
                autocorrelations.append((lag, corr or 0))
            
            # 找到最强的周期性
            if autocorrelations:
                best_lag, best_corr = max(autocorrelations, key=lambda x: abs(x[1]))
                cyclical['dominant_cycle'] = {
                    'period_steps': best_lag,
                    'period_years': best_lag / self.config.steps_per_year,
                    'strength': abs(best_corr)
                }
        
        return cyclical
    
    def _analyze_network_centrality(self) -> Dict[str, Any]:
        """分析网络中心性"""
        centrality = {}
        
        # 简化的度中心性分析
        agent_connections = defaultdict(int)
        for network_members in self.kinship_networks.values():
            for agent_id in network_members:
                agent_connections[agent_id] = len(network_members) - 1  # 连接数
        
        if agent_connections:
            max_connections = max(agent_connections.values())
            centrality['top_nodes'] = [
                {'agent_id': agent_id, 'connections': connections, 'centrality': connections/max_connections}
                for agent_id, connections in sorted(agent_connections.items(), key=lambda x: x[1], reverse=True)[:10]
            ]
        
        return centrality
    
    def _detect_communities(self) -> Dict[str, Any]:
        """检测社群"""
        communities = {}
        
        # 基于地理和经济状态的社群检测
        geo_communities = defaultdict(list)
        econ_communities = defaultdict(list)
        
        for migrant in self.migrants.values():
            geo_communities[migrant.location.value].append(migrant.agent_id)
        
        for family in self.families.values():
            econ_communities[family.economic_status.value].append(family.agent_id)
        
        communities['geographical'] = dict(geo_communities)
        communities['economic'] = dict(econ_communities)
        
        return communities
    
    def _analyze_network_evolution(self) -> Dict[str, Any]:
        """分析网络演化"""
        evolution = {}
        
        # 网络规模随时间变化
        network_sizes_over_time = []
        for ts_point in self.multi_timeseries['overall']:
            step = ts_point['step']
            # 简化：假设网络规模基本稳定，但活跃度变化
            active_networks = ts_point.get('active_kinship_networks', len(self.kinship_networks))
            network_sizes_over_time.append(active_networks)
        
        if network_sizes_over_time:
            evolution['network_growth'] = {
                'initial_size': network_sizes_over_time[0],
                'final_size': network_sizes_over_time[-1],
                'growth_rate': (network_sizes_over_time[-1] - network_sizes_over_time[0]) / max(network_sizes_over_time[0], 1),
                'stability': 1 - self._calculate_volatility(network_sizes_over_time)
            }
        
        return evolution
    
    def _analyze_single_event_impact(self, event_type: EventType) -> Dict[str, Any]:
        """分析单一事件类型的影响"""
        impact = {}
        
        # 找到该类型的所有事件
        events = [e for e in self.environment.historical_events if e.event_type == event_type]
        if not events:
            return {'message': f'没有{event_type.value}事件'}
        
        # 分析事件前后的指标变化
        impact['event_count'] = len(events)
        impact['total_affected_periods'] = sum(e.duration for e in events)
        
        # 受影响的侨批数量和金额
        affected_qiaopi = []
        for event in events:
            event_qiaopi = [q for q in self.qiaopi_history 
                           if event.start_time <= q.send_time < event.start_time + event.duration]
            affected_qiaopi.extend(event_qiaopi)
        
        if affected_qiaopi:
            impact['affected_remittances'] = {
                'count': len(affected_qiaopi),
                'total_amount': sum(q.amount for q in affected_qiaopi),
                'avg_amount': sum(q.amount for q in affected_qiaopi) / len(affected_qiaopi),
                'success_rate': len([q for q in affected_qiaopi if q.delivery_time is not None]) / len(affected_qiaopi)
            }
        
        return impact
    
    def _analyze_compound_event_effects(self) -> Dict[str, Any]:
        """分析复合事件效应"""
        compound_effects = {}
        
        # 找到同时发生的事件组合
        time_events = defaultdict(list)
        for event in self.environment.historical_events:
            for step in range(event.start_time, event.start_time + event.duration):
                time_events[step].append(event.event_type.value)
        
        # 统计事件组合的影响
        combination_stats = defaultdict(list)
        for step, event_types in time_events.items():
            if len(event_types) > 1:  # 多个事件同时发生
                combination = "+".join(sorted(event_types))
                step_qiaopi = [q for q in self.qiaopi_history if q.send_time == step]
                if step_qiaopi:
                    avg_amount = sum(q.amount for q in step_qiaopi) / len(step_qiaopi)
                    success_rate = len([q for q in step_qiaopi if q.delivery_time is not None]) / len(step_qiaopi)
                    combination_stats[combination].append({'amount': avg_amount, 'success': success_rate})
        
        # 汇总复合效应
        for combination, stats_list in combination_stats.items():
            if stats_list:
                compound_effects[combination] = {
                    'occurrence_count': len(stats_list),
                    'avg_impact_on_amount': np.mean([s['amount'] for s in stats_list]),
                    'avg_impact_on_success': np.mean([s['success'] for s in stats_list])
                }
        
        return compound_effects
    
    def _analyze_system_resilience(self) -> Dict[str, Any]:
        """分析系统韧性"""
        resilience = {}
        
        # 计算整体系统在不同冲击下的恢复能力
        overall_ts = self.multi_timeseries['overall']
        if len(overall_ts) > 10:
            success_rates = [d['qiaopi_statistics']['success_rate'] for d in overall_ts]
            volumes = [d['qiaopi_statistics']['total_amount_remitted'] for d in overall_ts]
            
            resilience['success_rate_resilience'] = self._calculate_resilience_metric(success_rates)
            resilience['volume_resilience'] = self._calculate_resilience_metric(volumes)
            
            # 系统稳定性
            resilience['overall_stability'] = {
                'success_rate_volatility': self._calculate_volatility(success_rates),
                'volume_volatility': self._calculate_volatility(volumes),
                'stability_score': 1 - (self._calculate_volatility(success_rates) + self._calculate_volatility(volumes)) / 2
            }
        
        return resilience
    
    def _calculate_resilience_metric(self, values: List[float]) -> float:
        """计算韧性指标"""
        if len(values) < 5:
            return 0.5
        
        # 识别显著下降和恢复
        min_val = min(values)
        max_val = max(values)
        min_idx = values.index(min_val)
        
        # 计算恢复速度
        if min_idx < len(values) - 2:
            recovery_values = values[min_idx+1:]
            recovery_speed = (max(recovery_values) - min_val) / max(abs(min_val), 1e-10) if recovery_values else 0
            return min(1.0, max(0.0, recovery_speed))
        
        return 0.5
    
    def _compare_event_periods(self, event_periods: List[Tuple[int, int]]) -> Dict[str, Any]:
        """比较事件期间与非事件期间"""
        comparison = {}
        
        # 事件期间的侨批
        event_qiaopi = []
        for start, end in event_periods:
            period_qiaopi = [q for q in self.qiaopi_history if start <= q.send_time < end]
            event_qiaopi.extend(period_qiaopi)
        
        # 非事件期间的侨批
        non_event_qiaopi = []
        for q in self.qiaopi_history:
            in_event = any(start <= q.send_time < end for start, end in event_periods)
            if not in_event:
                non_event_qiaopi.append(q)
        
        # 统计对比
        if event_qiaopi and non_event_qiaopi:
            comparison['during_events'] = {
                'count': len(event_qiaopi),
                'avg_amount': sum(q.amount for q in event_qiaopi) / len(event_qiaopi),
                'success_rate': len([q for q in event_qiaopi if q.delivery_time is not None]) / len(event_qiaopi)
            }
            
            comparison['outside_events'] = {
                'count': len(non_event_qiaopi),
                'avg_amount': sum(q.amount for q in non_event_qiaopi) / len(non_event_qiaopi),
                'success_rate': len([q for q in non_event_qiaopi if q.delivery_time is not None]) / len(non_event_qiaopi)
            }
            
            # 计算影响程度
            amount_impact = (comparison['during_events']['avg_amount'] - comparison['outside_events']['avg_amount']) / comparison['outside_events']['avg_amount']
            success_impact = (comparison['during_events']['success_rate'] - comparison['outside_events']['success_rate']) / comparison['outside_events']['success_rate']
            
            comparison['impact_metrics'] = {
                'amount_change_ratio': amount_impact,
                'success_rate_change_ratio': success_impact,
                'overall_disruption_score': (abs(amount_impact) + abs(success_impact)) / 2
            }
        
        return comparison
    
    def _analyze_network_clusters(self) -> Dict[str, Any]:
        """分析网络聚类"""
        # 基于网络规模和表现的聚类
        network_features = []
        network_ids = []
        
        for network_id, member_ids in self.kinship_networks.items():
            network_migrants = [self.migrants[mid] for mid in member_ids if mid in self.migrants]
            if network_migrants:
                avg_savings = sum(m.savings for m in network_migrants) / len(network_migrants)
                avg_income = sum(m.income_level for m in network_migrants) / len(network_migrants)
                network_qiaopi = [q for q in self.qiaopi_history if q.sender_id in member_ids]
                
                features = [
                    len(member_ids),  # 网络规模
                    avg_savings,      # 平均储蓄
                    avg_income,       # 平均收入
                    len(network_qiaopi),  # 侨批数量
                    len([q for q in network_qiaopi if q.delivery_time is not None]) / max(1, len(network_qiaopi))  # 成功率
                ]
                
                network_features.append(features)
                network_ids.append(network_id)
        
        if network_features:
            clusters = self._simple_kmeans(network_features, k=3)
            return {
                'cluster_assignments': {nid: cluster for nid, cluster in zip(network_ids, clusters)},
                'cluster_characteristics': self._analyze_network_cluster_profiles(network_features, clusters)
            }
        
        return {}
    
    def _analyze_network_cluster_profiles(self, features: List[List[float]], labels: List[int]) -> Dict[str, Any]:
        """分析网络聚类特征"""
        profiles = {}
        feature_names = ['size', 'avg_savings', 'avg_income', 'qiaopi_count', 'success_rate']
        
        features_array = np.array(features)
        
        for cluster_id in set(labels):
            cluster_mask = np.array(labels) == cluster_id
            cluster_features = features_array[cluster_mask]
            
            if len(cluster_features) > 0:
                profiles[f'network_cluster_{cluster_id}'] = {
                    'count': len(cluster_features),
                    'profile': {
                        name: {
                            'mean': float(cluster_features[:, i].mean()),
                            'std': float(cluster_features[:, i].std())
                        } for i, name in enumerate(feature_names)
                    }
                }
        
        return profiles
    
    def _analyze_regional_clusters(self) -> Dict[str, Any]:
        """分析地区聚类"""
        regional_clusters = {}
        
        # 基于地区表现特征的聚类
        regional_features = []
        region_names = []
        
        for region, series in self.multi_timeseries['by_region'].items():
            if len(series) > 5:
                # 提取地区特征
                total_volume = sum(d['flow_amount'] for d in series)
                avg_success_rate = np.mean([d['success_rate'] for d in series])
                volatility = self._calculate_volatility([d['flow_amount'] for d in series])
                growth = (series[-1]['flow_amount'] - series[0]['flow_amount']) / max(series[0]['flow_amount'], 1e-10)
                
                features = [total_volume, avg_success_rate, volatility, growth]
                regional_features.append(features)
                region_names.append(region)
        
        if regional_features:
            clusters = self._simple_kmeans(regional_features, k=3)
            regional_clusters = {
                'cluster_assignments': {region: cluster for region, cluster in zip(region_names, clusters)},
                'cluster_descriptions': self._describe_regional_clusters(regional_features, clusters, region_names)
            }
        
        return regional_clusters
    
    def _describe_regional_clusters(self, features: List[List[float]], labels: List[int], region_names: List[str]) -> Dict[str, Any]:
        """描述地区聚类"""
        descriptions = {}
        feature_names = ['total_volume', 'avg_success_rate', 'volatility', 'growth']
        
        for cluster_id in set(labels):
            cluster_regions = [region_names[i] for i, label in enumerate(labels) if label == cluster_id]
            cluster_features = np.array([features[i] for i, label in enumerate(labels) if label == cluster_id])
            
            if len(cluster_features) > 0:
                descriptions[f'cluster_{cluster_id}'] = {
                    'regions': cluster_regions,
                    'characteristics': {
                        name: float(cluster_features[:, i].mean()) 
                        for i, name in enumerate(feature_names)
                    },
                    'cluster_type': self._classify_regional_cluster(cluster_features.mean(axis=0))
                }
        
        return descriptions
    
    def _classify_regional_cluster(self, mean_features: np.ndarray) -> str:
        """分类地区聚类类型"""
        volume, success_rate, volatility, growth = mean_features
        
        if volume > 1000 and success_rate > 0.8:
            return "核心贸易区"
        elif growth > 0.5:
            return "新兴增长区"
        elif volatility < 0.2 and success_rate > 0.7:
            return "稳定服务区"
        else:
            return "边缘区域"
    
    def _generate_scenario_prediction(self, scenario_type: str) -> Dict[str, Any]:
        """生成情景预测"""
        prediction = {}
        
        # 基于历史数据的简单外推
        overall_ts = self.multi_timeseries['overall']
        if len(overall_ts) > 5:
            current_volume = overall_ts[-1]['qiaopi_statistics']['total_amount_remitted']
            current_success = overall_ts[-1]['qiaopi_statistics']['success_rate']
            
            # 不同情景的调整因子
            scenarios = {
                'optimistic': {'volume_factor': 1.5, 'success_factor': 1.2},
                'baseline': {'volume_factor': 1.1, 'success_factor': 1.05},
                'pessimistic': {'volume_factor': 0.8, 'success_factor': 0.9}
            }
            
            factors = scenarios.get(scenario_type, scenarios['baseline'])
            
            prediction = {
                'scenario_type': scenario_type,
                'predicted_volume': current_volume * factors['volume_factor'],
                'predicted_success_rate': min(1.0, current_success * factors['success_factor']),
                'confidence_intervals': {
                    'volume_lower': current_volume * factors['volume_factor'] * 0.8,
                    'volume_upper': current_volume * factors['volume_factor'] * 1.2,
                    'success_lower': max(0.0, current_success * factors['success_factor'] * 0.9),
                    'success_upper': min(1.0, current_success * factors['success_factor'] * 1.1)
                }
            }
        
        return prediction
    
    def _estimate_scenario_impact(self, scenario_config: Dict[str, Any]) -> Dict[str, Any]:
        """估算场景影响"""
        # 简化的影响估算
        impact = {
            'expected_changes': {},
            'risk_assessment': 'medium',
            'recommended_actions': []
        }
        
        # 根据配置变化估算影响
        if 'num_migrants' in scenario_config:
            change_ratio = scenario_config['num_migrants'] / self.config.num_migrants
            impact['expected_changes']['remittance_volume'] = f"{(change_ratio - 1) * 100:.1f}%"
        
        if 'start_year' in scenario_config:
            year_diff = scenario_config['start_year'] - self.config.start_year
            impact['expected_changes']['timeline_shift'] = f"{year_diff} 年"
        
        return impact
    
    def _save_enhanced_results(self, results: Dict[str, Any]):
        """保存增强分析结果"""
        # 保存多时间序列数据
        timeseries_path = f"{self.config.output_directory}/multi_timeseries.json"
        with open(timeseries_path, 'w', encoding='utf-8') as f:
            json.dump(self.multi_timeseries, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存增强分析报告
        enhanced_path = f"{self.config.output_directory}/enhanced_analysis.json"
        with open(enhanced_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 生成可视化数据
        if self.analysis_config.export_visualization_data:
            self._export_visualization_data()
    
    def _export_visualization_data(self):
        """导出可视化数据"""
        viz_data = {
            'timeseries_charts': self._prepare_timeseries_charts(),
            'heatmaps': self._prepare_heatmap_data(),
            'network_graphs': self._prepare_network_graph_data(),
            'correlation_matrices': self._prepare_correlation_matrices()
        }
        
        viz_path = f"{self.config.output_directory}/visualization_data.json"
        with open(viz_path, 'w', encoding='utf-8') as f:
            json.dump(viz_data, f, ensure_ascii=False, indent=2, default=str)
    
    def _prepare_timeseries_charts(self) -> Dict[str, Any]:
        """准备时间序列图表数据"""
        charts = {}
        
        # 整体趋势图
        overall_ts = self.multi_timeseries['overall']
        if overall_ts:
            charts['overall_trends'] = {
                'x_axis': [d['year'] for d in overall_ts],
                'series': {
                    'total_savings': [d['total_migrant_savings'] for d in overall_ts],
                    'avg_income': [d['average_migrant_income'] for d in overall_ts],
                    'success_rate': [d['qiaopi_statistics']['success_rate'] for d in overall_ts],
                    'remittance_volume': [d['qiaopi_statistics']['total_amount_remitted'] for d in overall_ts]
                }
            }
        
        # 地区对比图
        charts['regional_comparison'] = {}
        for region, series in self.multi_timeseries['by_region'].items():
            if series:
                charts['regional_comparison'][region] = {
                    'x_axis': [d['year'] for d in series],
                    'flow_volume': [d['remittance_volume'] for d in series],
                    'success_rate': [d['success_rate'] for d in series]
                }
        
        return charts
    
    def _prepare_heatmap_data(self) -> Dict[str, Any]:
        """准备热力图数据"""
        heatmaps = {}
        
        # 地区-时间热力图
        regions = list(self.multi_timeseries['by_region'].keys())
        if regions:
            time_points = sorted(set(d['year'] for series in self.multi_timeseries['by_region'].values() for d in series))
            
            volume_matrix = []
            for year in time_points:
                year_row = []
                for region in regions:
                    series = self.multi_timeseries['by_region'][region]
                    year_data = next((d for d in series if d['year'] == year), None)
                    volume = year_data['remittance_volume'] if year_data else 0
                    year_row.append(volume)
                volume_matrix.append(year_row)
            
            heatmaps['region_time_volume'] = {
                'rows': time_points,
                'columns': regions,
                'data': volume_matrix
            }
        
        return heatmaps
    
    def _prepare_network_graph_data(self) -> Dict[str, Any]:
        """准备网络图数据"""
        graph_data = {}
        
        # 构建节点和边
        nodes = []
        edges = []
        
        # 添加智能体节点
        for migrant in self.migrants.values():
            nodes.append({
                'id': migrant.agent_id,
                'type': 'migrant',
                'size': migrant.savings / 10,  # 按储蓄调整节点大小
                'location': migrant.location.value,
                'network_id': migrant.kinship_network_id
            })
        
        # 添加网络内连接
        for network_id, member_ids in self.kinship_networks.items():
            network_migrants = [mid for mid in member_ids if mid in self.migrants]
            for i, agent1 in enumerate(network_migrants):
                for agent2 in network_migrants[i+1:]:
                    edges.append({
                        'source': agent1,
                        'target': agent2,
                        'type': 'kinship',
                        'weight': 1
                    })
        
        graph_data['network_graph'] = {
            'nodes': nodes[:100],  # 限制节点数量
            'edges': edges[:200]   # 限制边数量
        }
        
        return graph_data
    
    def _prepare_correlation_matrices(self) -> Dict[str, Any]:
        """准备相关性矩阵数据"""
        matrices = {}
        
        # 地区间相关性矩阵
        regions = list(self.multi_timeseries['by_region'].keys())
        if len(regions) > 1:
            corr_matrix = []
            for region1 in regions:
                row = []
                for region2 in regions:
                    if region1 == region2:
                        row.append(1.0)
                    else:
                        series1 = self.multi_timeseries['by_region'][region1]
                        series2 = self.multi_timeseries['by_region'][region2]
                        
                        if len(series1) == len(series2) and len(series1) > 3:
                            values1 = [d['remittance_volume'] for d in series1]
                            values2 = [d['remittance_volume'] for d in series2]
                            corr = self._pearson(values1, values2) or 0
                        else:
                            corr = 0
                        row.append(corr)
                corr_matrix.append(row)
            
            matrices['regional_correlation'] = {
                'labels': regions,
                'matrix': corr_matrix
            }
        
        return matrices
    
    def run_multi_scenario_analysis(self, scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """运行多场景对比分析"""
        print("🔬 开始多场景对比分析...")
        
        scenario_results = {}
        
        for i, scenario_params in enumerate(scenarios):
            scenario_name = f"scenario_{i+1}"
            print(f"   运行场景 {scenario_name}: {scenario_params.get('description', '未命名场景')}")
            
            # 创建场景特定的配置
            scenario_config = SimulationConfig(**scenario_params)
            
            # 运行场景仿真
            scenario_engine = EnhancedQiaopiSimulationEngine(scenario_config, self.analysis_config)
            scenario_result = scenario_engine.run_enhanced_simulation()
            
            scenario_results[scenario_name] = {
                'config': scenario_params,
                'results': scenario_result
            }
        
        # 生成对比分析
        comparison_analysis = self._compare_scenario_results(scenario_results)
        
        return {
            'scenarios': scenario_results,
            'comparison': comparison_analysis
        }
    
    def _compare_scenario_results(self, scenario_results: Dict[str, Any]) -> Dict[str, Any]:
        """对比场景结果"""
        comparison = {}
        
        # 提取关键指标进行对比
        scenario_names = list(scenario_results.keys())
        key_metrics = ['total_migrant_savings', 'qiaopi_statistics.success_rate', 'qiaopi_statistics.total_amount_remitted']
        
        for metric in key_metrics:
            metric_values = {}
            for scenario_name in scenario_names:
                try:
                    result = scenario_results[scenario_name]['results']['final_statistics']
                    if '.' in metric:
                        keys = metric.split('.')
                        value = result
                        for key in keys:
                            value = value[key]
                    else:
                        value = result[metric]
                    metric_values[scenario_name] = value
                except (KeyError, TypeError):
                    metric_values[scenario_name] = 0
            
            comparison[metric] = {
                'values': metric_values,
                'best_scenario': max(metric_values.items(), key=lambda x: x[1])[0] if metric_values else None,
                'worst_scenario': min(metric_values.items(), key=lambda x: x[1])[0] if metric_values else None,
                'variance': statistics.variance(list(metric_values.values())) if len(metric_values) > 1 else 0
            }
        
        return comparison


# 预设场景配置
PRESET_SCENARIOS = [
    {
        'description': '标准场景 - 中等规模网络',
        'start_year': 1920,
        'end_year': 1940,
        'num_migrants': 300,
        'num_families': 300,
        'num_institutions': 10,
        'output_directory': 'results_standard'
    },
    {
        'description': '大规模场景 - 扩展网络',
        'start_year': 1910,
        'end_year': 1950,
        'num_migrants': 800,
        'num_families': 600,
        'num_institutions': 25,
        'output_directory': 'results_large_scale'
    },
    {
        'description': '历史危机场景 - 1930年代重点',
        'start_year': 1925,
        'end_year': 1945,
        'num_migrants': 400,
        'num_families': 350,
        'num_institutions': 12,
        'migration_probability_per_step': 0.02,
        'output_directory': 'results_crisis_period'
    },
    {
        'description': '制度优化场景 - 高信任机构',
        'start_year': 1920,
        'end_year': 1935,
        'num_migrants': 300,
        'num_families': 300,
        'num_institutions': 15,
        'initial_institution_liquidity_range': (2000.0, 8000.0),
        'output_directory': 'results_optimized_institutions'
    }
]


def create_enhanced_simulation_demo():
    """创建增强仿真演示"""
    print("🚀 增强版侨批网络仿真演示")
    print("="*60)
    
    # 配置增强分析
    analysis_config = EnhancedAnalysisConfig(
        enable_multi_timeseries=True,
        enable_geographical_analysis=True,
        enable_currency_analysis=True,
        enable_network_analysis=True,
        enable_event_analysis=True,
        enable_trend_analysis=True,
        enable_correlation_analysis=True,
        enable_clustering_analysis=True,
        enable_prediction_analysis=True,
        generate_detailed_reports=True,
        export_visualization_data=True
    )
    
    # 创建标准仿真配置
    sim_config = SimulationConfig(
        start_year=1920,
        end_year=1940,
        num_migrants=300,
        num_families=300,
        num_institutions=12,
        output_directory="enhanced_results",
        enable_personalization=True
    )
    
    # 运行增强仿真
    engine = EnhancedQiaopiSimulationEngine(sim_config, analysis_config)
    results = engine.run_enhanced_simulation()
    
    # 运行多场景对比
    print("\n🔍 运行多场景对比分析...")
    scenario_comparison = engine.run_multi_scenario_analysis(PRESET_SCENARIOS[:2])  # 运行前2个场景
    
    # 合并结果
    results['multi_scenario_analysis'] = scenario_comparison
    
    print(f"\n✅ 增强仿真完成！结果保存在: {sim_config.output_directory}/")
    
    return results


if __name__ == "__main__":
    # 运行演示
    results = create_enhanced_simulation_demo()
    
    print("\n📊 增强分析结果摘要:")
    print(f"  📈 时间序列数量: {len(results.get('multi_timeseries_analysis', {}).get('regional_series', {}))}")
    print(f"  🔍 聚类分析: {len(results.get('clustering_analysis', {}).get('migrant_behavior_clusters', {}).get('cluster_profiles', {}))}")
    print(f"  📉 趋势检测: {len(results.get('trend_analysis', {}).get('regional_trends', {}))}")
    print(f"  🌐 网络分析: 完成")
    print(f"  🎯 预测分析: 完成")