// 简单测试脚本 - 生成演示数据

const fs = require('fs').promises;
const path = require('path');

async function createDemoData() {
    console.log('📊 手动创建演示数据...\n');
    
    const enhancedResultsDir = path.join(__dirname, '..', 'enhanced_results');
    
    // 确保目录存在
    try {
        await fs.mkdir(enhancedResultsDir, { recursive: true });
    } catch (err) {
        console.log('目录已存在或创建失败:', err.message);
    }
    
    // 创建多时间序列数据
    const multiTimeseries = {
        "overall": {
            "总体趋势": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 180 + i * 8,
                flow_amount: 1800 + i * 80,
                success_rate: 0.83 + Math.random() * 0.1,
                total_savings: 10000 + i * 500,
                avg_income: 50 + i * 2
            }))
        },
        "by_region": {
            "广东": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 100 + i * 5,
                flow_amount: 1000 + i * 50,
                success_rate: 0.85 + Math.random() * 0.1,
                total_savings: 5000 + i * 250,
                avg_income: 55 + i * 2
            })),
            "福建": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 80 + i * 3,
                flow_amount: 800 + i * 30,
                success_rate: 0.80 + Math.random() * 0.15,
                total_savings: 4000 + i * 200,
                avg_income: 45 + i * 1.5
            }))
        },
        "by_currency": {
            "美元": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 120 + i * 6,
                flow_amount: 1200 + i * 60,
                success_rate: 0.90 + Math.random() * 0.08,
                total_savings: 6000 + i * 300,
                avg_income: 60 + i * 2.5
            })),
            "人民币": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 90 + i * 4,
                flow_amount: 900 + i * 40,
                success_rate: 0.85 + Math.random() * 0.12,
                total_savings: 4500 + i * 220,
                avg_income: 45 + i * 1.8
            }))
        },
        "by_institution": {
            "汇丰银行": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 50 + i * 2.5,
                flow_amount: 500 + i * 25,
                success_rate: 0.92 + Math.random() * 0.05,
                total_savings: 2500 + i * 125,
                avg_income: 65 + i * 2.8
            }))
        },
        "by_event": {
            "经济繁荣期": Array.from({length: 8}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 200 + i * 10,
                flow_amount: 2000 + i * 100,
                success_rate: 0.90 + Math.random() * 0.05,
                total_savings: 8000 + i * 400,
                avg_income: 70 + i * 3
            }))
        },
        "by_corridor": {
            "东南亚-广东": Array.from({length: 21}, (_, i) => ({
                year: 1920 + i,
                step: i,
                flow_count: 70 + i * 3.5,
                flow_amount: 700 + i * 35,
                success_rate: 0.86 + Math.random() * 0.08,
                total_savings: 3500 + i * 175,
                avg_income: 52 + i * 2.1
            }))
        }
    };
    
    // 保存文件
    const multiTimeseriesPath = path.join(enhancedResultsDir, 'multi_timeseries.json');
    await fs.writeFile(multiTimeseriesPath, JSON.stringify(multiTimeseries, null, 2));
    console.log('✅ 创建 multi_timeseries.json');
    
    // 创建增强分析文件
    const enhancedAnalysis = {
        "multi_timeseries_analysis": multiTimeseries,
        "trend_analysis": {
            "overall_trends": {
                "remittance_volume": "increasing",
                "success_rate": "stable"
            }
        },
        "clustering_analysis": {
            "migrant_behavior_clusters": {
                "cluster_profiles": {
                    "conservative": { "size": 120 },
                    "moderate": { "size": 100 },
                    "aggressive": { "size": 80 }
                }
            }
        },
        "prediction_analysis": {
            "5_year_forecast": {
                "predicted_savings": [15000, 16500, 18000, 19500, 21000]
            }
        },
        "network_topology_analysis": {
            "network_density": 0.45
        }
    };
    
    const enhancedAnalysisPath = path.join(enhancedResultsDir, 'enhanced_analysis.json');
    await fs.writeFile(enhancedAnalysisPath, JSON.stringify(enhancedAnalysis, null, 2));
    console.log('✅ 创建 enhanced_analysis.json');
    
    // 创建最终报告
    const finalReport = {
        "generated_at": new Date().toISOString(),
        "enhanced_analysis": enhancedAnalysis
    };
    
    const finalReportPath = path.join(enhancedResultsDir, 'final_report.json');
    await fs.writeFile(finalReportPath, JSON.stringify(finalReport, null, 2));
    console.log('✅ 创建 final_report.json');
    
    console.log('\n✅ 演示数据创建完成！');
    console.log('\n📌 现在您可以:');
    console.log('1. 启动服务器: npm start');
    console.log('2. 访问 http://localhost:3508/enhanced-simulation.html');
    console.log('3. 点击"查看现有结果"按钮');
}

createDemoData().catch(console.error);